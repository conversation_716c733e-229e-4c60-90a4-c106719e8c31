<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.xylink.sqltranspiler</groupId>
    <artifactId>sql-transpiler</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <name>SQL Transpiler</name>
    <description>A tool to transpile SQL from MySQL dialect to others like Dameng and PostgreSQL.</description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <java.version>21</java.version>

        <!-- 测试相关属性 -->
        <skipTests>false</skipTests>
        <skipUTs>false</skipUTs>
        <skipITs>false</skipITs>

        <!-- Dependency Versions -->
        <antlr.version>4.13.1</antlr.version>

        <commons-lang3.version>3.14.0</commons-lang3.version>
        <guava.version>33.2.0-jre</guava.version>
        <slf4j.version>2.0.13</slf4j.version>
        <logback.version>1.5.6</logback.version>
        <picocli.version>4.7.6</picocli.version>
        <lombok.version>1.18.32</lombok.version>

        <!-- Spring Boot Version -->
        <spring-boot.version>3.2.5</spring-boot.version>

        <!-- Testing Dependency Versions -->
        <junit.version>5.10.2</junit.version>
        <assertj.version>3.26.0</assertj.version>

        <!-- Plugin Versions -->
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.2.5</maven-surefire-plugin.version>
        <maven-failsafe-plugin.version>3.2.5</maven-failsafe-plugin.version>
        <maven-shade-plugin.version>3.5.3</maven-shade-plugin.version>
        <native-maven-plugin.version>0.9.28</native-maven-plugin.version>
    </properties>

    <!-- 普通依赖仓库 -->
    <repositories>
        <!-- Maven Central Repository (默认启用) -->
        <repository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>false</enabled></snapshots>
        </repository>

        <!-- 私有仓库（可选，如果不可用会回退到Maven Central） -->
        <repository>
            <id>xylink-nexus</id>
            <url>http://maven.xylink.com:8081/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <!-- 插件仓库 -->
    <pluginRepositories>
        <!-- Maven Central Plugin Repository -->
        <pluginRepository>
            <id>central</id>
            <url>https://repo.maven.apache.org/maven2</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>false</enabled></snapshots>
        </pluginRepository>

        <!-- 私有仓库（插件也从这里下载） -->
        <pluginRepository>
            <id>xylink-nexus</id>
            <url>http://maven.xylink.com:8081/nexus/content/groups/public/</url>
            <releases><enabled>true</enabled></releases>
            <snapshots><enabled>true</enabled></snapshots>
        </pluginRepository>
    </pluginRepositories>

    <dependencies>
        <!-- Core -->
        <dependency>
            <groupId>org.antlr</groupId>
            <artifactId>antlr4-runtime</artifactId>
            <version>${antlr.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>

        <!-- CLI -->
        <dependency>
            <groupId>info.picocli</groupId>
            <artifactId>picocli</artifactId>
            <version>${picocli.version}</version>
        </dependency>

        <!-- Developer Tools -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

        <!-- Spring Boot Web Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>${spring-boot.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
            <version>${spring-boot.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
            <version>${spring-boot.version}</version>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <version>${spring-boot.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-suite</artifactId>
            <version>1.10.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>${assertj.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>sql-transpiler</finalName>
        <sourceDirectory>${project.basedir}/src/main/java</sourceDirectory>
        <testSourceDirectory>${project.basedir}/src/test/java</testSourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>add-source</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>${project.build.directory}/generated-sources/antlr4</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.antlr</groupId>
                <artifactId>antlr4-maven-plugin</artifactId>
                <version>${antlr.version}</version>
                <configuration>
                    <outputDirectory>${project.build.directory}/generated-sources/antlr4/com/xylink/sqltranspiler/infrastructure/parser/generated</outputDirectory>
                    <visitor>true</visitor>
                    <listener>false</listener>
                    <treatWarningsAsErrors>false</treatWarningsAsErrors>
                </configuration>
                <executions>
                    <execution>
                        <id>antlr</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>antlr4</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- 创建 Lombok 配置文件以避免 StackOverflowError -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>create-lombok-config</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <!-- 创建生成代码目录的 Lombok 配置文件 -->
                                <mkdir dir="${project.build.directory}/generated-sources"/>
                                <echo file="${project.build.directory}/generated-sources/lombok.config">
# Lombok 配置文件 - 生成代码目录专用
# 完全禁用 Lombok 对此目录及其子目录的处理
# 解决 ANTLR 生成代码导致的 StackOverflowError 问题

# 阻止配置向上冒泡，确保此配置只影响当前目录及子目录
config.stopBubbling = true

# 禁用 Lombok 生成的注解
lombok.addLombokGeneratedAnnotation = false

# 禁用构造函数属性
lombok.anyConstructor.addConstructorProperties = false

# 设置日志字段名
lombok.log.fieldName = log

# 禁用所有注解处理，防止 StackOverflowError
lombok.data.flagUsage = error
lombok.value.flagUsage = error
lombok.getter.flagUsage = error
lombok.setter.flagUsage = error
lombok.toString.flagUsage = error
lombok.equalsAndHashCode.flagUsage = error
lombok.allArgsConstructor.flagUsage = error
lombok.noArgsConstructor.flagUsage = error
lombok.requiredArgsConstructor.flagUsage = error
lombok.builder.flagUsage = error
lombok.log.flagUsage = error
                                </echo>

                                <!-- 创建 ANTLR4 目录的 Lombok 配置文件 -->
                                <mkdir dir="${project.build.directory}/generated-sources/antlr4"/>
                                <echo file="${project.build.directory}/generated-sources/antlr4/lombok.config">
# Lombok 配置文件 - ANTLR4 生成代码专用
# 完全禁用 Lombok 对 ANTLR 生成代码的处理
# 解决 StackOverflowError 问题

# 阻止配置向上冒泡，确保此配置只影响当前目录及子目录
config.stopBubbling = true

# 禁用 Lombok 生成的注解
lombok.addLombokGeneratedAnnotation = false

# 禁用构造函数属性
lombok.anyConstructor.addConstructorProperties = false

# 禁用访问器
lombok.accessors.fluent = false
lombok.accessors.chain = false

# 设置日志字段名
lombok.log.fieldName = log

# 禁用调用父类方法
lombok.equalsAndHashCode.callSuper = skip
lombok.toString.callSuper = skip

# 禁用所有注解处理，防止 StackOverflowError
lombok.data.flagUsage = error
lombok.value.flagUsage = error
lombok.getter.flagUsage = error
lombok.setter.flagUsage = error
lombok.toString.flagUsage = error
lombok.equalsAndHashCode.flagUsage = error
lombok.allArgsConstructor.flagUsage = error
lombok.noArgsConstructor.flagUsage = error
lombok.requiredArgsConstructor.flagUsage = error
lombok.builder.flagUsage = error
lombok.log.flagUsage = error
                                </echo>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>info.picocli</groupId>
                            <artifactId>picocli-codegen</artifactId>
                            <version>${picocli.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-Xlint:none</arg>
                        <arg>-nowarn</arg>
                    </compilerArgs>
                </configuration>
            </plugin>

            <!-- Maven Surefire Plugin for Unit Tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <includes>
                        <!-- 单元测试 -->
                        <include>**/unit/**/*Test.java</include>
                        <include>**/shared/**/*Test.java</include>
                        <!-- 兼容旧的测试类 -->
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                        <include>**/*TestCase.java</include>
                    </includes>
                    <excludes>
                        <!-- 排除集成测试、端到端测试、合规性测试 -->
                        <exclude>**/integration/**/*Test.java</exclude>
                        <exclude>**/e2e/**/*Test.java</exclude>
                        <exclude>**/compliance/**/*Test.java</exclude>
                        <exclude>**/regression/**/*Test.java</exclude>
                        <exclude>**/*IntegrationTest.java</exclude>
                        <exclude>**/*E2ETest.java</exclude>
                        <exclude>**/*ComplianceTest.java</exclude>
                        <exclude>**/*RegressionTest.java</exclude>
                        <exclude>**/*IT.java</exclude>
                    </excludes>
                    <systemPropertyVariables>
                        <!-- 使用标准JUL配置，避免log4j依赖问题 -->
                        <java.util.logging.config.file>src/test/resources/logging.properties</java.util.logging.config.file>
                    </systemPropertyVariables>
                    <argLine>
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/java.text=ALL-UNNAMED
                        --add-opens java.desktop/java.awt.font=ALL-UNNAMED
                    </argLine>
                </configuration>
            </plugin>

            <!-- Maven Failsafe Plugin for Integration Tests -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <version>${maven-failsafe-plugin.version}</version>
                <configuration>
                    <includes>
                        <!-- 集成测试 -->
                        <include>**/integration/**/*Test.java</include>
                        <include>**/integration/**/*IntegrationTest.java</include>
                        <!-- 端到端测试 -->
                        <include>**/e2e/**/*Test.java</include>
                        <include>**/e2e/**/*E2ETest.java</include>
                        <!-- 合规性测试 -->
                        <include>**/compliance/**/*Test.java</include>
                        <include>**/compliance/**/*ComplianceTest.java</include>
                        <!-- 回归测试 -->
                        <include>**/regression/**/*Test.java</include>
                        <include>**/regression/**/*RegressionTest.java</include>
                        <!-- 兼容旧的测试类 -->
                        <include>**/*IntegrationTest.java</include>
                        <include>**/*E2ETest.java</include>
                        <include>**/*ComplianceTest.java</include>
                        <include>**/*RegressionTest.java</include>
                        <include>**/*IT.java</include>
                    </includes>
                    <systemPropertyVariables>
                        <!-- 使用标准JUL配置，避免log4j依赖问题 -->
                        <java.util.logging.config.file>src/test/resources/logging.properties</java.util.logging.config.file>
                    </systemPropertyVariables>
                    <argLine>
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/java.text=ALL-UNNAMED
                        --add-opens java.desktop/java.awt.font=ALL-UNNAMED
                    </argLine>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>${maven-shade-plugin.version}</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <mainClass>com.xylink.sqltranspiler.Main</mainClass>
                                </transformer>
                            </transformers>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Spring Boot Maven Plugin -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>process-aot</id>
                        <goals>
                            <goal>process-aot</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

    <profiles>
        <!-- 测试配置Profile -->
        <profile>
            <id>test</id>
            <properties>
                <skipTests>false</skipTests>
                <skipUTs>false</skipUTs>
                <skipITs>false</skipITs>
            </properties>
        </profile>

        <!-- 跳过测试Profile -->
        <profile>
            <id>skip-tests</id>
            <properties>
                <skipTests>true</skipTests>
                <skipUTs>true</skipUTs>
                <skipITs>true</skipITs>
            </properties>
        </profile>

        <!-- 只运行单元测试Profile -->
        <profile>
            <id>unit-tests-only</id>
            <properties>
                <skipTests>false</skipTests>
                <skipUTs>false</skipUTs>
                <skipITs>true</skipITs>
            </properties>
        </profile>

        <!--
        Native Image Profile using Spring Boot AOT.
       This single profile replaces the multiple, platform-specific profiles.
        It leverages the spring-boot-maven-plugin's AOT processing and the
        native-maven-plugin's ability to auto-detect the host environment.
        To build, run: mvn clean package -Pnative
        -->
        <profile>
            <id>native</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.graalvm.buildtools</groupId>
                        <artifactId>native-maven-plugin</artifactId>
                        <version>${native-maven-plugin.version}</version>
                        <extensions>true</extensions>
                        <executions>
                            <execution>
                                <id>build-native</id>
                                <goals>
                                    <goal>compile-no-fork</goal>
                                </goals>
                                <phase>package</phase>
                            </execution>
                        </executions>
                        <configuration>
                            <imageName>sql-transpiler</imageName>
                            <mainClass>com.xylink.sqltranspiler.Main</mainClass>
                            <buildArgs>
                                <buildArg>--no-fallback</buildArg>
                                <buildArg>--enable-preview</buildArg>
                                <buildArg>--report-unsupported-elements-at-runtime</buildArg>
                                <buildArg>--allow-incomplete-classpath</buildArg>
                                <buildArg>-H:+ReportExceptionStackTraces</buildArg>
                                <buildArg>-H:+AddAllCharsets</buildArg>
                                <buildArg>-H:IncludeResources=.*\.properties$</buildArg>
                                <buildArg>-H:IncludeResources=.*\.xml$</buildArg>
                                <buildArg>-H:IncludeResources=.*\.g4$</buildArg>
                                <buildArg>-H:IncludeResources=.*\.tokens$</buildArg>
                                <buildArg>-H:IncludeResources=.*\.interp$</buildArg>
                            </buildArgs>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>