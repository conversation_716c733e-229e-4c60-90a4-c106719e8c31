package com.xylink.sqltranspiler.core.dialects;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import com.xylink.sqltranspiler.common.constants.ReservedWords;

/**
 * 神通数据库方言实现
 * 
 * 基于神通数据库官方文档实现：
 * - 标识符引用规则
 * - 数据类型映射
 * - 函数映射
 * - SQL特性支持
 * 
 * 遵循 .augment/rules/rule-db.md：
 * - 基于官方文档的准确实现
 * - 不允许推测，必须查看官方文档
 * - 动态验证机制
 */
public class ShentongDialect implements SqlDialect {
    
    // 数据类型映射表 - 基于神通官方文档
    private static final Map<String, String> DATA_TYPE_MAPPING;
    
    // 函数映射表 - 基于神通官方文档
    private static final Map<String, String> FUNCTION_MAPPING;
    
    static {
        // 初始化数据类型映射
        Map<String, String> map = new HashMap<>();
        
        // 数值类型 - 基于神通官方文档
        map.put("TINYINT", "SMALLINT"); // 神通不支持TINYINT，映射为SMALLINT
        map.put("SMALLINT", "SMALLINT");
        map.put("MEDIUMINT", "INT");
        map.put("INT", "INT");
        map.put("INTEGER", "INTEGER");
        map.put("BIGINT", "BIGINT");
        map.put("DECIMAL", "DECIMAL");
        map.put("NUMERIC", "NUMERIC");
        map.put("FLOAT", "FLOAT");
        map.put("DOUBLE", "DOUBLE PRECISION");
        map.put("REAL", "REAL");
        map.put("BIT", "CHAR(1)"); // 神通使用CHAR(1)表示布尔值
        map.put("BOOLEAN", "CHAR(1)");
        
        // 字符串类型 - 基于神通官方文档
        map.put("CHAR", "CHAR");
        map.put("VARCHAR", "VARCHAR");
        map.put("TINYTEXT", "VARCHAR(255)");
        map.put("TEXT", "TEXT");
        map.put("MEDIUMTEXT", "TEXT");
        map.put("LONGTEXT", "TEXT");
        
        // 二进制类型 - 基于神通官方文档
        map.put("BINARY", "BYTEA");
        map.put("VARBINARY", "BYTEA");
        map.put("TINYBLOB", "BYTEA");
        map.put("BLOB", "BYTEA");
        map.put("MEDIUMBLOB", "BYTEA");
        map.put("LONGBLOB", "BYTEA");
        
        // 日期时间类型 - 基于神通官方文档
        map.put("DATE", "DATE");
        map.put("TIME", "TIME");
        map.put("DATETIME", "TIMESTAMP");
        map.put("TIMESTAMP", "TIMESTAMP");
        map.put("YEAR", "SMALLINT"); // 神通不支持YEAR类型
        
        // JSON类型 - 基于神通官方文档
        map.put("JSON", "TEXT"); // 神通使用TEXT存储JSON
        
        DATA_TYPE_MAPPING = Collections.unmodifiableMap(map);
        
        // 初始化函数映射
        map = new HashMap<>();
        
        // 日期时间函数 - 基于神通官方文档
        map.put("NOW", "CURRENT_TIMESTAMP");
        map.put("CURDATE", "CURRENT_DATE");
        map.put("CURTIME", "CURRENT_TIME");
        map.put("SYSDATE", "CURRENT_TIMESTAMP");
        map.put("UTC_TIMESTAMP", "CURRENT_TIMESTAMP");
        
        // 字符串函数 - 基于神通官方文档
        map.put("SUBSTRING", "SUBSTR");
        map.put("LENGTH", "LENGTH");
        map.put("CONCAT", "CONCAT");
        map.put("UPPER", "UPPER");
        map.put("LOWER", "LOWER");
        map.put("TRIM", "TRIM");
        
        // 条件函数 - 基于神通官方文档
        map.put("IFNULL", "COALESCE");
        map.put("NULLIF", "NULLIF");
        map.put("COALESCE", "COALESCE");
        
        // 数学函数 - 基于神通官方文档
        map.put("ABS", "ABS");
        map.put("CEIL", "CEIL");
        map.put("FLOOR", "FLOOR");
        map.put("ROUND", "ROUND");
        map.put("MOD", "MOD");
        map.put("RAND", "RANDOM");
        
        FUNCTION_MAPPING = Collections.unmodifiableMap(map);
    }
    
    @Override
    public String getName() {
        return "ShenTong";
    }
    
    @Override
    public String getDatabaseProduct() {
        return "ShenTong Database";
    }
    
    @Override
    public String quoteIdentifier(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return identifier;
        }
        
        String cleanIdentifier = identifier.trim();
        
        // 如果已经被引用，直接返回
        if (cleanIdentifier.startsWith("\"") && cleanIdentifier.endsWith("\"")) {
            return cleanIdentifier;
        }
        
        // 检查是否需要引用
        if (needsQuoting(cleanIdentifier)) {
            return "\"" + cleanIdentifier + "\"";
        }
        
        return cleanIdentifier;
    }

    @Override
    public String quoteLiteral(String literal) {
        if (literal == null) {
            return "NULL";
        }
        // 神通数据库使用单引号引用字符串字面量
        return "'" + literal.replace("'", "''") + "'";
    }

    @Override
    public boolean requiresQuoting(String identifier) {
        return needsQuoting(identifier);
    }

    /**
     * 判断标识符是否需要引用
     * 基于神通数据库官方文档
     */
    private boolean needsQuoting(String identifier) {
        // 1. 保留字需要引用
        if (ReservedWords.isReservedWord(identifier, "shentong")) {
            return true;
        }
        
        // 2. 包含特殊字符需要引用
        if (!identifier.matches("[a-zA-Z_][a-zA-Z0-9_]*")) {
            return true;
        }
        
        // 3. 混合大小写需要引用
        boolean hasUpper = identifier.chars().anyMatch(Character::isUpperCase);
        boolean hasLower = identifier.chars().anyMatch(Character::isLowerCase);
        if (hasUpper && hasLower) {
            return true;
        }
        
        return false;
    }
    
    @Override
    public String mapDataType(String mysqlType, Integer length, Integer precision, Integer scale) {
        if (mysqlType == null) {
            return null;
        }
        
        String upperType = mysqlType.toUpperCase();
        
        // 处理带长度的类型
        if (upperType.startsWith("VARCHAR") && length != null) {
            return "VARCHAR(" + length + ")";
        } else if (upperType.startsWith("CHAR") && length != null) {
            return "CHAR(" + length + ")";
        } else if (upperType.startsWith("DECIMAL") && precision != null && precision > 0) {
            if (scale != null && scale >= 0) {
                return "DECIMAL(" + precision + "," + scale + ")";
            } else {
                return "DECIMAL(" + precision + ")";
            }
        }
        
        // 基础类型映射
        String baseType = upperType.split("\\(")[0]; // 去除长度部分
        String mappedType = DATA_TYPE_MAPPING.get(baseType);
        
        return mappedType != null ? mappedType : mysqlType;
    }
    
    @Override
    public String mapFunction(String functionName, String... args) {
        if (functionName == null) {
            return "";
        }
        
        String stFunction = FUNCTION_MAPPING.get(functionName.toUpperCase());
        if (stFunction != null) {
            if (args.length > 0) {
                return stFunction + "(" + String.join(", ", args) + ")";
            } else {
                // 对于无参数函数，检查是否需要括号
                if (needsParentheses(functionName)) {
                    return stFunction + "()";
                } else {
                    return stFunction;
                }
            }
        }
        
        // 如果没有映射，返回原函数名
        if (args.length > 0) {
            return functionName + "(" + String.join(", ", args) + ")";
        } else {
            return functionName;
        }
    }
    
    /**
     * 检查函数是否需要括号
     */
    private boolean needsParentheses(String functionName) {
        String upperFunction = functionName.toUpperCase();
        // 这些函数即使无参数也需要括号
        return "RAND".equals(upperFunction) || 
               "RANDOM".equals(upperFunction) ||
               "NOW".equals(upperFunction) ||
               "CURDATE".equals(upperFunction) ||
               "CURTIME".equals(upperFunction) ||
               "CURRENT_TIMESTAMP".equals(upperFunction) ||
               "CURRENT_DATE".equals(upperFunction) ||
               "CURRENT_TIME".equals(upperFunction);
    }
    
    @Override
    public String getCurrentTimestampFunction() {
        return "CURRENT_TIMESTAMP";
    }
    
    @Override
    public String getCurrentDateFunction() {
        return "CURRENT_DATE";
    }

    @Override
    public boolean supportsLimit() {
        // 神通数据库支持LIMIT语法
        return true;
    }

    @Override
    public boolean supportsOffset() {
        // 神通数据库支持OFFSET语法
        return true;
    }

    @Override
    public String formatPagination(Integer limit, Integer offset) {
        // 神通数据库支持标准的LIMIT OFFSET语法
        StringBuilder sb = new StringBuilder();

        if (limit != null && limit > 0) {
            sb.append(" LIMIT ").append(limit);
        }

        if (offset != null && offset > 0) {
            sb.append(" OFFSET ").append(offset);
        }

        return sb.toString();
    }
    
    @Override
    public boolean supportsDataType(String dataType) {
        if (dataType == null) {
            return false;
        }
        String baseType = dataType.toUpperCase().split("\\(")[0];
        return DATA_TYPE_MAPPING.containsKey(baseType);
    }
    
    @Override
    public boolean supportsFunction(String functionName) {
        return FUNCTION_MAPPING.containsKey(functionName.toUpperCase());
    }

    @Override
    public boolean supportsCheckConstraint() {
        // 神通数据库支持CHECK约束
        return true;
    }

    @Override
    public boolean supportsForeignKey() {
        // 神通数据库支持外键约束
        return true;
    }

    @Override
    public boolean supportsAutoIncrement() {
        // 神通数据库支持AUTO_INCREMENT
        return true;
    }

    @Override
    public String getAutoIncrementSyntax() {
        // 神通数据库使用AUTO_INCREMENT语法
        return "AUTO_INCREMENT";
    }

    @Override
    public String formatPrimaryKey(String... columnNames) {
        if (columnNames == null || columnNames.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("PRIMARY KEY (");

        for (int i = 0; i < columnNames.length; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(quoteIdentifier(columnNames[i]));
        }

        sb.append(")");
        return sb.toString();
    }

    @Override
    public String formatUniqueConstraint(String... columnNames) {
        if (columnNames == null || columnNames.length == 0) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("UNIQUE (");

        for (int i = 0; i < columnNames.length; i++) {
            if (i > 0) {
                sb.append(", ");
            }
            sb.append(quoteIdentifier(columnNames[i]));
        }

        sb.append(")");
        return sb.toString();
    }
}
