package com.xylink.sqltranspiler.core.functions;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.xylink.sqltranspiler.core.dialects.SqlDialect;

/**
 * 函数映射引擎
 * 借鉴Apache Calcite的函数映射思路，提供标准化的函数转换能力
 * 
 * 设计原则：
 * 1. MySQL函数 -> 标准SQL函数 -> 目标数据库函数
 * 2. 支持复杂的函数参数转换
 * 3. 基于官方文档的准确映射
 * 4. 易于扩展新的函数映射
 */
public class FunctionMapper {
    
    // MySQL到标准SQL的函数映射 - 借鉴Calcite的标准函数表思路
    private static final Map<String, String> MYSQL_TO_STANDARD;
    static {
        Map<String, String> map = new HashMap<>();
        
        // 日期时间函数映射 - 基于MySQL 8.4官方文档
        map.put("NOW", "CURRENT_TIMESTAMP");
        map.put("CURDATE", "CURRENT_DATE");
        map.put("CURTIME", "CURRENT_TIME");
        map.put("SYSDATE", "CURRENT_TIMESTAMP");
        
        // 字符串函数映射 - 基于MySQL 8.4官方文档
        map.put("LENGTH", "CHAR_LENGTH");
        map.put("CHAR_LENGTH", "CHAR_LENGTH");
        map.put("CHARACTER_LENGTH", "CHAR_LENGTH");
        map.put("SUBSTR", "SUBSTRING");
        map.put("SUBSTRING", "SUBSTRING");
        map.put("CONCAT", "CONCAT");
        map.put("UPPER", "UPPER");
        map.put("LOWER", "LOWER");
        map.put("TRIM", "TRIM");
        map.put("LTRIM", "LTRIM");
        map.put("RTRIM", "RTRIM");
        
        // 数学函数映射 - 基于MySQL 8.4官方文档
        map.put("ABS", "ABS");
        map.put("CEIL", "CEIL");
        map.put("CEILING", "CEIL");
        map.put("FLOOR", "FLOOR");
        map.put("ROUND", "ROUND");
        map.put("MOD", "MOD");
        map.put("POWER", "POWER");
        map.put("POW", "POWER");
        map.put("SQRT", "SQRT");
        
        // 聚合函数映射 - 基于MySQL 8.4官方文档
        map.put("COUNT", "COUNT");
        map.put("SUM", "SUM");
        map.put("AVG", "AVG");
        map.put("MAX", "MAX");
        map.put("MIN", "MIN");
        
        // 条件函数映射 - 基于MySQL 8.4官方文档
        map.put("IFNULL", "COALESCE");
        map.put("ISNULL", "IS_NULL");
        map.put("NULLIF", "NULLIF");
        
        // 类型转换函数映射 - 基于MySQL 8.4官方文档
        map.put("CAST", "CAST");
        map.put("CONVERT", "CAST");
        
        MYSQL_TO_STANDARD = Collections.unmodifiableMap(map);
    }
    
    // 特殊函数处理规则
    private static final Map<String, FunctionTransformRule> SPECIAL_FUNCTIONS;
    static {
        Map<String, FunctionTransformRule> map = new HashMap<>();
        
        // IF函数特殊处理：IF(condition, true_value, false_value) -> CASE WHEN condition THEN true_value ELSE false_value END
        map.put("IF", new IfFunctionRule());
        
        // DATE_FORMAT函数特殊处理：需要根据目标数据库转换格式字符串
        map.put("DATE_FORMAT", new DateFormatFunctionRule());
        
        // SUBSTRING函数特殊处理：MySQL的SUBSTRING(str, pos, len) vs 标准SQL的SUBSTRING(str FROM pos FOR len)
        map.put("SUBSTRING", new SubstringFunctionRule());
        
        SPECIAL_FUNCTIONS = Collections.unmodifiableMap(map);
    }
    
    /**
     * 映射MySQL函数到目标数据库函数
     * 
     * @param mysqlFunction MySQL函数名
     * @param args 函数参数列表
     * @param targetDialect 目标数据库方言
     * @return 转换后的函数调用字符串
     */
    public String mapFunction(String mysqlFunction, List<String> args, SqlDialect targetDialect) {
        if (mysqlFunction == null || mysqlFunction.isEmpty()) {
            return "";
        }
        
        String upperFunction = mysqlFunction.toUpperCase();
        
        // 1. 检查是否有特殊处理规则
        FunctionTransformRule specialRule = SPECIAL_FUNCTIONS.get(upperFunction);
        if (specialRule != null) {
            return specialRule.transform(mysqlFunction, args, targetDialect);
        }
        
        // 2. 直接使用目标方言映射MySQL函数
        // 这样可以利用各方言自己的函数映射表
        return targetDialect.mapFunction(mysqlFunction, args.toArray(new String[0]));
    }
    
    /**
     * 检查是否支持指定的MySQL函数
     * 
     * @param mysqlFunction MySQL函数名
     * @param targetDialect 目标数据库方言
     * @return 是否支持
     */
    public boolean supportsFunction(String mysqlFunction, SqlDialect targetDialect) {
        if (mysqlFunction == null || mysqlFunction.isEmpty()) {
            return false;
        }
        
        String upperFunction = mysqlFunction.toUpperCase();
        
        // 检查特殊函数
        if (SPECIAL_FUNCTIONS.containsKey(upperFunction)) {
            return true;
        }
        
        // 检查标准映射
        if (MYSQL_TO_STANDARD.containsKey(upperFunction)) {
            return true;
        }
        
        // 检查目标方言是否直接支持
        return targetDialect.supportsFunction(mysqlFunction);
    }
    
    /**
     * 获取所有支持的MySQL函数列表
     * 
     * @return MySQL函数名集合
     */
    public static java.util.Set<String> getSupportedMySqlFunctions() {
        java.util.Set<String> functions = new java.util.HashSet<>();
        functions.addAll(MYSQL_TO_STANDARD.keySet());
        functions.addAll(SPECIAL_FUNCTIONS.keySet());
        return Collections.unmodifiableSet(functions);
    }
    
    // ==================== 函数转换规则接口 ====================
    
    /**
     * 函数转换规则接口
     */
    public interface FunctionTransformRule {
        /**
         * 转换函数调用
         * 
         * @param functionName 原函数名
         * @param args 参数列表
         * @param targetDialect 目标方言
         * @return 转换后的SQL
         */
        String transform(String functionName, List<String> args, SqlDialect targetDialect);
    }
    
    // ==================== 具体的函数转换规则 ====================
    
    /**
     * IF函数转换规则
     * MySQL: IF(condition, true_value, false_value)
     * 标准SQL: CASE WHEN condition THEN true_value ELSE false_value END
     */
    public static class IfFunctionRule implements FunctionTransformRule {
        @Override
        public String transform(String functionName, List<String> args, SqlDialect targetDialect) {
            if (args.size() != 3) {
                // 参数数量不正确，回退到原函数
                return targetDialect.mapFunction(functionName, args.toArray(new String[0]));
            }
            
            return String.format("CASE WHEN %s THEN %s ELSE %s END", 
                args.get(0), args.get(1), args.get(2));
        }
    }
    
    /**
     * DATE_FORMAT函数转换规则
     * MySQL: DATE_FORMAT(date, format)
     * 需要根据目标数据库转换格式字符串
     */
    public static class DateFormatFunctionRule implements FunctionTransformRule {
        @Override
        public String transform(String functionName, List<String> args, SqlDialect targetDialect) {
            if (args.size() != 2) {
                return targetDialect.mapFunction(functionName, args.toArray(new String[0]));
            }
            
            String date = args.get(0);
            String format = args.get(1);
            
            // 根据目标数据库类型转换格式字符串
            String dialectName = targetDialect.getName();
            switch (dialectName) {
                case "DM":
                    // 达梦使用TO_CHAR函数
                    return String.format("TO_CHAR(%s, %s)", date, convertFormatToDameng(format));
                case "KingbaseES":
                    // 金仓使用TO_CHAR函数
                    return String.format("TO_CHAR(%s, %s)", date, convertFormatToKingbase(format));
                case "Shentong":
                    // 神通使用TO_CHAR函数
                    return String.format("TO_CHAR(%s, %s)", date, convertFormatToShentong(format));
                default:
                    // 默认回退
                    return targetDialect.mapFunction(functionName, args.toArray(new String[0]));
            }
        }
        
        private String convertFormatToDameng(String mysqlFormat) {
            // 转换MySQL格式字符串到达梦格式
            // 基于达梦官方文档的格式字符串规范
            return mysqlFormat
                .replace("%Y", "YYYY")
                .replace("%m", "MM")
                .replace("%d", "DD")
                .replace("%H", "HH24")
                .replace("%i", "MI")
                .replace("%s", "SS");
        }
        
        private String convertFormatToKingbase(String mysqlFormat) {
            // 转换MySQL格式字符串到金仓格式（PostgreSQL兼容）
            // 基于金仓官方文档的格式字符串规范
            return mysqlFormat
                .replace("%Y", "YYYY")
                .replace("%m", "MM")
                .replace("%d", "DD")
                .replace("%H", "HH24")
                .replace("%i", "MI")
                .replace("%s", "SS");
        }
        
        private String convertFormatToShentong(String mysqlFormat) {
            // 转换MySQL格式字符串到神通格式
            // 基于神通官方文档的格式字符串规范
            return mysqlFormat
                .replace("%Y", "YYYY")
                .replace("%m", "MM")
                .replace("%d", "DD")
                .replace("%H", "HH24")
                .replace("%i", "MI")
                .replace("%s", "SS");
        }
    }
    
    /**
     * SUBSTRING函数转换规则
     * MySQL: SUBSTRING(str, pos, len) 或 SUBSTRING(str FROM pos FOR len)
     * 标准SQL: SUBSTRING(str FROM pos FOR len)
     */
    public static class SubstringFunctionRule implements FunctionTransformRule {
        @Override
        public String transform(String functionName, List<String> args, SqlDialect targetDialect) {
            // 直接使用目标方言的SUBSTRING映射
            return targetDialect.mapFunction("SUBSTRING", args.toArray(new String[0]));
        }
    }
}
