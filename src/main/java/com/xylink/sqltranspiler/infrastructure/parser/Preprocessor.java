package com.xylink.sqltranspiler.infrastructure.parser;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Preprocessor {

    // 使用更安全的正则表达式模式，避免在大文件中导致 StackOverflowError
    // 限制匹配长度，避免过度回溯
    private static final Pattern MYSQL_CONDITIONAL_COMMENT_PATTERN = Pattern.compile("/\\*!\\d{5}\\s?([^*]{0,1000}(?:\\*(?!/)[^*]{0,1000})*)\\s?\\*/", Pattern.DOTALL);

    /**
     * 预处理SQL - 向后兼容的重载方法
     * 默认保留COMMENT语句
     */
    public static PreprocessingResult preprocess(String sql) {
        return preprocess(sql, true, null);
    }

    /**
     * 预处理SQL - 支持控制COMMENT语句处理
     *
     * @param sql 原始SQL内容
     * @param preserveComments 是否保留MySQL COMMENT子句（用于后续转换为COMMENT ON语句）
     * @return 预处理结果
     */
    public static PreprocessingResult preprocess(String sql, boolean preserveComments) {
        return preprocess(sql, preserveComments, null);
    }

    /**
     * 预处理SQL - 完整版本，支持目标数据库类型
     *
     * @param sql 原始SQL内容
     * @param preserveComments 是否保留MySQL COMMENT子句（用于后续转换为COMMENT ON语句）
     * @param targetDialect 目标数据库类型，用于决定是否移除特定选项
     * @return 预处理结果
     */
    public static PreprocessingResult preprocess(String sql, boolean preserveComments, String targetDialect) {
        List<String> logs = new ArrayList<>();

        // 记录原始SQL统计信息
        int originalLines = sql.split("\n").length;
        int originalLength = sql.length();
        logs.add("PREPROCESSING: Starting with " + originalLines + " lines, " + originalLength + " characters");
        logs.add("PREPROCESSING: Comment preservation: " + (preserveComments ? "enabled" : "disabled"));

        // 【修复StackOverflowError】当SQL内容过大时，使用简化的预处理流程
        // 避免在处理大文件时出现栈溢出错误
        if (sql.length() > 100000) { // 100KB阈值
            logs.add("PREPROCESSING: Large SQL detected (" + sql.length() + " chars), using simplified processing to avoid StackOverflowError");
            return preprocessLargeFile(sql, preserveComments, targetDialect, logs);
        }

        // 第一步：预处理非标准数据类型，转换为MySQL兼容格式（必须在双引号转换之前）
        String cleanedSql = preprocessNonStandardDataTypes(sql, logs, targetDialect);

        // 第二步：将双引号标识符转换为反引号（MySQL标准）
        cleanedSql = convertDoubleQuotesToBackticks(cleanedSql, logs);

        // 第三步：清理MySQL注释（但保留COMMENT子句如果需要）
        cleanedSql = removeAllComments(cleanedSql, logs, preserveComments);

        // 第四步：移除MySQL条件注释
        cleanedSql = removeConditionalComments(cleanedSql, logs);

        // 第五步：规范化跨行的列定义（在添加反引号之前执行）
        cleanedSql = normalizeMultiLineColumnDefinitions(cleanedSql, logs);

        // 第六步：清理空行和多余空白
        cleanedSql = cleanWhitespace(cleanedSql, logs);

        // 第七步：清理无效字符和符号
        cleanedSql = cleanInvalidCharacters(cleanedSql, logs);

        // 第五步：修复孤立关键字（暂时禁用）
        cleanedSql = fixOrphanedKeywordsAsColumns(cleanedSql, logs);

        // 第五步：移除MySQL特有的表选项（ENGINE、DEFAULT CHARSET等）
        cleanedSql = removeMySqlTableOptions(cleanedSql, logs, targetDialect);

        // 第六步：过滤不支持的SQL语句（明确记录）
        cleanedSql = filterUnsupportedStatements(cleanedSql, logs);

        // 第七步：添加标识符引用，遵循MySQL官方标准
        // MySQL标准：反引号用于标识符（表名、字段名），避免与保留关键字冲突
        // 注意：达梦生成器会自动将反引号转换为双引号，符合达梦官方标准
        cleanedSql = addBackticksToAllIdentifiers(cleanedSql, logs);

        // 第七步：标准化反引号标识符，移除反引号让ANTLR能够正常解析
        // 生成器会根据目标数据库的标准添加适当的引号
        cleanedSql = normalizeBacktickIdentifiers(cleanedSql, logs);

        // 记录处理后的统计信息
        int finalLines = cleanedSql.split("\n").length;
        int finalLength = cleanedSql.length();
        logs.add("PREPROCESSING: Completed with " + finalLines + " lines, " + finalLength + " characters");
        logs.add("PREPROCESSING: Removed " + (originalLines - finalLines) + " lines, " + (originalLength - finalLength) + " characters");

        return new PreprocessingResult(cleanedSql, logs);
    }

    /**
     * 大文件的简化预处理流程
     * 避免复杂的正则表达式导致StackOverflowError
     */
    private static PreprocessingResult preprocessLargeFile(String sql, boolean preserveComments, String targetDialect, List<String> logs) {
        String cleanedSql = sql;

        // 只进行最基本的清理，避免复杂的正则表达式
        // 1. 简单的注释清理
        cleanedSql = cleanedSql.replaceAll("--[^\r\n]*", ""); // 单行注释
        cleanedSql = cleanedSql.replaceAll("/\\*[^*]*\\*+(?:[^/*][^*]*\\*+)*/", ""); // 块注释（简化版）
        logs.add("PREPROCESSING: Removed basic comments");

        // 2. 基本的空白清理
        cleanedSql = cleanedSql.replaceAll("\\s+", " ").trim();
        logs.add("PREPROCESSING: Cleaned whitespace");

        // 3. 移除MySQL特有的表选项（简化版）
        cleanedSql = cleanedSql.replaceAll("(?i)\\s+ENGINE\\s*=\\s*\\w+", "");
        cleanedSql = cleanedSql.replaceAll("(?i)\\s+DEFAULT\\s+CHARSET\\s*=\\s*\\w+", "");
        cleanedSql = cleanedSql.replaceAll("(?i)\\s+ROW_FORMAT\\s*=\\s*\\w+", "");
        logs.add("PREPROCESSING: Removed basic MySQL table options");

        // 记录处理后的统计信息
        int finalLines = cleanedSql.split("\n").length;
        int finalLength = cleanedSql.length();
        logs.add("PREPROCESSING: Large file processing completed with " + finalLines + " lines, " + finalLength + " characters");

        return new PreprocessingResult(cleanedSql, logs);
    }

    private static String removeConditionalComments(String sql, List<String> logs) {
        Matcher matcher = MYSQL_CONDITIONAL_COMMENT_PATTERN.matcher(sql);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            logs.add("Preprocessor: Removed MySQL conditional comment: " + matcher.group(0));
            matcher.appendReplacement(sb, "");
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 规范化跨行的列定义
     * 将跨行的列定义合并为单行，以便ANTLR解析器能够正确处理
     *
     * 简化策略：在CREATE TABLE语句中，将以逗号或分号结尾但被换行分割的内容合并为单行
     *
     * 例如：
     * number_prefix bigint not null
     *     unique comment 'subtype 生成前缀',
     *
     * 转换为：
     * number_prefix bigint not null unique comment 'subtype 生成前缀',
     */
    private static String normalizeMultiLineColumnDefinitions(String sql, List<String> logs) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        String result = sql;
        int normalizedCount = 0;

        // 简化策略：在CREATE TABLE语句中，查找跨行但以逗号或分号结尾的内容
        // 分步处理：
        // 1. 找到CREATE TABLE语句的范围
        // 2. 在该范围内，将跨行的内容合并

        Pattern createTablePattern = Pattern.compile(
            "(CREATE\\s+TABLE\\s+[^(]+\\([^;]+;)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher createTableMatcher = createTablePattern.matcher(result);
        StringBuffer sb = new StringBuffer();

        while (createTableMatcher.find()) {
            String createTableStatement = createTableMatcher.group(1);
            String normalizedStatement = normalizeWithinCreateTable(createTableStatement, logs);

            if (!normalizedStatement.equals(createTableStatement)) {
                normalizedCount++;
            }

            createTableMatcher.appendReplacement(sb, normalizedStatement);
        }

        createTableMatcher.appendTail(sb);
        result = sb.toString();

        if (normalizedCount > 0) {
            logs.add("COLUMN_NORMALIZATION: Normalized " + normalizedCount + " CREATE TABLE statements with multi-line definitions");
        }

        return result;
    }

    /**
     * 在CREATE TABLE语句内部规范化跨行内容
     * 将以逗号或分号结尾但被换行分割的内容合并为单行
     */
    private static String normalizeWithinCreateTable(String createTableStatement, List<String> logs) {
        // 简单策略：将换行+缩进+非逗号非分号内容 合并到前一行
        // 匹配模式：行尾不是逗号或分号（可能有空格），下一行可能有缩进的内容
        Pattern multiLinePattern = Pattern.compile(
            "([^,;\\n])\\s*\\n\\s*([^\\n]+[,;])",
            Pattern.MULTILINE
        );

        String result = createTableStatement;
        Matcher matcher = multiLinePattern.matcher(result);

        while (matcher.find()) {
            String beforeNewline = matcher.group(1);
            String afterNewline = matcher.group(2);

            // 合并为单行，保持一个空格分隔，并清理多余空格
            String replacement = beforeNewline + " " + afterNewline;
            result = result.replace(matcher.group(), replacement);

            // 重新匹配，因为内容已经改变
            matcher = multiLinePattern.matcher(result);
        }

        // 清理CREATE TABLE语句中的多余空格
        result = cleanExcessiveSpacesInCreateTable(result);

        return result;
    }

    /**
     * 清理CREATE TABLE语句中的多余空格
     * 将多个连续空格替换为单个空格，但保留必要的格式
     */
    private static String cleanExcessiveSpacesInCreateTable(String createTableStatement) {
        String result = createTableStatement;

        // 在列定义行中，将多个空格替换为单个空格
        // 但要小心不要破坏字符串字面量中的空格
        String[] lines = result.split("\n");
        StringBuilder sb = new StringBuilder();

        for (String line : lines) {
            // 检查是否是列定义行（包含数据类型关键字）
            if (line.trim().matches(".*\\b(bigint|int|integer|varchar|text|timestamp|datetime|date|decimal|float|double|char|boolean|bit)\\b.*")) {
                // 清理多余空格，但保留缩进
                String indent = line.replaceAll("^(\\s*).*", "$1");
                String content = line.trim();
                // 将多个空格替换为单个空格
                content = content.replaceAll("\\s+", " ");
                sb.append(indent).append(content);
            } else {
                // 非列定义行保持原样
                sb.append(line);
            }
            sb.append("\n");
        }

        // 移除最后一个多余的换行符
        if (sb.length() > 0 && sb.charAt(sb.length() - 1) == '\n') {
            sb.setLength(sb.length() - 1);
        }

        return sb.toString();
    }

    /**
     * 预处理非标准数据类型，转换为MySQL兼容格式
     * 这样可以让MySQL解析器正确解析，然后在生成器中再转换回目标数据库格式
     */
    private static String preprocessNonStandardDataTypes(String sql, List<String> logs, String targetDialect) {
        String result = sql;

        // 处理NVARCHAR2类型 - 根据目标数据库类型决定转换策略
        // 神通数据库：根据官方文档，神通数据库将NVARCHAR2映射为VARCHAR，所以直接转换
        // 达梦/金仓数据库：原生支持NVARCHAR2，保持不变
        Pattern nvarchar2Pattern = Pattern.compile("(?i)\\bNVARCHAR2\\s*\\((\\d+)\\)", Pattern.MULTILINE);
        Matcher nvarchar2Matcher = nvarchar2Pattern.matcher(result);
        if (nvarchar2Matcher.find()) {
            if ("shentong".equalsIgnoreCase(targetDialect)) {
                // 神通数据库：根据官方文档第572行，神通数据库将NVARCHAR2映射为VARCHAR
                result = nvarchar2Pattern.matcher(result).replaceAll(matchResult -> {
                    String originalLength = matchResult.group(1);
                    return "VARCHAR(" + originalLength + ")";
                });
                logs.add("INFO: Converted NVARCHAR2 to VARCHAR for Shentong database (official doc: NVARCHAR2 maps to VARCHAR)");
            } else {
                // 达梦/金仓数据库：原生支持NVARCHAR2，保持不变
                logs.add("INFO: NVARCHAR2 type preserved for target database: " + targetDialect + " (native support)");
            }
        }

        // 处理神通数据库ROWNUM伪列 - 临时转换为MySQL兼容的语法
        // 根据神通数据库官方文档，ROWNUM是一个重要的伪列功能
        Pattern rownumPattern = Pattern.compile("(?i)\\bROWNUM\\b", Pattern.MULTILINE);
        Matcher rownumMatcher = rownumPattern.matcher(result);
        if (rownumMatcher.find()) {
            // 将ROWNUM转换为MySQL兼容的ROW_NUMBER()函数调用
            // 这样MySQL解析器可以理解，在生成器中再转换回ROWNUM
            result = result.replaceAll("(?i)\\bROWNUM\\b", "ROW_NUMBER() OVER ()");
            logs.add("INFO: Temporarily converted Shentong ROWNUM to MySQL ROW_NUMBER() for parser compatibility");
        }

        // 处理BIGSERIAL类型 - 临时转换为BIGINT AUTO_INCREMENT让MySQL解析器能理解
        // 在神通生成器中会再转换回BIGSERIAL格式
        Pattern bigserialPattern = Pattern.compile("(?i)\\bBIGSERIAL\\b", Pattern.MULTILINE);
        Matcher bigserialMatcher = bigserialPattern.matcher(result);
        if (bigserialMatcher.find()) {
            result = result.replaceAll("(?i)\\bBIGSERIAL\\b", "BIGINT AUTO_INCREMENT");
            logs.add("INFO: Temporarily converted BIGSERIAL to BIGINT AUTO_INCREMENT for MySQL parser compatibility");
        }

        // 处理SERIAL类型 - 临时转换为INT AUTO_INCREMENT让MySQL解析器能理解
        // 在神通生成器中会再转换回SERIAL格式
        Pattern serialPattern = Pattern.compile("(?i)\\bSERIAL\\b", Pattern.MULTILINE);
        Matcher serialMatcher = serialPattern.matcher(result);
        if (serialMatcher.find()) {
            result = result.replaceAll("(?i)\\bSERIAL\\b", "INT AUTO_INCREMENT");
            logs.add("INFO: Temporarily converted SERIAL to INT AUTO_INCREMENT for MySQL parser compatibility");
        }

        // 【新增】处理神通数据库特有函数和语法 - 临时转换为MySQL兼容语法
        // 根据神通数据库官方文档，需要处理SYSDATE、dual表等特有语法
        if ("shentong".equalsIgnoreCase(targetDialect)) {
            result = preprocessShentongSpecificSyntax(result, logs);
        }

        // 处理神通数据库特有类型 - 临时转换为MySQL兼容类型让解析器能理解
        // 在神通生成器中会再转换回原始格式

        // 处理NAME类型 - 只在CREATE TABLE的列定义上下文中替换
        // 使用更精确的模式，确保只在数据类型位置替换，不影响列名或其他地方的name
        Pattern nameTypePattern = Pattern.compile("(?i)\\b(\\w+)\\s+NAME\\s*(?=[,\\s)]|$)", Pattern.MULTILINE);
        Matcher nameTypeMatcher = nameTypePattern.matcher(result);
        StringBuffer nameBuffer = new StringBuffer();
        boolean nameFound = false;
        while (nameTypeMatcher.find()) {
            // 检查是否在CREATE TABLE的列定义上下文中
            String beforeMatch = result.substring(Math.max(0, nameTypeMatcher.start() - 200), nameTypeMatcher.start());
            if (beforeMatch.toUpperCase().contains("CREATE TABLE") &&
                (beforeMatch.contains("(") || beforeMatch.contains(","))) {
                String columnName = nameTypeMatcher.group(1);
                nameTypeMatcher.appendReplacement(nameBuffer, columnName + " VARCHAR(127)");
                nameFound = true;
            } else {
                nameTypeMatcher.appendReplacement(nameBuffer, nameTypeMatcher.group());
            }
        }
        nameTypeMatcher.appendTail(nameBuffer);
        if (nameFound) {
            result = nameBuffer.toString();
            logs.add("INFO: Temporarily converted NAME data type to VARCHAR(127) for MySQL parser compatibility");
        }

        // 处理"CHAR"类型 - 需要在双引号转换为反引号之前处理
        // 神通数据库的"CHAR"是特殊类型，需要特殊处理
        if (result.contains("\"CHAR\"")) {
            // 使用更简单直接的替换方式
            result = result.replaceAll("\"CHAR\"", "CHAR(1)");
            logs.add("INFO: Temporarily converted \"CHAR\" data type to CHAR(1) for MySQL parser compatibility");
        }

        // 处理VARBIT类型 - 使用精确的上下文感知替换
        Pattern varbitTypePattern = Pattern.compile("(?i)\\b(\\w+)\\s+(VARBIT)\\s*\\(", Pattern.MULTILINE);
        Matcher varbitTypeMatcher = varbitTypePattern.matcher(result);
        if (varbitTypeMatcher.find()) {
            result = varbitTypeMatcher.replaceAll("$1 BIT(");
            logs.add("INFO: Temporarily converted VARBIT data type to BIT for MySQL parser compatibility");
        }

        return result;
    }

    /**
     * 预处理神通数据库特有函数和语法
     * 根据神通数据库官方文档，将神通特有语法转换为MySQL兼容语法
     *
     * 主要处理：
     * 1. SYSDATE函数 → NOW()函数
     * 2. dual表 → 保持不变（MySQL解析器需要特殊处理）
     * 3. 其他神通特有函数
     */
    private static String preprocessShentongSpecificSyntax(String sql, List<String> logs) {
        String result = sql;

        // 处理SYSDATE函数 - 转换为MySQL兼容的NOW()函数
        // 根据神通数据库官方文档，SYSDATE返回当前系统日期和时间
        Pattern sysdatePattern = Pattern.compile("(?i)\\bSYSDATE\\b", Pattern.MULTILINE);
        Matcher sysdateMatcher = sysdatePattern.matcher(result);
        if (sysdateMatcher.find()) {
            result = result.replaceAll("(?i)\\bSYSDATE\\b", "NOW()");
            logs.add("INFO: Temporarily converted Shentong SYSDATE to MySQL NOW() for parser compatibility");
        }

        // 处理dual表 - 转换为MySQL兼容的虚拟表
        // MySQL没有dual表，但我们可以创建一个等效的子查询
        Pattern dualPattern = Pattern.compile("(?i)\\bFROM\\s+dual\\b", Pattern.MULTILINE);
        Matcher dualMatcher = dualPattern.matcher(result);
        if (dualMatcher.find()) {
            // 将FROM dual转换为FROM (SELECT 1) AS dual_table
            // 这样MySQL解析器可以理解，在生成器中再转换回FROM dual
            result = result.replaceAll("(?i)\\bFROM\\s+dual\\b", "FROM (SELECT 1) AS dual_table");
            logs.add("INFO: Temporarily converted Shentong 'FROM dual' to MySQL compatible subquery for parser compatibility");
        }

        // 处理其他神通特有函数
        // 根据神通数据库官方文档，还有其他一些特有函数需要处理

        // TRUNC函数（日期截断）- 转换为DATE函数
        Pattern truncPattern = Pattern.compile("(?i)\\bTRUNC\\s*\\(\\s*SYSDATE\\s*\\)", Pattern.MULTILINE);
        Matcher truncMatcher = truncPattern.matcher(result);
        if (truncMatcher.find()) {
            result = result.replaceAll("(?i)\\bTRUNC\\s*\\(\\s*SYSDATE\\s*\\)", "CURDATE()");
            logs.add("INFO: Temporarily converted Shentong TRUNC(SYSDATE) to MySQL CURDATE() for parser compatibility");
        }

        // TO_CHAR函数（格式化）- 转换为DATE_FORMAT函数
        Pattern toCharPattern = Pattern.compile("(?i)\\bTO_CHAR\\s*\\(\\s*SYSDATE\\s*,\\s*'([^']+)'\\s*\\)", Pattern.MULTILINE);
        Matcher toCharMatcher = toCharPattern.matcher(result);
        if (toCharMatcher.find()) {
            result = toCharPattern.matcher(result).replaceAll(matchResult -> {
                String format = matchResult.group(1);
                // 简单的格式转换，实际应用中可能需要更复杂的映射
                String mysqlFormat = convertOracleFormatToMySQL(format);
                return "DATE_FORMAT(NOW(), '" + mysqlFormat + "')";
            });
            logs.add("INFO: Temporarily converted Shentong TO_CHAR(SYSDATE, format) to MySQL DATE_FORMAT() for parser compatibility");
        }

        return result;
    }

    /**
     * 将Oracle/神通的日期格式转换为MySQL格式
     * 这是一个简化的转换，实际应用中可能需要更完整的映射
     */
    private static String convertOracleFormatToMySQL(String oracleFormat) {
        String mysqlFormat = oracleFormat;

        // 基本的格式转换
        mysqlFormat = mysqlFormat.replaceAll("(?i)YYYY", "%Y");
        mysqlFormat = mysqlFormat.replaceAll("(?i)MM", "%m");
        mysqlFormat = mysqlFormat.replaceAll("(?i)DD", "%d");
        mysqlFormat = mysqlFormat.replaceAll("(?i)HH24", "%H");
        mysqlFormat = mysqlFormat.replaceAll("(?i)MI", "%i");
        mysqlFormat = mysqlFormat.replaceAll("(?i)SS", "%s");

        return mysqlFormat;
    }

    /**
     * Remove all MySQL comments including single-line, multi-line and hash comments
     * Uses simple and safe patterns that won't interfere with string content
     *
     * @param sql 原始SQL内容
     * @param logs 日志列表
     * @param preserveComments 是否保留COMMENT子句（如果为false，会移除所有注释包括COMMENT子句）
     */
    private static String removeAllComments(String sql, List<String> logs, boolean preserveComments) {
        String result = sql;
        int removedCount = 0;

        // 如果不保留注释，则移除所有注释（包括COMMENT子句）
        // 如果保留注释，则只移除真正的注释（-- /* # 等），保留COMMENT子句
        if (!preserveComments) {
            logs.add("COMMENT_REMOVAL: Removing all comments including COMMENT clauses");
            // 移除COMMENT子句 - 这会影响DDL语句的注释部分
            result = removeCommentClauses(result, logs);
        } else {
            logs.add("COMMENT_REMOVAL: Preserving COMMENT clauses, removing only comment syntax");
        }

        // 1. 移除单行注释（-- 注释到行尾）
        String[] lines = result.split("\n");
        StringBuilder sb = new StringBuilder();
        int currentPos = 0;

        for (String line : lines) {
            // 查找所有 -- 注释的位置，处理同一行中的多个注释
            int searchStart = 0;
            boolean foundComment = false;

            while (true) {
                int commentPos = line.indexOf("--", searchStart);
                if (commentPos == -1) break;

                // 计算在整个SQL中的绝对位置
                int absoluteCommentPos = currentPos + commentPos;
                // 检查 -- 是否在字符串内
                if (!isInsideString(result, absoluteCommentPos)) {
                    // 找到真正的注释，移除从这里到行尾的所有内容
                    String beforeComment = line.substring(0, commentPos).trim();
                    String comment = line.substring(commentPos).trim();
                    if (!comment.isEmpty()) {
                        logs.add("COMMENT_REMOVAL: Removed single-line comment: " + comment);
                        removedCount++;
                    }
                    if (!beforeComment.isEmpty()) {
                        sb.append(beforeComment).append("\n");
                    }
                    foundComment = true;
                    break;
                } else {
                    // 在字符串内，继续查找下一个
                    searchStart = commentPos + 2;
                }
            }

            if (!foundComment) {
                sb.append(line).append("\n");
            }

            currentPos += line.length() + 1; // +1 for the newline character
        }
        result = sb.toString();

        // 2. 移除多行注释（/* 注释 */）- 使用更安全的方法避免 StackOverflowError
        if (result.length() > 100000) { // 对于大文件使用简化处理
            logs.add("COMMENT_REMOVAL: Large file detected, using simplified multi-line comment removal");
            result = removeMultiLineCommentsSafely(result, logs);
        } else {
            // 原有的处理逻辑，适用于小文件
            int searchStart = 0;
            while (true) {
                int startPos = result.indexOf("/*", searchStart);
                if (startPos == -1) break;

                int endPos = result.indexOf("*/", startPos);
                if (endPos == -1) break;

                // 检查 /* 和 */ 是否都在字符串外
                boolean startInString = isInsideString(result, startPos);
                boolean endInString = isInsideString(result, endPos);

                if (!startInString && !endInString) {
                    // 两个标记都在字符串外，检查是否是特殊的类型标记注释
                    String comment = result.substring(startPos, endPos + 2);

                    // 保留特殊的类型标记注释，不移除
                    if (comment.contains("ORIGINAL_TYPE:")) {
                        logs.add("COMMENT_REMOVAL: Preserved type marker comment: " + comment.replaceAll("\\s+", " "));
                        searchStart = endPos + 2;
                    } else {
                        // 普通注释，正常移除
                        logs.add("COMMENT_REMOVAL: Removed multi-line comment: " + comment.replaceAll("\\s+", " "));
                        result = result.substring(0, startPos) + result.substring(endPos + 2);
                        removedCount++;
                        // 从删除位置重新开始搜索
                        searchStart = startPos;
                    }
                } else {
                    // 如果任一标记在字符串内，跳过这个注释，从下一个位置继续搜索
                    searchStart = startPos + 2;
                }
            }
        }

        // 3. 移除哈希注释（# 注释到行尾）
        lines = result.split("\n");
        sb = new StringBuilder();
        currentPos = 0;

        for (String line : lines) {
            int commentPos = line.indexOf("#");
            if (commentPos >= 0) {
                // 计算在整个SQL中的绝对位置
                int absoluteCommentPos = currentPos + commentPos;
                if (!isInsideString(result, absoluteCommentPos)) {
                    String beforeComment = line.substring(0, commentPos).trim();
                    String comment = line.substring(commentPos).trim();
                    if (!comment.isEmpty()) {
                        logs.add("COMMENT_REMOVAL: Removed hash comment: " + comment);
                        removedCount++;
                    }
                    if (!beforeComment.isEmpty()) {
                        sb.append(beforeComment).append("\n");
                    }
                } else {
                    sb.append(line).append("\n");
                }
            } else {
                sb.append(line).append("\n");
            }
            currentPos += line.length() + 1; // +1 for the newline character
        }
        result = sb.toString();

        logs.add("COMMENT_REMOVAL: Total comments removed: " + removedCount);
        return result;
    }

    /**
     * 安全地移除多行注释，避免在大文件中导致 StackOverflowError
     * 使用简化的字符串处理，避免复杂的正则表达式
     */
    private static String removeMultiLineCommentsSafely(String sql, List<String> logs) {
        StringBuilder result = new StringBuilder();
        int i = 0;
        int removedCount = 0;

        while (i < sql.length()) {
            // 查找下一个 /*
            int startPos = sql.indexOf("/*", i);
            if (startPos == -1) {
                // 没有更多注释，添加剩余内容
                result.append(sql.substring(i));
                break;
            }

            // 添加注释前的内容
            result.append(sql.substring(i, startPos));

            // 查找对应的 */
            int endPos = sql.indexOf("*/", startPos + 2);
            if (endPos == -1) {
                // 没有找到结束标记，保留原内容
                result.append(sql.substring(startPos));
                break;
            }

            // 简化的字符串内检查：检查注释前的最近100个字符中的引号数量
            String beforeComment = sql.substring(Math.max(0, startPos - 100), startPos);
            long singleQuotes = beforeComment.chars().filter(ch -> ch == '\'').count();
            long doubleQuotes = beforeComment.chars().filter(ch -> ch == '"').count();

            // 如果引号数量是奇数，可能在字符串内
            boolean likelyInString = (singleQuotes % 2 != 0) || (doubleQuotes % 2 != 0);

            if (likelyInString) {
                // 可能在字符串内，保留注释
                result.append(sql.substring(startPos, endPos + 2));
            } else {
                // 不在字符串内，检查是否是特殊的类型标记注释
                String comment = sql.substring(startPos, endPos + 2);

                if (comment.contains("ORIGINAL_TYPE:")) {
                    // 保留特殊的类型标记注释
                    logs.add("COMMENT_REMOVAL: Preserved type marker comment: " + comment.replaceAll("\\s+", " "));
                    result.append(comment);
                } else {
                    // 普通注释，正常移除
                    String shortComment = sql.substring(startPos, Math.min(endPos + 2, startPos + 100));
                    logs.add("COMMENT_REMOVAL: Removed multi-line comment: " + shortComment.replaceAll("\\s+", " "));
                    removedCount++;
                    // 注释被移除，不添加到结果中
                }
            }

            i = endPos + 2;
        }

        if (removedCount > 0) {
            logs.add("COMMENT_REMOVAL: Safely removed " + removedCount + " multi-line comments");
        }

        return result.toString();
    }

    /**
     * Check if a position in a string is inside a string literal
     * This method handles the entire SQL text, not just a single line
     */
    private static boolean isInsideString(String sql, int position) {
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        boolean escaped = false;

        for (int i = 0; i < position && i < sql.length(); i++) {
            char c = sql.charAt(i);

            if (escaped) {
                escaped = false;
                continue;
            }

            if (c == '\\') {
                escaped = true;
                continue;
            }

            if (c == '\'' && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
            } else if (c == '"' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
            }
        }

        return inSingleQuote || inDoubleQuote;
    }

    /**
     * 检查指定位置是否在JSON字符串中
     * JSON字符串通常出现在DEFAULT子句中，如：DEFAULT '{"key": "value"}'
     */
    private static boolean isInJsonString(String sql, int position) {
        // 查找包含当前位置的字符串字面量
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;
        boolean escaped = false;
        int stringStart = -1;
        int stringEnd = -1;

        // 找到包含当前位置的字符串字面量的边界
        for (int i = 0; i < sql.length(); i++) {
            char c = sql.charAt(i);

            if (escaped) {
                escaped = false;
                continue;
            }

            if (c == '\\') {
                escaped = true;
                continue;
            }

            if (c == '\'' && !inDoubleQuote) {
                if (!inSingleQuote) {
                    stringStart = i;
                } else {
                    stringEnd = i;
                    // 检查当前位置是否在这个字符串内
                    if (position > stringStart && position < stringEnd) {
                        // 检查这个字符串是否是JSON格式
                        String stringContent = sql.substring(stringStart + 1, stringEnd);
                        return isJsonFormat(stringContent);
                    }
                }
                inSingleQuote = !inSingleQuote;
            } else if (c == '"' && !inSingleQuote) {
                if (!inDoubleQuote) {
                    stringStart = i;
                } else {
                    stringEnd = i;
                    // 检查当前位置是否在这个字符串内
                    if (position > stringStart && position < stringEnd) {
                        // 检查这个字符串是否是JSON格式
                        String stringContent = sql.substring(stringStart + 1, stringEnd);
                        return isJsonFormat(stringContent);
                    }
                }
                inDoubleQuote = !inDoubleQuote;
            }
        }

        return false;
    }

    /**
     * 简单检查字符串是否可能是JSON格式
     */
    private static boolean isJsonFormat(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }

        content = content.trim();

        // 检查是否以JSON对象或数组的格式开始和结束
        return (content.startsWith("{") && content.endsWith("}")) ||
               (content.startsWith("[") && content.endsWith("]"));
    }

    /**
     * 清理多余的空白字符和空行
     */
    private static String cleanWhitespace(String sql, List<String> logs) {
        String original = sql;

        // 1. 移除多余的空行（连续的空行合并为单个空行）
        String result = sql.replaceAll("\\n\\s*\\n\\s*\\n", "\n\n");

        // 2. 移除行首和行尾的空白字符
        result = result.replaceAll("(?m)^\\s+|\\s+$", "");

        // 3. 移除文件开头和结尾的空行
        result = result.replaceAll("^\\s+|\\s+$", "");

        // 4. 标准化行分隔符
        result = result.replaceAll("\\r\\n|\\r", "\n");

        int originalLines = original.split("\\n").length;
        int finalLines = result.split("\\n").length;

        if (originalLines != finalLines) {
            logs.add("WHITESPACE_CLEANUP: Removed " + (originalLines - finalLines) + " empty lines");
        }

        if (original.length() != result.length()) {
            logs.add("WHITESPACE_CLEANUP: Removed " + (original.length() - result.length()) + " whitespace characters");
        }

        return result;
    }

    /**
     * Clean invalid characters and symbols that may cause parsing issues
     * This method is conservative and only removes truly invalid characters,
     * not content within SQL strings which may contain special characters
     */
    private static String cleanInvalidCharacters(String sql, List<String> logs) {
        String original = sql;
        String result = sql;
        int removedCount = 0;

        // Only remove control characters that are truly invalid in SQL
        // Do NOT remove characters that might be valid within string literals

        // 1. 移除控制字符（除了换行符、制表符、回车符）
        // 但要小心不要破坏字符串内容
        String beforeControl = result;
        result = result.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");
        if (!beforeControl.equals(result)) {
            removedCount++;
            logs.add("INVALID_CHAR_CLEANUP: Removed control characters");
        }

        // 2. 移除非打印字符（保留基本的ASCII和UTF-8字符）
        // 但保留在字符串字面量中可能有效的字符
        String beforeNonPrint = result;
        result = result.replaceAll("[\\p{Cntrl}&&[^\r\n\t]]", "");
        if (!beforeNonPrint.equals(result)) {
            removedCount++;
            logs.add("INVALID_CHAR_CLEANUP: Removed non-printable characters");
        }

        if (removedCount > 0) {
            logs.add("INVALID_CHAR_CLEANUP: Total invalid character patterns removed: " + removedCount);
            logs.add("INVALID_CHAR_CLEANUP: Character count reduced from " + original.length() + " to " + result.length());
        } else {
            logs.add("INVALID_CHAR_CLEANUP: No invalid characters found");
        }

        return result;
    }

    private static String fixOrphanedKeywordsAsColumns(String sql, List<String> logs) {
        // 这个方法有严重的bug，会把SQL语句开头的关键字（如DELETE、SELECT）错误地替换为INT NULL
        // 暂时禁用这个方法，避免破坏SQL语义
        logs.add("fixOrphanedKeywordsAsColumns: Disabled due to bug that corrupts SQL statements");
        return sql;
    }

    /**
     * 将双引号标识符转换为反引号（MySQL标准）
     *
     * 某些SQL可能使用双引号来引用标识符（如PostgreSQL风格），
     * 但MySQL标准使用反引号，所以需要转换：
     * "identifier" -> `identifier`
     * "schema"."table" -> `schema`.`table`
     */
    private static String convertDoubleQuotesToBackticks(String sql, List<String> logs) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        // 对于大文件，使用简化的双引号处理避免 StackOverflowError
        if (sql.length() > 100000) {
            logs.add("INFO: Large file detected, using simplified double-quote processing");
            return convertDoubleQuotesSimplified(sql, logs);
        }

        // 匹配双引号包围的标识符，使用更安全的正则表达式
        // 限制匹配长度避免过度回溯
        Pattern doubleQuotePattern = Pattern.compile("\"([^\"]{0,200})\"");
        Matcher matcher = doubleQuotePattern.matcher(sql);

        StringBuffer result = new StringBuffer();
        int count = 0;

        while (matcher.find()) {
            String content = matcher.group(1);

            // 检查是否是有效的标识符（不是字符串值）
            if (isValidIdentifierForConversion(content, sql, matcher.start())) {
                String backtickIdentifier = "`" + content + "`";
                matcher.appendReplacement(result, backtickIdentifier);
                logs.add("INFO: Converted double-quoted identifier \"" + content + "\" to backtick `" + content + "`");
                count++;
            } else {
                // 保持原样（可能是字符串值）
                // 使用Matcher.quoteReplacement确保特殊字符不被解释
                matcher.appendReplacement(result, Matcher.quoteReplacement(matcher.group()));
            }
        }

        matcher.appendTail(result);

        if (count > 0) {
            logs.add("INFO: Converted " + count + " double-quoted identifiers to backticks");
        }

        return result.toString();
    }

    /**
     * 简化的双引号转换方法，用于大文件，避免 StackOverflowError
     */
    private static String convertDoubleQuotesSimplified(String sql, List<String> logs) {
        StringBuilder result = new StringBuilder();
        int i = 0;
        int count = 0;

        while (i < sql.length()) {
            char c = sql.charAt(i);

            if (c == '"') {
                // 查找匹配的结束双引号
                int start = i;
                i++; // 跳过开始的双引号
                StringBuilder content = new StringBuilder();
                boolean foundEnd = false;

                while (i < sql.length()) {
                    char next = sql.charAt(i);
                    if (next == '"') {
                        foundEnd = true;
                        i++; // 跳过结束的双引号
                        break;
                    } else if (next == '\\' && i + 1 < sql.length()) {
                        // 处理转义字符
                        content.append(next);
                        i++;
                        if (i < sql.length()) {
                            content.append(sql.charAt(i));
                            i++;
                        }
                    } else {
                        content.append(next);
                        i++;
                    }
                }

                if (foundEnd) {
                    String contentStr = content.toString();
                    // 简化的标识符检查
                    if (isSimpleIdentifier(contentStr)) {
                        result.append("`").append(contentStr).append("`");
                        logs.add("INFO: Converted double-quoted identifier \"" + contentStr + "\" to backtick `" + contentStr + "`");
                        count++;
                    } else {
                        // 保持原样
                        result.append(sql, start, i);
                    }
                } else {
                    // 没有找到结束引号，保持原样
                    result.append(sql, start, i);
                }
            } else {
                result.append(c);
                i++;
            }
        }

        if (count > 0) {
            logs.add("INFO: Simplified conversion processed " + count + " double-quoted identifiers");
        }

        return result.toString();
    }

    /**
     * 简化的标识符检查
     */
    private static boolean isSimpleIdentifier(String content) {
        if (content == null || content.trim().isEmpty()) return false;

        String trimmed = content.trim();
        if (trimmed.length() > 64) return false; // 标识符长度限制

        // 简单检查：只包含字母、数字、下划线，且以字母或下划线开头
        if (!Character.isLetter(trimmed.charAt(0)) && trimmed.charAt(0) != '_') return false;

        for (int i = 1; i < trimmed.length(); i++) {
            char c = trimmed.charAt(i);
            if (!Character.isLetterOrDigit(c) && c != '_') return false;
        }

        return true;
    }

    /**
     * 检查双引号内容是否是标识符（而不是字符串值）
     */
    private static boolean isValidIdentifierForConversion(String content, String sql, int position) {
        // 基本检查：分隔标识符可以包含任何字符（除了双引号本身）
        // 根据神通数据库官方文档，分隔标识符可以包含空格、特殊字符等
        if (content == null || content.trim().isEmpty()) {
            return false;
        }

        // 如果内容包含双引号，检查是否是转义的
        if (content.contains("\"")) {
            // 检查是否包含转义的双引号（C风格 \" 或 SQL风格 ""）
            if (!content.contains("\\\"") && !content.contains("\"\"")) {
                // 包含未转义的双引号，很可能是字符串值而不是标识符
                return false;
            }
        }

        // 获取更大的上下文窗口进行分析，确保能包含关键字
        int contextStart = Math.max(0, position - 200); // 增加上下文窗口
        int contextEnd = Math.min(sql.length(), position + content.length() + 50);
        String beforeContext = sql.substring(contextStart, position).toUpperCase();
        String afterContext = sql.substring(position + content.length() + 2, contextEnd).toUpperCase(); // +2 for quotes

        // 检查是否在VALUES子句中的括号内
        if (isInValuesClause(beforeContext, afterContext)) {
            return false;
        }

        // 检查是否在SET子句的等号右边（值的位置）
        if (isInSetValuePosition(beforeContext, afterContext)) {
            return false;
        }

        // 检查是否在WHERE子句的比较操作符右边（值的位置）
        if (isInWhereValuePosition(beforeContext, afterContext)) {
            return false;
        }

        // 检查是否在COMMENT子句中
        if (isInCommentClause(beforeContext, afterContext)) {
            return false;
        }

        // 检查是否在JSON字符串中（DEFAULT子句中的JSON值）
        if (isInJsonString(sql, position)) {
            return false;
        }

        // 检查是否在DEFAULT子句中
        if (isInDefaultClause(beforeContext, afterContext)) {
            return false;
        }

        // 其他情况认为是标识符
        return true;
    }

    /**
     * 检查是否在VALUES子句中
     */
    private static boolean isInValuesClause(String beforeContext, String afterContext) {
        // 查找最近的VALUES关键字
        int valuesIndex = beforeContext.toUpperCase().lastIndexOf("VALUES");
        if (valuesIndex == -1) {
            return false;
        }

        // 检查VALUES后面是否有开括号，且当前位置在括号内
        String afterValues = beforeContext.substring(valuesIndex + 6);

        // 计算括号的平衡状态，同时忽略字符串内的括号
        int parenBalance = 0;
        boolean foundOpenParen = false;
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;

        for (int i = 0; i < afterValues.length(); i++) {
            char c = afterValues.charAt(i);
            char prev = i > 0 ? afterValues.charAt(i - 1) : '\0';

            // 处理字符串状态
            if (c == '\'' && prev != '\\' && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
            } else if (c == '"' && prev != '\\' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
            }

            // 只在字符串外计算括号平衡
            if (!inSingleQuote && !inDoubleQuote) {
                if (c == '(') {
                    parenBalance++;
                    foundOpenParen = true;
                } else if (c == ')') {
                    parenBalance--;
                }
            }
        }

        // 如果找到了开括号且括号没有完全闭合，说明在VALUES的括号内
        return foundOpenParen && parenBalance > 0;
    }

    /**
     * 检查是否在SET子句的值位置
     */
    private static boolean isInSetValuePosition(String beforeContext, String afterContext) {
        // 查找最近的等号
        int lastEquals = beforeContext.lastIndexOf('=');
        if (lastEquals == -1) {
            return false;
        }

        // 检查等号前面是否有SET关键字
        String beforeEquals = beforeContext.substring(0, lastEquals).trim();
        if (beforeEquals.toUpperCase().contains("SET")) {
            // 检查等号后面是否紧跟着当前的双引号位置
            String afterEquals = beforeContext.substring(lastEquals + 1).trim();
            return afterEquals.isEmpty(); // 如果等号后面直接是双引号，说明是值
        }

        return false;
    }

    /**
     * 检查是否在WHERE子句的值位置
     */
    private static boolean isInWhereValuePosition(String beforeContext, String afterContext) {
        // 查找最近的比较操作符
        String[] operators = {"=", "!=", "<>", "<", ">", "<=", ">=", "LIKE", "IN"};
        int lastOperatorPos = -1;

        for (String op : operators) {
            int pos = beforeContext.lastIndexOf(op);
            if (pos > lastOperatorPos) {
                lastOperatorPos = pos;
            }
        }

        if (lastOperatorPos == -1) {
            return false;
        }

        // 检查操作符前面是否有WHERE关键字
        String beforeOperator = beforeContext.substring(0, lastOperatorPos).trim();
        if (beforeOperator.contains("WHERE")) {
            // 检查操作符后面是否紧跟着当前的双引号位置
            String afterOperator = beforeContext.substring(lastOperatorPos + 1).trim();
            return afterOperator.isEmpty(); // 如果操作符后面直接是双引号，说明是值
        }

        return false;
    }

    /**
     * 检查是否在COMMENT子句中
     */
    private static boolean isInCommentClause(String beforeContext, String afterContext) {
        int commentIndex = beforeContext.lastIndexOf("COMMENT");
        if (commentIndex == -1) {
            return false;
        }

        // 检查COMMENT后面是否紧跟着当前的双引号位置
        String afterComment = beforeContext.substring(commentIndex + 7).trim();
        return afterComment.isEmpty();
    }

    /**
     * 检查是否在DEFAULT子句中
     */
    private static boolean isInDefaultClause(String beforeContext, String afterContext) {
        int defaultIndex = beforeContext.lastIndexOf("DEFAULT");
        if (defaultIndex == -1) {
            return false;
        }

        // 检查DEFAULT后面是否紧跟着当前的双引号位置
        String afterDefault = beforeContext.substring(defaultIndex + 7).trim();
        return afterDefault.isEmpty();
    }

    /**
     * 标准化反引号标识符
     *
     * MySQL标准使用反引号来引用标识符：`identifier`
     * 由于当前ANTLR配置在处理反引号时有问题，我们采用以下策略：
     * 1. 识别所有反引号标识符
     * 2. 移除反引号，让ANTLR能够正常解析
     * 3. 记录这些标识符需要在目标数据库中被引用
     * 4. 在Generator中根据目标数据库的标准添加适当的引号
     *
     * 这样既保持了MySQL语义，又避免了ANTLR解析错误
     */
    private static String normalizeBacktickIdentifiers(String sql, List<String> logs) {
        // 修复策略：智能处理反引号
        // 1. 移除SQL语法关键字的反引号（如SELECT、FROM、WHERE等）
        // 2. 保留标识符中保留关键字的反引号（如列名`select`、`order`等）
        // 3. 移除非保留关键字的反引号

        Pattern backtickPattern = Pattern.compile("`([^`]+)`");
        Matcher matcher = backtickPattern.matcher(sql);
        StringBuffer result = new StringBuffer();
        int removedCount = 0;
        int preservedCount = 0;

        while (matcher.find()) {
            String identifier = matcher.group(1);
            int position = matcher.start();

            // 优先检查是否是MySQL保留关键字（用作标识符时必须被引用）
            if (isMySqlReservedKeyword(identifier)) {
                // 标识符中的保留关键字保持反引号
                matcher.appendReplacement(result, "`" + identifier + "`");
                preservedCount++;
                logs.add("PREPROCESSING: Preserved backticks for reserved keyword identifier: " + identifier);
            } else if (isSqlSyntaxKeyword(identifier, sql, position)) {
                // SQL语法关键字移除反引号（只有在不是保留关键字的情况下）
                matcher.appendReplacement(result, identifier);
                removedCount++;
                logs.add("PREPROCESSING: Removed backticks from SQL syntax keyword: " + identifier);
            } else {
                // 用户明确添加的反引号应该保留（可能是为了避免与未来关键字冲突或其他原因）
                matcher.appendReplacement(result, "`" + identifier + "`");
                preservedCount++;
                logs.add("PREPROCESSING: Preserved backticks for user-defined identifier: " + identifier);
            }
        }

        matcher.appendTail(result);

        if (removedCount > 0) {
            logs.add("PREPROCESSING: Removed " + removedCount + " backtick identifiers to enable ANTLR parsing");
        }
        if (preservedCount > 0) {
            logs.add("PREPROCESSING: Preserved " + preservedCount + " backticks for reserved keyword identifiers");
        }
        if (removedCount > 0 || preservedCount > 0) {
            logs.add("INFO: Generators will add appropriate quotes per target database standards");
        }

        return result.toString();
    }

    /**
     * 检查标识符是否是MySQL保留关键字
     * 根据MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/keywords.html
     */
    private static boolean isMySqlReservedKeyword(String identifier) {
        if (identifier == null) {
            return false;
        }

        String upperIdentifier = identifier.toUpperCase();

        // MySQL保留关键字列表（根据MySQL 8.4官方文档）
        // 这些关键字在用作标识符时必须被引用
        // 使用统一的保留字管理系统
        return com.xylink.sqltranspiler.common.constants.ReservedWords.isReservedWord(upperIdentifier, "mysql");
    }

    /**
     * 检查标识符是否是SQL语法关键字（不应该被引用的关键字）
     * 这些关键字是SQL语法的一部分，不是用户定义的标识符
     */
    private static boolean isSqlSyntaxKeyword(String identifier, String sql, int position) {
        if (identifier == null) {
            return false;
        }

        String upperIdentifier = identifier.toUpperCase();

        // 使用统一的SQL关键字管理系统
        Set<String> sqlSyntaxKeywords = com.xylink.sqltranspiler.common.constants.SqlKeywords.getAllKeywords();

        if (!sqlSyntaxKeywords.contains(upperIdentifier)) {
            return false;
        }

        // 进一步检查上下文，确保这确实是SQL语法关键字而不是标识符
        // 例如：SELECT语句中的SELECT关键字 vs 列名`select`
        return isInSqlSyntaxContext(upperIdentifier, sql, position);
    }

    /**
     * 检查关键字是否在SQL语法上下文中（而不是作为标识符）
     */
    private static boolean isInSqlSyntaxContext(String keyword, String sql, int position) {
        // 获取关键字前后的上下文
        int start = Math.max(0, position - 100);
        int end = Math.min(sql.length(), position + keyword.length() + 100);
        String context = sql.substring(start, end).toUpperCase();

        // 首先检查是否在CREATE TABLE的列定义中
        // 在列定义中，所有关键字都应该被视为标识符
        if (isInCreateTableColumnDefinition(context, keyword, position - start)) {
            return false; // 在列定义中，不是SQL语法关键字
        }

        // 使用更安全的字符串检查，避免复杂正则表达式导致 StackOverflowError
        switch (keyword) {
            case "SELECT":
                // SELECT应该出现在语句开始或AS后面
                return context.contains("SELECT");
            case "FROM":
                // FROM应该出现在SELECT子句后面
                return context.contains("SELECT") && context.contains("FROM");
            case "WHERE":
                // WHERE应该出现在FROM子句后面
                return (context.contains("FROM") || context.contains("JOIN")) && context.contains("WHERE");
            case "ORDER":
                // ORDER应该与BY一起出现
                return context.contains("ORDER") && context.contains("BY");
            case "BY":
                // BY应该在ORDER或GROUP后面
                return (context.contains("ORDER") || context.contains("GROUP")) && context.contains("BY");
            case "GROUP":
                // GROUP应该与BY一起出现
                return context.contains("GROUP") && context.contains("BY");
            case "TABLE":
                // TABLE应该在CREATE TABLE或ALTER TABLE中
                return (context.contains("CREATE") || context.contains("ALTER") || context.contains("DROP")) && context.contains("TABLE");
            case "INDEX":
                // INDEX应该在CREATE INDEX或DROP INDEX中
                return (context.contains("CREATE") || context.contains("DROP")) && context.contains("INDEX");
            default:
                // 对于其他关键字，检查是否在列定义中
                return !isInCreateTableColumnDefinition(context, keyword, position - start);
        }
    }

    /**
     * 检查关键字是否在CREATE TABLE的列定义中
     */
    private static boolean isInCreateTableColumnDefinition(String context, String keyword, int relativePosition) {
        // 检查是否在CREATE TABLE语句中
        if (!context.contains("CREATE TABLE")) {
            return false;
        }

        // 查找CREATE TABLE的开始和结束位置
        int createTableStart = context.indexOf("CREATE TABLE");
        int openParen = context.indexOf("(", createTableStart);
        int closeParen = context.lastIndexOf(")");

        if (openParen == -1 || closeParen == -1 || relativePosition < openParen || relativePosition > closeParen) {
            return false;
        }

        // 检查关键字是否在列定义的位置
        // 列定义的模式：column_name data_type [constraints]
        String afterKeyword = context.substring(relativePosition, Math.min(context.length(), relativePosition + 50));

        // 如果关键字后面跟着数据类型（INT, VARCHAR等），说明它是列名
        // 使用简单的字符串检查避免复杂正则表达式
        String upperAfter = afterKeyword.toUpperCase();
        return upperAfter.contains(" INT") || upperAfter.contains(" VARCHAR") || upperAfter.contains(" CHAR") ||
               upperAfter.contains(" TEXT") || upperAfter.contains(" DECIMAL") || upperAfter.contains(" FLOAT") ||
               upperAfter.contains(" DOUBLE") || upperAfter.contains(" BOOLEAN") || upperAfter.contains(" DATE") ||
               upperAfter.contains(" TIME") || upperAfter.contains(" TIMESTAMP") || upperAfter.contains(" BLOB");
    }



    /**
     * 智能为标识符添加反引号
     * 只在真正的标识符位置（schema、table、column名）判断是否是关键字来决定是否加反引号
     * 遵循MySQL官方文档标准：https://dev.mysql.com/doc/refman/8.4/en/identifiers.html
     */
    private static String addBackticksToAllIdentifiers(String sql, List<String> logs) {
        logs.add("DEBUG: addBackticksToAllIdentifiers - Starting with SQL length: " + sql.length());
        String result = sql;

        // 基于SQL语句类型进行精确的标识符处理
        // 只处理真正的标识符位置，不处理SQL语法关键字
        // 使用更精确的语句识别，避免多语句时的错误匹配

        result = processStatementSpecificIdentifiers(result, logs);

        logs.add("DEBUG: addBackticksToAllIdentifiers - Finished with SQL length: " + result.length());
        return result;
    }

    /**
     * 智能处理不同类型SQL语句中的标识符
     * 避免多语句时的错误匹配，只为真正的标识符位置添加反引号
     */
    private static String processStatementSpecificIdentifiers(String sql, List<String> logs) {
        String result = sql;

        // 分析SQL中包含的语句类型，使用更安全的字符串检查避免 StackOverflowError
        String upperSql = sql.toUpperCase();

        // 1. 处理CREATE TABLE语句（使用简单的字符串包含检查）
        if (upperSql.contains("CREATE TABLE")) {
            logs.add("DEBUG: Processing CREATE TABLE identifiers");
            result = processCreateTableIdentifiersOnly(result, logs);
        }

        // 2. 处理CREATE VIEW语句（使用简单的字符串包含检查）
        if (upperSql.contains("CREATE VIEW") || upperSql.contains("CREATE OR REPLACE VIEW")) {
            logs.add("DEBUG: Processing CREATE VIEW identifiers");
            result = processCreateViewIdentifiersOnly(result, logs);
        }

        // 3. 处理INSERT语句
        if (upperSql.contains("INSERT INTO")) {
            logs.add("DEBUG: Processing INSERT identifiers");
            result = addBackticksToInsertIdentifiers(result, logs);
        }

        // 4. 处理独立的SELECT语句（不是VIEW中的）
        if (upperSql.contains("SELECT") && !upperSql.contains("CREATE VIEW") && !upperSql.contains("CREATE OR REPLACE VIEW")) {
            logs.add("DEBUG: Processing SELECT identifiers");
            result = addBackticksToSelectIdentifiers(result, logs);
        }

        // 5. 处理UPDATE语句
        if (upperSql.contains("UPDATE")) {
            logs.add("DEBUG: Processing UPDATE identifiers");
            result = addBackticksToUpdateIdentifiers(result, logs);
        }

        // 6. 处理DELETE语句
        if (upperSql.contains("DELETE FROM")) {
            logs.add("DEBUG: Processing DELETE identifiers");
            result = addBackticksToDeleteIdentifiers(result, logs);
        }

        // 7. 处理ALTER TABLE语句
        if (upperSql.contains("ALTER TABLE")) {
            logs.add("DEBUG: Processing ALTER TABLE identifiers");
            result = addBackticksToAlterTableIdentifiers(result, logs);
        }

        return result;
    }

    /**
     * 只处理CREATE TABLE语句中的标识符，不影响其他语句
     */
    private static String processCreateTableIdentifiersOnly(String sql, List<String> logs) {
        // 使用更精确的正则表达式，只匹配CREATE TABLE语句部分
        Pattern createTablePattern = Pattern.compile(
            "(CREATE\\s+TABLE\\s+[^;]+;)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher matcher = createTablePattern.matcher(sql);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String createTableStatement = matcher.group(1);
            String processedStatement = addBackticksToCreateTable(createTableStatement, logs);
            matcher.appendReplacement(sb, processedStatement);
        }

        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 只处理CREATE VIEW语句中的标识符，不影响其他语句
     */
    private static String processCreateViewIdentifiersOnly(String sql, List<String> logs) {
        // 使用更精确的正则表达式，只匹配CREATE VIEW语句部分
        Pattern createViewPattern = Pattern.compile(
            "(CREATE\\s+(?:OR\\s+REPLACE\\s+)?VIEW\\s+[^;]+;)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );

        Matcher matcher = createViewPattern.matcher(sql);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String createViewStatement = matcher.group(1);
            String processedStatement = processCreateViewStatement(createViewStatement, logs);
            matcher.appendReplacement(sb, processedStatement);
        }

        matcher.appendTail(sb);
        return sb.toString();
    }

    /**
     * 处理单个CREATE VIEW语句中的标识符
     */
    private static String processCreateViewStatement(String viewStatement, List<String> logs) {
        String result = viewStatement;

        // 1. 处理视图名：CREATE [OR REPLACE] VIEW view_name
        Pattern viewNamePattern = Pattern.compile(
            "(?i)(CREATE\\s+(?:OR\\s+REPLACE\\s+)?VIEW\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s+)",
            Pattern.MULTILINE
        );
        result = addBackticksToPatternIfKeyword(result, viewNamePattern, logs, "VIEW name", 2);

        // 2. 处理FROM子句中的表名（只在VIEW语句内）
        Pattern fromTablePattern = Pattern.compile(
            "(?i)(FROM\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s+|$|;|\\s+WHERE|\\s+ORDER|\\s+GROUP|\\s+LIMIT)",
            Pattern.MULTILINE
        );
        result = addBackticksToPatternIfKeyword(result, fromTablePattern, logs, "table name in VIEW", 2);

        // 3. 处理WHERE子句中的列名（只在VIEW语句内）
        Pattern whereColumnPattern = Pattern.compile(
            "(?i)(WHERE\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s*[=<>!])",
            Pattern.MULTILINE
        );
        result = addBackticksToPatternIfKeyword(result, whereColumnPattern, logs, "column name in WHERE", 2);

        // 4. 处理ORDER BY子句中的列名（只在VIEW语句内）
        Pattern orderByPattern = Pattern.compile(
            "(?i)(ORDER\\s+BY\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s*[,;]|\\s+ASC|\\s+DESC|$)",
            Pattern.MULTILINE
        );
        result = addBackticksToPatternIfKeyword(result, orderByPattern, logs, "column name in ORDER BY", 2);

        return result;
    }

    /**
     * 为INSERT语句中的标识符添加反引号
     */
    private static String addBackticksToInsertIdentifiers(String sql, List<String> logs) {
        // 重用现有的INSERT处理逻辑
        return addBackticksToInsert(sql, logs);
    }

    /**
     * 为SELECT语句中的标识符添加反引号
     */
    private static String addBackticksToSelectIdentifiers(String sql, List<String> logs) {
        // 重用现有的SELECT处理逻辑，但要小心不处理SQL语法关键字
        return addBackticksToSelectOnlyIdentifiers(sql, logs);
    }

    /**
     * 为UPDATE语句中的标识符添加反引号
     */
    private static String addBackticksToUpdateIdentifiers(String sql, List<String> logs) {
        // 重用现有的UPDATE处理逻辑
        return addBackticksToUpdate(sql, logs);
    }

    /**
     * 为DELETE语句中的标识符添加反引号
     */
    private static String addBackticksToDeleteIdentifiers(String sql, List<String> logs) {
        // 重用现有的DELETE处理逻辑
        return addBackticksToDelete(sql, logs);
    }

    /**
     * 为ALTER TABLE语句中的标识符添加反引号
     */
    private static String addBackticksToAlterTableIdentifiers(String sql, List<String> logs) {
        // 重用现有的ALTER TABLE处理逻辑
        return addBackticksToAlterTable(sql, logs);
    }

    /**
     * 智能添加反引号：只有当标识符是保留关键字时才添加
     */
    private static String addBackticksToPatternIfKeyword(String sql, Pattern pattern, List<String> logs, String context, int groupIndex) {
        Matcher matcher = pattern.matcher(sql);
        StringBuffer sb = new StringBuffer();
        int count = 0;

        while (matcher.find()) {
            String prefix = matcher.group(1);
            String identifier = matcher.group(groupIndex);
            String suffix = matcher.group(groupIndex + 1);

            // 只有当标识符是保留关键字时才添加反引号
            if (isMySqlReservedKeyword(identifier) && !identifier.startsWith("`")) {
                String quoted = "`" + identifier + "`";
                String replacement = prefix + quoted + suffix;
                // 使用Matcher.quoteReplacement避免特殊字符被解释为group reference
                matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
                count++;
                logs.add("PREPROCESSING: Added backticks to reserved keyword " + context + ": " + identifier);
            } else {
                // 非保留关键字不添加反引号
                matcher.appendReplacement(sb, Matcher.quoteReplacement(matcher.group(0)));
            }
        }

        matcher.appendTail(sb);

        if (count > 0) {
            logs.add("INFO: Added backticks to " + count + " reserved keyword " + context + "(s)");
        }

        return sb.toString();
    }

    /**
     * 为SELECT语句中的标识符添加反引号（只处理标识符，不处理SQL关键字）
     */
    private static String addBackticksToSelectOnlyIdentifiers(String sql, List<String> logs) {
        String result = sql;

        // 只处理FROM后的表名和WHERE中的列名，不处理其他可能的SQL关键字
        Pattern fromTablePattern = Pattern.compile(
            "(?i)(FROM\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s+|$|;|\\s+WHERE|\\s+ORDER|\\s+GROUP|\\s+LIMIT)",
            Pattern.MULTILINE
        );
        result = addBackticksToPatternIfKeyword(result, fromTablePattern, logs, "table name", 2);

        Pattern whereColumnPattern = Pattern.compile(
            "(?i)(WHERE\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s*[=<>!])",
            Pattern.MULTILINE
        );
        result = addBackticksToPatternIfKeyword(result, whereColumnPattern, logs, "column name", 2);

        return result;
    }

    /**
     * 为CREATE TABLE语句中的表名和字段名添加反引号
     */
    private static String addBackticksToCreateTable(String sql, List<String> logs) {
        // 处理表名：CREATE TABLE table_name
        Pattern tableNamePattern = Pattern.compile(
            "(?i)(CREATE\\s+TABLE\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s*\\()",
            Pattern.MULTILINE
        );

        sql = addBackticksToPattern(sql, tableNamePattern, logs, "CREATE TABLE table name", 2);

        // 处理列名：在CREATE TABLE的列定义中
        // 使用更精确的正则表达式，避免匹配到数据类型
        sql = addBackticksToCreateTableColumns(sql, logs);

        // 处理KEY语句中的列名
        sql = addBackticksToKeyColumns(sql, logs);

        // 处理索引名和约束名
        sql = addBackticksToIndexNames(sql, logs);

        return sql;
    }

    /**
     * 为INSERT语句中的表名和字段名添加反引号
     */
    private static String addBackticksToInsert(String sql, List<String> logs) {
        // 处理表名：INSERT INTO table_name 或 INSERT INTO schema.table_name
        Pattern tableNamePattern = Pattern.compile(
            "(?i)(INSERT\\s+INTO\\s+)([a-zA-Z_][a-zA-Z0-9_]*(?:\\.[a-zA-Z_][a-zA-Z0-9_]*)?)(\\s*\\()",
            Pattern.MULTILINE
        );

        sql = addBackticksToPattern(sql, tableNamePattern, logs, "INSERT table name", 2);

        // 处理列名：INSERT INTO table (col1, col2, ...) 或 INSERT INTO schema.table (col1, col2, ...)
        Pattern insertPattern = Pattern.compile(
            "(?i)(INSERT\\s+INTO\\s+`?[a-zA-Z_][a-zA-Z0-9_]*`?(?:\\.`?[a-zA-Z_][a-zA-Z0-9_]*`?)?\\s*\\()([^)]+)(\\))",
            Pattern.MULTILINE
        );

        sql = addBackticksToInsertColumns(sql, insertPattern, logs);

        return sql;
    }

    /**
     * 为UPDATE语句中的表名和字段名添加反引号
     */
    private static String addBackticksToUpdate(String sql, List<String> logs) {
        // 处理表名：UPDATE table_name SET
        Pattern tableNamePattern = Pattern.compile(
            "(?i)(UPDATE\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s+SET)",
            Pattern.MULTILINE
        );

        sql = addBackticksToPattern(sql, tableNamePattern, logs, "UPDATE table name", 2);

        // 处理字段名：SET col = value 和 WHERE col = value
        sql = addBackticksToUpdateColumns(sql, logs);

        return sql;
    }

    /**
     * 为DELETE语句中的表名和字段名添加反引号
     */
    private static String addBackticksToDelete(String sql, List<String> logs) {
        // 处理表名：DELETE FROM table_name
        Pattern tableNamePattern = Pattern.compile(
            "(?i)(DELETE\\s+FROM\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s*[;\\s]|WHERE|$)",
            Pattern.MULTILINE
        );

        sql = addBackticksToPattern(sql, tableNamePattern, logs, "DELETE table name", 2);

        // 处理字段名：WHERE col = value
        sql = addBackticksToWhereColumns(sql, logs);

        return sql;
    }

    /**
     * 为ALTER TABLE语句中的表名和字段名添加反引号
     */
    private static String addBackticksToAlterTable(String sql, List<String> logs) {
        // 处理表名：ALTER TABLE table_name
        Pattern tableNamePattern = Pattern.compile(
            "(?i)(ALTER\\s+TABLE\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s+)",
            Pattern.MULTILINE
        );

        sql = addBackticksToPattern(sql, tableNamePattern, logs, "ALTER TABLE table name", 2);

        // 处理字段名：ADD COLUMN col_name type
        Pattern addColumnPattern = Pattern.compile(
            "(?i)(ADD\\s+COLUMN\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s+[a-zA-Z]+)",
            Pattern.MULTILINE
        );

        sql = addBackticksToPattern(sql, addColumnPattern, logs, "ALTER TABLE column name", 2);

        return sql;
    }

    /**
     * 通用的模式处理方法：为匹配的标识符添加反引号
     * 改进版本：更严格的检查，避免重复添加反引号
     */
    private static String addBackticksToPattern(String sql, Pattern pattern, List<String> logs,
                                               String context, int groupIndex) {
        logs.add("DEBUG: addBackticksToPattern - " + context + " - Pattern: " + pattern.pattern());
        Matcher matcher = pattern.matcher(sql);
        StringBuffer sb = new StringBuffer();
        int count = 0;

        while (matcher.find()) {
            String identifier = matcher.group(groupIndex);
            logs.add("DEBUG: Found match for " + context + ": '" + identifier + "' at position " + matcher.start() + "-" + matcher.end());

            // 更严格的检查：确保标识符没有被引用过
            if (needsBackticks(identifier)) {
                String quoted = "`" + identifier + "`";
                // 构建精确的替换字符串，避免替换错误的部分
                String replacement = buildPreciseReplacement(matcher, groupIndex, quoted);
                logs.add("DEBUG: Adding backticks to " + context + ": '" + identifier + "' -> '" + quoted + "'");
                matcher.appendReplacement(sb, replacement);
                count++;
            } else {
                logs.add("DEBUG: Skipping " + context + ": '" + identifier + "' (already has backticks or invalid)");
                matcher.appendReplacement(sb, matcher.group());
            }
        }
        matcher.appendTail(sb);

        if (count > 0) {
            logs.add("INFO: Added backticks to " + count + " " + context + "(s)");
        } else {
            logs.add("DEBUG: No backticks added for " + context);
        }

        return sb.toString();
    }

    /**
     * 构建精确的替换字符串，只替换指定组的内容
     */
    private static String buildPreciseReplacement(Matcher matcher, int groupIndex, String quoted) {
        StringBuilder result = new StringBuilder();

        // 添加组之前的部分
        for (int i = 1; i < groupIndex; i++) {
            if (matcher.group(i) != null) {
                result.append(matcher.group(i));
            }
        }

        // 添加替换后的组内容
        result.append(quoted);

        // 添加组之后的部分
        for (int i = groupIndex + 1; i <= matcher.groupCount(); i++) {
            if (matcher.group(i) != null) {
                result.append(matcher.group(i));
            }
        }

        return result.toString();
    }

    /**
     * 检查标识符是否需要添加反引号
     * 遵循MySQL官方文档标准：统一为所有标识符添加反引号以避免关键字冲突
     * 参考：https://dev.mysql.com/doc/refman/8.4/en/identifiers.html
     */
    private static boolean needsBackticks(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return false;
        }

        String trimmed = identifier.trim();

        // 如果已经被反引号包围，不需要再添加
        if (trimmed.startsWith("`") && trimmed.endsWith("`")) {
            return false;
        }

        // 如果已经被双引号包围，需要转换为反引号
        if (trimmed.startsWith("\"") && trimmed.endsWith("\"")) {
            return true;
        }

        // 如果包含反引号但不是完整包围，可能是错误的格式，也不处理
        if (trimmed.contains("`")) {
            return false;
        }

        // 检查是否是有效的标识符
        if (!isValidIdentifier(trimmed)) {
            return false;
        }

        // 不给SQL关键字加反引号（如SELECT、FROM等语句关键字）
        if (isSqlKeyword(trimmed)) {
            return false;
        }

        // 遵循MySQL官方标准：为所有标识符（表名、字段名、索引名等）添加反引号
        // 这样可以避免所有可能的关键字冲突，确保SQL解析的一致性
        return true;
    }

    /**
     * 检查是否是SQL关键字（不应该加反引号的关键字）
     */
    private static boolean isSqlKeyword(String word) {
        if (word == null) {
            return false;
        }

        String upperWord = word.toUpperCase();

        // 使用统一的SQL关键字管理系统
        Set<String> sqlKeywords = com.xylink.sqltranspiler.common.constants.SqlKeywords.getAllKeywords();

        return sqlKeywords.contains(upperWord);
    }

    /**
     * 为索引名和约束名添加反引号
     */
    private static String addBackticksToIndexNames(String sql, List<String> logs) {
        String result = sql;

        // 处理PRIMARY KEY
        Pattern primaryKeyPattern = Pattern.compile(
            "(?i)(PRIMARY\\s+KEY\\s*\\()([^)]+)(\\))",
            Pattern.MULTILINE
        );
        result = addBackticksToColumnList(result, primaryKeyPattern, logs, "PRIMARY KEY");

        // 处理KEY/INDEX
        // 修改正则表达式以支持已经有反引号的KEY名称
        Pattern keyPattern = Pattern.compile(
            "(?i)(KEY\\s+)([a-zA-Z_`][a-zA-Z0-9_`]*)(\\s*\\([^)]+\\))",
            Pattern.MULTILINE
        );
        result = addBackticksToPattern(result, keyPattern, logs, "KEY name", 2);

        // 处理UNIQUE KEY
        Pattern uniqueKeyPattern = Pattern.compile(
            "(?i)(UNIQUE\\s+KEY\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s*\\([^)]+\\))",
            Pattern.MULTILINE
        );
        result = addBackticksToPattern(result, uniqueKeyPattern, logs, "UNIQUE KEY name", 2);

        // 处理FOREIGN KEY
        Pattern foreignKeyPattern = Pattern.compile(
            "(?i)(FOREIGN\\s+KEY\\s*\\()([^)]+)(\\))",
            Pattern.MULTILINE
        );
        result = addBackticksToColumnList(result, foreignKeyPattern, logs, "FOREIGN KEY");

        return result;
    }

    /**
     * 为INSERT语句的列名添加反引号
     */
    private static String addBackticksToInsertColumns(String sql, Pattern pattern, List<String> logs) {
        Matcher matcher = pattern.matcher(sql);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String prefix = matcher.group(1);
            String columnList = matcher.group(2);
            String suffix = matcher.group(3);

            String processedColumnList = addBackticksToColumnListString(columnList, logs, "INSERT");
            matcher.appendReplacement(sb, prefix + processedColumnList + suffix);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 为UPDATE语句的列名添加反引号
     */
    private static String addBackticksToUpdateColumns(String sql, List<String> logs) {
        // 处理SET子句中的列名
        Pattern setPattern = Pattern.compile(
            "(?i)(SET\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s*=)",
            Pattern.MULTILINE
        );

        sql = addBackticksToPattern(sql, setPattern, logs, "UPDATE SET column", 2);

        return addBackticksToWhereColumns(sql, logs);
    }

    /**
     * 为WHERE条件中的列名添加反引号
     */
    private static String addBackticksToWhereColumns(String sql, List<String> logs) {
        // 处理WHERE子句中的列名
        Pattern wherePattern = Pattern.compile(
            "(?i)(WHERE\\s+)([a-zA-Z_][a-zA-Z0-9_]*)(\\s*[=<>!])",
            Pattern.MULTILINE
        );

        return addBackticksToPattern(sql, wherePattern, logs, "WHERE column", 2);
    }

    /**
     * 为列名列表添加反引号
     */
    private static String addBackticksToColumnList(String sql, Pattern pattern, List<String> logs, String context) {
        Matcher matcher = pattern.matcher(sql);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String prefix = matcher.group(1);
            String columnList = matcher.group(2);
            String suffix = matcher.group(3);

            String processedColumnList = addBackticksToColumnListString(columnList, logs, context);
            matcher.appendReplacement(sb, prefix + processedColumnList + suffix);
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 为列名列表字符串中的每个列名添加反引号
     * 改进版本：使用更严格的检查
     */
    private static String addBackticksToColumnListString(String columnList, List<String> logs, String context) {
        String[] columns = columnList.split(",");
        StringBuilder result = new StringBuilder();
        int count = 0;

        for (int i = 0; i < columns.length; i++) {
            String column = columns[i].trim();

            // 使用更严格的检查
            if (needsBackticks(column)) {
                column = "`" + column + "`";
                count++;
            }

            result.append(column);
            if (i < columns.length - 1) {
                result.append(", ");
            }
        }

        if (count > 0) {
            logs.add("INFO: Added backticks to " + count + " column(s) in " + context);
        }

        return result.toString();
    }

    /**
     * 检查是否是有效的标识符
     */
    private static boolean isValidIdentifier(String identifier) {
        if (identifier == null || identifier.trim().isEmpty()) {
            return false;
        }

        String trimmed = identifier.trim();

        // 检查是否是有效的标识符格式 - 使用简单检查避免正则表达式
        if (trimmed.isEmpty()) return false;

        // 第一个字符必须是字母或下划线
        char first = trimmed.charAt(0);
        if (!Character.isLetter(first) && first != '_') return false;

        // 其余字符必须是字母、数字或下划线
        for (int i = 1; i < trimmed.length(); i++) {
            char c = trimmed.charAt(i);
            if (!Character.isLetterOrDigit(c) && c != '_') return false;
        }

        return true;
    }

    /**
     * 为KEY语句中的列名添加反引号
     */
    private static String addBackticksToKeyColumns(String sql, List<String> logs) {
        // 处理KEY语句中的列名：KEY idx_name (column_name)
        Pattern keyColumnPattern = Pattern.compile(
            "(?i)(KEY\\s+[a-zA-Z_`][a-zA-Z0-9_`]*\\s*\\()([^)]+)(\\))",
            Pattern.MULTILINE
        );

        sql = addBackticksToColumnList(sql, keyColumnPattern, logs, "KEY columns");

        // 处理UNIQUE KEY语句中的列名
        Pattern uniqueKeyColumnPattern = Pattern.compile(
            "(?i)(UNIQUE\\s+KEY\\s+[a-zA-Z_`][a-zA-Z0-9_`]*\\s*\\()([^)]+)(\\))",
            Pattern.MULTILINE
        );

        sql = addBackticksToColumnList(sql, uniqueKeyColumnPattern, logs, "UNIQUE KEY columns");

        return sql;
    }

    /**
     * 为CREATE TABLE中的列名添加反引号（改进版本）
     */
    private static String addBackticksToCreateTableColumns(String sql, List<String> logs) {
        // 使用行级处理，避免复杂的正则表达式匹配问题
        String[] lines = sql.split("\\n");
        StringBuilder result = new StringBuilder();
        boolean inCreateTable = false;
        boolean inTableDefinition = false;
        int count = 0;

        logs.add("DEBUG: addBackticksToCreateTableColumns - Processing " + lines.length + " lines");

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            String trimmedLine = line.trim().toUpperCase();

            // 检测CREATE TABLE开始
            if (trimmedLine.startsWith("CREATE TABLE")) {
                inCreateTable = true;
                inTableDefinition = false;
                logs.add("DEBUG: Found CREATE TABLE at line " + (i + 1));
            }

            // 检测表定义开始
            if (inCreateTable && line.contains("(")) {
                inTableDefinition = true;
                logs.add("DEBUG: Table definition started at line " + (i + 1));
            }

            // 检测表定义结束
            if (inTableDefinition && (line.contains(") ENGINE") ||
                                     line.trim().endsWith(");") ||
                                     line.trim().equals(")"))) {
                inTableDefinition = false;
                inCreateTable = false;
                logs.add("DEBUG: Table definition ended at line " + (i + 1));
            }

            // 处理列定义行
            boolean isColumnLine = inTableDefinition && !trimmedLine.startsWith("PRIMARY KEY") &&
                !trimmedLine.startsWith("KEY ") && !trimmedLine.startsWith("UNIQUE KEY") &&
                !trimmedLine.startsWith("FOREIGN KEY") && !trimmedLine.startsWith("CONSTRAINT") &&
                !trimmedLine.startsWith("FULLTEXT KEY") && !trimmedLine.startsWith("SPATIAL KEY") &&
                !trimmedLine.startsWith("CHECK") && !trimmedLine.startsWith("INDEX") &&
                !trimmedLine.equals("(") && !trimmedLine.startsWith(")") &&
                !trimmedLine.startsWith("CREATE TABLE");

            if (isColumnLine) {
                logs.add("DEBUG: Processing column line " + (i + 1) + ": " + line.trim());
                String originalLine = line;
                line = processColumnDefinitionLine(line, logs);
                if (!line.equals(originalLine)) {
                    count++;
                    logs.add("DEBUG: Modified line " + (i + 1) + ": " + line.trim());
                }
            }

            result.append(line).append("\n");
        }

        logs.add("DEBUG: addBackticksToCreateTableColumns - Processed " + count + " column definitions");
        if (count > 0) {
            logs.add("INFO: Added backticks to " + count + " column definition(s)");
        }

        return result.toString();
    }

    /**
     * 处理单个列定义行
     * 改进版本：使用更严格的检查
     */
    private static String processColumnDefinitionLine(String line, List<String> logs) {
        // 匹配列定义：column_name datatype [constraints...]
        // 支持反引号、双引号或无引号的标识符
        Pattern columnDefPattern = Pattern.compile(
            "^(\\s*)([`\"]?)([a-zA-Z_][a-zA-Z0-9_]*)([`\"]?)(\\s+.*)$"
        );

        Matcher matcher = columnDefPattern.matcher(line);
        if (matcher.find()) {
            String indent = matcher.group(1);
            String startQuote = matcher.group(2);
            String columnName = matcher.group(3);
            String endQuote = matcher.group(4);
            String rest = matcher.group(5);

            // 如果已经有完整的反引号，保持不变
            if ("`".equals(startQuote) && "`".equals(endQuote)) {
                return line;
            }

            // 如果是双引号，转换为反引号
            if ("\"".equals(startQuote) && "\"".equals(endQuote)) {
                logs.add("PREPROCESSING: Converting double quotes to backticks for column: " + columnName);
                return indent + "`" + columnName + "`" + rest;
            }

            // 统一策略：为所有标识符添加反引号（遵循MySQL官方文档建议）
            // 这样可以避免关键字冲突并确保解析器的一致性
            if (startQuote.isEmpty() && endQuote.isEmpty()) {
                logs.add("PREPROCESSING: Adding backticks to column: " + columnName);
                return indent + "`" + columnName + "`" + rest;
            }
        }

        return line;
    }

    /**
     * 移除MySQL特有的表选项（ENGINE、DEFAULT CHARSET等）
     * 这些选项在其他数据库中不支持，需要在预处理阶段移除
     *
     * @param sql 输入SQL
     * @param logs 日志列表
     * @param targetDialect 目标数据库类型，用于决定是否移除特定选项
     */
    private static String removeMySqlTableOptions(String sql, List<String> logs, String targetDialect) {
        String result = sql;
        int removedCount = 0;

        // 移除ENGINE选项
        // 匹配: ENGINE=InnoDB, ENGINE = MyISAM, ENGINE=MEMORY等
        Pattern enginePattern = Pattern.compile("\\s+ENGINE\\s*=\\s*[a-zA-Z0-9_]+", Pattern.CASE_INSENSITIVE);
        if (enginePattern.matcher(result).find()) {
            result = enginePattern.matcher(result).replaceAll("");
            removedCount++;
            logs.add("MYSQL_OPTIONS: Removed ENGINE option from CREATE TABLE statement");
        }

        // 移除DEFAULT CHARSET选项
        // 匹配: DEFAULT CHARSET=utf8mb4, DEFAULT CHARSET = utf8等
        Pattern charsetPattern = Pattern.compile("\\s+DEFAULT\\s+CHARSET\\s*=\\s*[a-zA-Z0-9_]+", Pattern.CASE_INSENSITIVE);
        if (charsetPattern.matcher(result).find()) {
            result = charsetPattern.matcher(result).replaceAll("");
            removedCount++;
            logs.add("MYSQL_OPTIONS: Removed DEFAULT CHARSET option from CREATE TABLE statement");
        }

        // 移除COLLATE选项
        // 匹配: COLLATE=utf8mb4_unicode_ci, COLLATE = utf8_general_ci等
        Pattern collatePattern = Pattern.compile("\\s+COLLATE\\s*=\\s*[a-zA-Z0-9_]+", Pattern.CASE_INSENSITIVE);
        if (collatePattern.matcher(result).find()) {
            result = collatePattern.matcher(result).replaceAll("");
            removedCount++;
            logs.add("MYSQL_OPTIONS: Removed COLLATE option from CREATE TABLE statement");
        }

        // 根据目标数据库类型决定是否移除AUTO_INCREMENT表级选项
        // 神通数据库完全支持AUTO_INCREMENT语法，不应该移除
        if (targetDialect != null && !"shentong".equalsIgnoreCase(targetDialect)) {
            // 移除AUTO_INCREMENT表级选项（仅对不支持的数据库）
            // 匹配: AUTO_INCREMENT=1000等（表级别的，不是列级别的）
            Pattern autoIncrementPattern = Pattern.compile("\\)\\s+AUTO_INCREMENT\\s*=\\s*\\d+", Pattern.CASE_INSENSITIVE);
            if (autoIncrementPattern.matcher(result).find()) {
                result = autoIncrementPattern.matcher(result).replaceAll(")");
                removedCount++;
                logs.add("MYSQL_OPTIONS: Removed table-level AUTO_INCREMENT option from CREATE TABLE statement (not supported by " + targetDialect + ")");
            }
        } else {
            logs.add("MYSQL_OPTIONS: Preserved table-level AUTO_INCREMENT option (supported by " + targetDialect + ")");
        }

        if (removedCount > 0) {
            logs.add("MYSQL_OPTIONS: Total MySQL-specific table options removed: " + removedCount);
        } else {
            logs.add("MYSQL_OPTIONS: No MySQL-specific table options found to remove");
        }

        return result;
    }

    /**
     * 过滤不支持的SQL语句，明确记录丢弃的内容
     *
     * 对于达梦数据库转换，以下语句类型不支持，将被过滤：
     * - LOCK TABLES / UNLOCK TABLES
     * - SET 语句（部分）
     * - FLUSH 语句
     * - RESET 语句
     * - 其他MySQL特有的管理语句
     */
    private static String filterUnsupportedStatements(String sql, List<String> logs) {
        String[] lines = sql.split("\n");
        StringBuilder result = new StringBuilder();
        int filteredCount = 0;
        int setStatementCount = 0;
        int otherUnsupportedCount = 0;

        for (String line : lines) {
            String trimmedLine = line.trim().toUpperCase();
            boolean shouldFilter = false;
            String filterReason = "";

            // 注意：LOCK/UNLOCK TABLES语句现在由转换器处理，不在预处理阶段过滤

            // 检查SET语句（某些类型）
            // 注意：SET语句现在由转换器处理，包括FOREIGN_KEY_CHECKS等，不在预处理阶段过滤
            // 这些语句会被转换器转换为适当的注释或等效语句
            if (trimmedLine.startsWith("SET ")) {
                // 检查是否是确实不支持且无法转换的SET语句
                // 大多数SET语句现在由生成器处理，转换为注释或等效语句
                // 暂时不过滤任何SET语句，让转换器决定如何处理
                // if (trimmedLine.contains("SOME_TRULY_UNSUPPORTED_VARIABLE")) {
                //     shouldFilter = true;
                //     filterReason = "SET statement with truly unsupported variable";
                //     setStatementCount++;
                // }
            }
            // 检查其他不支持的语句
            else if (trimmedLine.startsWith("FLUSH ") ||
                     trimmedLine.startsWith("RESET ") ||
                     trimmedLine.startsWith("DESCRIBE ") ||
                     trimmedLine.startsWith("DESC ")) {
                shouldFilter = true;
                filterReason = "Administrative statement (not needed for data migration)";
                otherUnsupportedCount++;
            }
            // 检查SHOW语句 - 只过滤不支持的SHOW语句，保留SHOW INDEX/INDEXES/KEYS
            else if (trimmedLine.startsWith("SHOW ")) {
                // 检查是否是支持的SHOW语句
                if (trimmedLine.contains("INDEX") || trimmedLine.contains("INDEXES") || trimmedLine.contains("KEYS")) {
                    // SHOW INDEX语句保留，用于后续转换为系统表查询
                    shouldFilter = false;
                } else {
                    // 其他SHOW语句过滤掉
                    shouldFilter = true;
                    filterReason = "SHOW statement (not supported, except SHOW INDEX)";
                    otherUnsupportedCount++;
                }
            }

            if (shouldFilter) {
                filteredCount++;
                // 记录被过滤的具体SQL
                logs.add("FILTERED: " + filterReason + " - Original SQL: " + line.trim());
            } else {
                // 保留支持的语句
                result.append(line).append("\n");
            }
        }

        // 记录过滤统计
        if (filteredCount > 0) {
            logs.add("FILTERING SUMMARY: Removed " + filteredCount + " unsupported statements:");
            if (setStatementCount > 0) {
                logs.add("  - SET statements: " + setStatementCount + " statements");
            }
            if (otherUnsupportedCount > 0) {
                logs.add("  - Other administrative: " + otherUnsupportedCount + " statements");
            }
            logs.add("FILTERING: These statements are not supported in Dameng and were safely removed");
            logs.add("FILTERING: LOCK/UNLOCK TABLES statements are now handled by the transpiler");
        } else {
            logs.add("FILTERING: No unsupported statements found, all SQL preserved");
        }

        return result.toString();
    }

    /**
     * 移除MySQL DDL语句中的COMMENT子句
     * 当preserveComments=false时调用，移除所有COMMENT相关内容
     */
    private static String removeCommentClauses(String sql, List<String> logs) {
        String result = sql;
        int removedCount = 0;

        // 移除表级别的COMMENT子句
        // 匹配: COMMENT='...' 或 COMMENT="..."
        Pattern tableCommentPattern = Pattern.compile("\\s+COMMENT\\s*=\\s*['\"][^'\"]*['\"]", Pattern.CASE_INSENSITIVE);
        result = tableCommentPattern.matcher(result).replaceAll("");

        // 移除列级别的COMMENT子句
        // 匹配: COMMENT '...' 或 COMMENT "..."
        Pattern columnCommentPattern = Pattern.compile("\\s+COMMENT\\s+['\"][^'\"]*['\"]", Pattern.CASE_INSENSITIVE);
        result = columnCommentPattern.matcher(result).replaceAll("");

        // 统计移除的COMMENT子句数量
        removedCount = countMatches(sql, tableCommentPattern) + countMatches(sql, columnCommentPattern);

        if (removedCount > 0) {
            logs.add("COMMENT_REMOVAL: Removed " + removedCount + " COMMENT clauses from DDL statements");
        }

        return result;
    }

    /**
     * 计算正则表达式在字符串中的匹配次数
     */
    private static int countMatches(String text, Pattern pattern) {
        int count = 0;
        java.util.regex.Matcher matcher = pattern.matcher(text);
        while (matcher.find()) {
            count++;
        }
        return count;
    }
}