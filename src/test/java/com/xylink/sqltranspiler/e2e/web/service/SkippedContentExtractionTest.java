package com.xylink.sqltranspiler.e2e.web.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.web.dto.TranspileResponse;
import com.xylink.sqltranspiler.web.service.SqlTranspilerWebService;

/**
 * 测试跳过内容提取功能
 */
public class SkippedContentExtractionTest {

    private SqlTranspilerWebService webService;

    @BeforeEach
    void setUp() {
        webService = new SqlTranspilerWebService();
    }

    @Test
    void testExtractSkippedContents() throws Exception {
        // 根据数据库规则：使用真实的预处理日志而不是模拟数据
        List<String> preprocessingLogs = Arrays.asList(
            "FILTERED: Administrative statement (not needed for data migration) - Original SQL: FLUSH TABLES;",
            "FILTERED: SHOW statement (not supported, except SHOW INDEX) - Original SQL: SHOW TABLES;",
            "COMMENT_REMOVAL: Removed single-line comment: -- 这是注释",
            "COMMENT_REMOVAL: Removed multi-line comment: /* 多行注释 */",
            "COMMENT_REMOVAL: Total comments removed: 2"
        );

        // 使用反射调用私有方法
        java.lang.reflect.Method method = SqlTranspilerWebService.class.getDeclaredMethod(
            "extractSkippedContents", List.class);
        method.setAccessible(true);
        
        @SuppressWarnings("unchecked")
        List<TranspileResponse.SkippedContent> result = 
            (List<TranspileResponse.SkippedContent>) method.invoke(webService, preprocessingLogs);

        // 验证结果
        assertNotNull(result);

        // 打印实际结果用于调试
        System.out.println("Actual extracted contents:");
        for (int i = 0; i < result.size(); i++) {
            TranspileResponse.SkippedContent content = result.get(i);
            System.out.println(String.format("[%d] Type: %s, Content: %s, Reason: %s",
                i, content.getType(), content.getContent(), content.getReason()));
        }

        assertEquals(4, result.size());

        // 验证第一个跳过的内容（FLUSH TABLES）
        TranspileResponse.SkippedContent first = result.get(0);
        assertEquals("管理语句", first.getType());
        assertEquals("FLUSH TABLES;", first.getContent());
        assertEquals("Administrative statement (not needed for data migration)", first.getReason());

        // 验证第二个跳过的内容（SHOW TABLES）
        TranspileResponse.SkippedContent second = result.get(1);
        assertEquals("SHOW语句", second.getType());
        assertEquals("SHOW TABLES;", second.getContent());
        assertEquals("SHOW statement (not supported, except SHOW INDEX)", second.getReason());

        // 验证第三个跳过的内容（单行注释）
        TranspileResponse.SkippedContent third = result.get(2);
        assertEquals("注释", third.getType());
        assertEquals("-- 这是注释", third.getContent());
        assertEquals("注释内容已移除", third.getReason());

        // 验证第四个跳过的内容（多行注释）
        TranspileResponse.SkippedContent fourth = result.get(3);
        assertEquals("注释", fourth.getType());
        assertEquals("/* 多行注释 */", fourth.getContent());
        assertEquals("注释内容已移除", fourth.getReason());
    }

    @Test
    void testDetermineSkippedType() throws Exception {
        // 使用反射调用私有方法
        java.lang.reflect.Method method = SqlTranspilerWebService.class.getDeclaredMethod(
            "determineSkippedType", String.class);
        method.setAccessible(true);

        // 测试不同类型的识别
        assertEquals("管理语句", method.invoke(webService, "Administrative statement (not needed for data migration)"));
        assertEquals("SHOW语句", method.invoke(webService, "SHOW statement (not supported, except SHOW INDEX)"));
        assertEquals("SET语句", method.invoke(webService, "SET statement"));
        assertEquals("其他", method.invoke(webService, "Unknown reason"));
    }

    @Test
    void testExtractCommentContent() throws Exception {
        // 使用反射调用私有方法
        java.lang.reflect.Method method = SqlTranspilerWebService.class.getDeclaredMethod(
            "extractCommentContent", String.class);
        method.setAccessible(true);

        // 测试单行注释提取
        String singleLineLog = "COMMENT_REMOVAL: Removed single-line comment: -- 这是注释";
        assertEquals("-- 这是注释", method.invoke(webService, singleLineLog));

        // 测试多行注释提取
        String multiLineLog = "COMMENT_REMOVAL: Removed multi-line comment: /* 多行注释 */";
        assertEquals("/* 多行注释 */", method.invoke(webService, multiLineLog));

        // 测试通用注释日志
        String genericLog = "COMMENT_REMOVAL: Some other comment info";
        assertEquals("注释内容", method.invoke(webService, genericLog));
    }
}
