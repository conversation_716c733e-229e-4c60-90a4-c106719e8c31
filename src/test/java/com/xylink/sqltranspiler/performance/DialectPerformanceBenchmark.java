package com.xylink.sqltranspiler.performance;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;

import com.xylink.sqltranspiler.core.dialects.DamengDialect;
import com.xylink.sqltranspiler.core.dialects.KingbaseDialect;
import com.xylink.sqltranspiler.core.dialects.SqlDialect;
import com.xylink.sqltranspiler.core.functions.FunctionMapper;
import com.xylink.sqltranspiler.infrastructure.util.SqlConversionUtils;

/**
 * 方言和函数映射性能基准测试
 * 
 * 遵循 .augment/rules/rule-db.md：
 * - 基于官方文档的性能基准
 * - 确保增强不影响性能
 * - 提供清晰的性能指标
 */
public class DialectPerformanceBenchmark {
    
    private SqlDialect damengDialect;
    private SqlDialect kingbaseDialect;
    private FunctionMapper functionMapper;
    
    // 测试数据
    private List<String> testIdentifiers;
    private List<String> testDataTypes;
    private List<String> testFunctions;
    private List<String> testSqlStatements;
    
    @BeforeEach
    void setUp() {
        damengDialect = new DamengDialect();
        kingbaseDialect = new KingbaseDialect();
        functionMapper = new FunctionMapper();
        
        // 准备测试数据
        prepareTestData();
    }
    
    private void prepareTestData() {
        // 标识符测试数据
        testIdentifiers = new ArrayList<>();
        testIdentifiers.add("user_table");
        testIdentifiers.add("SELECT"); // 保留字
        testIdentifiers.add("MyTable"); // 混合大小写
        testIdentifiers.add("table_with_very_long_name_that_might_affect_performance");
        testIdentifiers.add("123invalid"); // 无效标识符
        
        // 数据类型测试数据
        testDataTypes = new ArrayList<>();
        testDataTypes.add("VARCHAR");
        testDataTypes.add("TEXT");
        testDataTypes.add("INT");
        testDataTypes.add("DECIMAL");
        testDataTypes.add("DATETIME");
        testDataTypes.add("BLOB");
        testDataTypes.add("TINYINT");
        testDataTypes.add("BIGINT");
        testDataTypes.add("FLOAT");
        testDataTypes.add("DOUBLE");
        
        // 函数测试数据
        testFunctions = new ArrayList<>();
        testFunctions.add("NOW");
        testFunctions.add("CURDATE");
        testFunctions.add("CURTIME");
        testFunctions.add("CONCAT");
        testFunctions.add("LENGTH");
        testFunctions.add("SUBSTRING");
        testFunctions.add("IFNULL");
        testFunctions.add("RAND");
        testFunctions.add("ABS");
        testFunctions.add("UPPER");
        
        // SQL语句测试数据
        testSqlStatements = new ArrayList<>();
        testSqlStatements.add("SELECT NOW(), CURDATE() FROM users WHERE id = 1");
        testSqlStatements.add("SELECT CONCAT(first_name, ' ', last_name) AS full_name FROM users");
        testSqlStatements.add("SELECT LENGTH(description) FROM products WHERE price > 100");
        testSqlStatements.add("UPDATE users SET last_login = NOW() WHERE active = 1");
        testSqlStatements.add("INSERT INTO logs (message, created_at) VALUES ('test', NOW())");
    }
    
    @Test
    void benchmarkIdentifierQuoting(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 达梦方言性能测试
        long damengTime = measureTime(() -> {
            for (int i = 0; i < 10000; i++) {
                for (String identifier : testIdentifiers) {
                    damengDialect.quoteIdentifier(identifier);
                }
            }
        });
        
        // 金仓方言性能测试
        long kingbaseTime = measureTime(() -> {
            for (int i = 0; i < 10000; i++) {
                for (String identifier : testIdentifiers) {
                    kingbaseDialect.quoteIdentifier(identifier);
                }
            }
        });
        
        System.out.printf("达梦标识符引用: %d ms (平均 %.3f μs/次)%n", 
            damengTime, (double) damengTime * 1000 / (10000 * testIdentifiers.size()));
        System.out.printf("金仓标识符引用: %d ms (平均 %.3f μs/次)%n", 
            kingbaseTime, (double) kingbaseTime * 1000 / (10000 * testIdentifiers.size()));
        
        // 性能断言 - 单次操作应该在1毫秒以内
        double avgDamengTime = (double) damengTime / (10000 * testIdentifiers.size());
        double avgKingbaseTime = (double) kingbaseTime / (10000 * testIdentifiers.size());
        
        if (avgDamengTime > 1.0 || avgKingbaseTime > 1.0) {
            System.out.println("⚠️  警告: 标识符引用性能可能需要优化");
        } else {
            System.out.println("✅ 标识符引用性能良好");
        }
    }
    
    @Test
    void benchmarkDataTypeMapping(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 达梦数据类型映射性能测试
        long damengTime = measureTime(() -> {
            for (int i = 0; i < 10000; i++) {
                for (String dataType : testDataTypes) {
                    damengDialect.mapDataType(dataType, 255, 10, 2);
                }
            }
        });
        
        // 金仓数据类型映射性能测试
        long kingbaseTime = measureTime(() -> {
            for (int i = 0; i < 10000; i++) {
                for (String dataType : testDataTypes) {
                    kingbaseDialect.mapDataType(dataType, 255, 10, 2);
                }
            }
        });
        
        System.out.printf("达梦数据类型映射: %d ms (平均 %.3f μs/次)%n", 
            damengTime, (double) damengTime * 1000 / (10000 * testDataTypes.size()));
        System.out.printf("金仓数据类型映射: %d ms (平均 %.3f μs/次)%n", 
            kingbaseTime, (double) kingbaseTime * 1000 / (10000 * testDataTypes.size()));
        
        // 性能断言
        double avgDamengTime = (double) damengTime / (10000 * testDataTypes.size());
        double avgKingbaseTime = (double) kingbaseTime / (10000 * testDataTypes.size());
        
        if (avgDamengTime > 1.0 || avgKingbaseTime > 1.0) {
            System.out.println("⚠️  警告: 数据类型映射性能可能需要优化");
        } else {
            System.out.println("✅ 数据类型映射性能良好");
        }
    }
    
    @Test
    void benchmarkFunctionMapping(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 达梦函数映射性能测试
        long damengTime = measureTime(() -> {
            for (int i = 0; i < 10000; i++) {
                for (String function : testFunctions) {
                    damengDialect.mapFunction(function);
                }
            }
        });
        
        // 金仓函数映射性能测试
        long kingbaseTime = measureTime(() -> {
            for (int i = 0; i < 10000; i++) {
                for (String function : testFunctions) {
                    kingbaseDialect.mapFunction(function);
                }
            }
        });
        
        // FunctionMapper性能测试
        long mapperTime = measureTime(() -> {
            for (int i = 0; i < 10000; i++) {
                for (String function : testFunctions) {
                    functionMapper.mapFunction(function, new ArrayList<>(), damengDialect);
                }
            }
        });
        
        System.out.printf("达梦函数映射: %d ms (平均 %.3f μs/次)%n", 
            damengTime, (double) damengTime * 1000 / (10000 * testFunctions.size()));
        System.out.printf("金仓函数映射: %d ms (平均 %.3f μs/次)%n", 
            kingbaseTime, (double) kingbaseTime * 1000 / (10000 * testFunctions.size()));
        System.out.printf("FunctionMapper: %d ms (平均 %.3f μs/次)%n", 
            mapperTime, (double) mapperTime * 1000 / (10000 * testFunctions.size()));
        
        // 性能断言
        double avgMapperTime = (double) mapperTime / (10000 * testFunctions.size());
        
        if (avgMapperTime > 2.0) {
            System.out.println("⚠️  警告: FunctionMapper性能可能需要优化");
        } else {
            System.out.println("✅ 函数映射性能良好");
        }
    }
    
    @Test
    void benchmarkSqlConversion(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // SQL转换工具性能测试
        long conversionTime = measureTime(() -> {
            for (int i = 0; i < 1000; i++) {
                for (String sql : testSqlStatements) {
                    SqlConversionUtils.convertFunctionsWithMapper(sql, damengDialect);
                    SqlConversionUtils.convertIdentifierQuotes(sql, "dameng");
                    SqlConversionUtils.formatSqlSpacing(sql);
                }
            }
        });
        
        System.out.printf("SQL转换工具: %d ms (平均 %.3f ms/次)%n", 
            conversionTime, (double) conversionTime / (1000 * testSqlStatements.size()));
        
        // 性能断言 - 单次SQL转换应该在10毫秒以内
        double avgConversionTime = (double) conversionTime / (1000 * testSqlStatements.size());
        
        if (avgConversionTime > 10.0) {
            System.out.println("⚠️  警告: SQL转换性能可能需要优化");
        } else {
            System.out.println("✅ SQL转换性能良好");
        }
    }
    
    @Test
    void benchmarkMemoryUsage(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 强制垃圾回收
        System.gc();
        long beforeMemory = getUsedMemory();
        
        // 创建大量方言实例
        List<SqlDialect> dialects = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            dialects.add(new DamengDialect());
            dialects.add(new KingbaseDialect());
        }
        
        long afterMemory = getUsedMemory();
        long memoryUsed = afterMemory - beforeMemory;
        
        System.out.printf("创建2000个方言实例内存使用: %.2f MB%n", memoryUsed / 1024.0 / 1024.0);
        System.out.printf("平均每个实例: %.2f KB%n", memoryUsed / 2000.0 / 1024.0);
        
        // 内存使用断言 - 每个实例应该小于10KB
        double avgMemoryPerInstance = memoryUsed / 2000.0 / 1024.0;
        
        if (avgMemoryPerInstance > 10.0) {
            System.out.println("⚠️  警告: 方言实例内存使用可能过高");
        } else {
            System.out.println("✅ 内存使用合理");
        }
        
        // 清理
        dialects.clear();
        System.gc();
    }
    
    /**
     * 测量执行时间
     */
    private long measureTime(Runnable task) {
        long startTime = System.nanoTime();
        task.run();
        long endTime = System.nanoTime();
        return TimeUnit.NANOSECONDS.toMillis(endTime - startTime);
    }
    
    /**
     * 获取已使用内存
     */
    private long getUsedMemory() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }
    
    /**
     * 打印性能总结
     */
    @Test
    void printPerformanceSummary(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        System.out.println("性能基准测试总结:");
        System.out.println("- 标识符引用: 应该 < 1ms/次");
        System.out.println("- 数据类型映射: 应该 < 1ms/次");
        System.out.println("- 函数映射: 应该 < 2ms/次");
        System.out.println("- SQL转换: 应该 < 10ms/次");
        System.out.println("- 内存使用: 应该 < 10KB/实例");
        System.out.println();
        System.out.println("基于 .augment/rules/rule-db.md 的性能要求:");
        System.out.println("- 启动时间无明显增加");
        System.out.println("- 保持轻量级特性");
        System.out.println("- 不引入重量级依赖");
    }
}
