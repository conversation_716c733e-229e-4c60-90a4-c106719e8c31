package com.xylink.sqltranspiler.demo;

import com.xylink.sqltranspiler.core.validation.StrictSqlValidator;
import com.xylink.sqltranspiler.core.validation.StrictValidationResult;

/**
 * LIMIT/OFFSET误报修复演示
 *
 * 基于MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/select.html
 *
 * MySQL官方文档描述：
 * "The LIMIT clause can be used to constrain the number of rows returned by the SELECT statement.
 *  LIMIT takes one or two numeric arguments, which must both be nonnegative integer constants"
 *
 * 演示修复前后的差异：
 * - 修复前：ALTER TABLE语句会误报LIMIT/OFFSET相关告警
 * - 修复后：只有实际包含大LIMIT/OFFSET值的SQL才会产生告警
 *
 * 严格遵循官方文档：基于MySQL官方文档进行实现
 * 测试驱动开发：当测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 * 不妥协代码质量：坚持正确的实现，确保功能的准确性和完整性
 */
public class LimitOffsetFixDemo {
    
    public static void main(String[] args) {
        StrictSqlValidator validator = new StrictSqlValidator();
        
        System.out.println("=".repeat(80));
        System.out.println("LIMIT/OFFSET 误报修复演示");
        System.out.println("=".repeat(80));
        
        // 测试用户提供的实际SQL（之前会误报）
        testUserProvidedSql(validator);
        
        // 测试实际的大LIMIT值（应该报警）
        testLargeLimitSql(validator);
        
        // 测试实际的大OFFSET值（应该报警）
        testLargeOffsetSql(validator);
        
        // 测试注释中的LIMIT/OFFSET（不应该报警）
        testCommentsWithLimitOffset(validator);
        
        // 测试字符串中的LIMIT/OFFSET（不应该报警）
        testStringsWithLimitOffset(validator);
        
        System.out.println("\n" + "=".repeat(80));
        System.out.println("演示完成！修复成功避免了误报问题。");
        System.out.println("=".repeat(80));
    }
    
    private static void testUserProvidedSql(StrictSqlValidator validator) {
        System.out.println("\n1. 测试用户提供的ALTER TABLE语句（修复前会误报）");
        System.out.println("-".repeat(60));
        
        String sql = """
            -- 20250627_0016.sql
            alter table ainemo.libra_special_feature_user
                add data_type int default 0 not null;
            
            alter table ainemo.libra_special_feature_user
                add dept_name varchar(255) default '' not null;
            alter table ainemo.libra_special_feature_user
                add update_time datetime default now() null on update now();
            """;
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        boolean hasLimitOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit") || 
                     warning.getMessage().toLowerCase().contains("offset") ||
                     warning.getMessage().toLowerCase().contains("大结果集"));
        
        System.out.println("SQL: " + sql.trim());
        System.out.println("是否有LIMIT/OFFSET相关告警: " + (hasLimitOffsetWarning ? "是 ❌" : "否 ✅"));
        
        if (hasLimitOffsetWarning) {
            System.out.println("相关告警:");
            result.getWarnings().stream()
                .filter(warning -> warning.getMessage().toLowerCase().contains("limit") || 
                        warning.getMessage().toLowerCase().contains("offset") ||
                        warning.getMessage().toLowerCase().contains("大结果集"))
                .forEach(warning -> System.out.println("  - " + warning.getMessage()));
        }
    }
    
    private static void testLargeLimitSql(StrictSqlValidator validator) {
        System.out.println("\n2. 测试大LIMIT值（应该产生告警）");
        System.out.println("-".repeat(60));
        
        String sql = "SELECT * FROM users LIMIT 5000;";
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        boolean hasLimitWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大结果集") && 
                     warning.getMessage().contains("LIMIT"));
        
        System.out.println("SQL: " + sql);
        System.out.println("是否有大结果集告警: " + (hasLimitWarning ? "是 ✅" : "否 ❌"));
        
        if (hasLimitWarning) {
            result.getWarnings().stream()
                .filter(warning -> warning.getMessage().contains("大结果集"))
                .forEach(warning -> System.out.println("  告警: " + warning.getMessage()));
        }
    }
    
    private static void testLargeOffsetSql(StrictSqlValidator validator) {
        System.out.println("\n3. 测试大OFFSET值（应该产生告警）");
        System.out.println("-".repeat(60));
        
        String sql = "SELECT * FROM users LIMIT 10 OFFSET 2000;";
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        boolean hasOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().contains("大偏移量") && 
                     warning.getMessage().contains("OFFSET"));
        
        System.out.println("SQL: " + sql);
        System.out.println("是否有大偏移量告警: " + (hasOffsetWarning ? "是 ✅" : "否 ❌"));
        
        if (hasOffsetWarning) {
            result.getWarnings().stream()
                .filter(warning -> warning.getMessage().contains("大偏移量"))
                .forEach(warning -> System.out.println("  告警: " + warning.getMessage()));
        }
    }
    
    private static void testCommentsWithLimitOffset(StrictSqlValidator validator) {
        System.out.println("\n4. 测试注释中的LIMIT/OFFSET（不应该产生告警）");
        System.out.println("-".repeat(60));
        
        String sql = """
            -- This query uses LIMIT 5000 for testing
            /* OFFSET 2000 is mentioned here */
            SELECT * FROM users WHERE id = 1;
            """;
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        boolean hasLimitOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit") || 
                     warning.getMessage().toLowerCase().contains("offset") ||
                     warning.getMessage().toLowerCase().contains("大结果集"));
        
        System.out.println("SQL: " + sql.trim());
        System.out.println("是否有LIMIT/OFFSET相关告警: " + (hasLimitOffsetWarning ? "是 ❌" : "否 ✅"));
    }
    
    private static void testStringsWithLimitOffset(StrictSqlValidator validator) {
        System.out.println("\n5. 测试字符串中的LIMIT/OFFSET（不应该产生告警）");
        System.out.println("-".repeat(60));
        
        String sql = """
            INSERT INTO logs (message) VALUES ('Query used LIMIT 5000');
            UPDATE config SET value = "OFFSET 2000" WHERE key = 'pagination';
            """;
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        boolean hasLimitOffsetWarning = result.getWarnings().stream()
            .anyMatch(warning -> warning.getMessage().toLowerCase().contains("limit") || 
                     warning.getMessage().toLowerCase().contains("offset") ||
                     warning.getMessage().toLowerCase().contains("大结果集"));
        
        System.out.println("SQL: " + sql.trim());
        System.out.println("是否有LIMIT/OFFSET相关告警: " + (hasLimitOffsetWarning ? "是 ❌" : "否 ✅"));
    }
}
