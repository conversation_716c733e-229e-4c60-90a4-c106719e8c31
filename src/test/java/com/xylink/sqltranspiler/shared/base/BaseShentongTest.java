package com.xylink.sqltranspiler.shared.base;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;

import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;

/**
 * 神通数据库测试基类
 * 
 * 提供神通数据库转换测试的专用功能：
 * 1. 初始化神通生成器
 * 2. 提供神通特定的断言方法
 * 3. 提供神通转换的便捷方法
 * 
 * 严格遵循神通官方文档：
 * - 神通官方文档：@shentong.md
 * 
 * <AUTHOR>
 */
public abstract class BaseShentongTest extends BaseTranspilerTest {

    protected ShentongGenerator generator;

    @BeforeEach
    @Override
    protected void setUp() {
        super.setUp();
        generator = new ShentongGenerator();
    }

    /**
     * 转换MySQL SQL为神通SQL
     */
    protected String convertMySqlToShentong(String mysqlSql) {
        return convertMySqlTo(mysqlSql, "shentong");
    }

    /**
     * 验证神通特定的转换要求
     * 基于神通数据库的基本要求
     */
    protected void assertShentongConversionRequirements(String shentongSql) {
        assertBasicConversionRequirements(shentongSql);
        
        // 验证神通标识符规范 - 使用双引号
        assertTrue(shentongSql.contains("\"") || !containsIdentifiers(shentongSql), 
            "神通数据库应使用双引号标识符");
        
        // 验证不包含MySQL特有的语法
        assertFalse(shentongSql.contains("ENGINE="), "不应包含MySQL的ENGINE语法");
        assertFalse(shentongSql.contains("DEFAULT CHARSET"), "不应包含MySQL的DEFAULT CHARSET语法");
        
        // 验证系统保留前缀冲突检测
        if (shentongSql.contains("SYS_")) {
            log.warn("⚠️ 警告：SYS_开头的标识符可能与神通系统对象冲突");
        }
        
        if (shentongSql.contains("V_SYS_")) {
            log.warn("⚠️ 警告：V_SYS_开头的标识符可能与神通系统视图冲突");
        }
    }

    /**
     * 验证神通数据类型转换
     */
    protected void assertShentongDataTypeConversions(String shentongSql) {
        assertDataTypeConversions(shentongSql);
        
        // 神通特定的数据类型验证
        if (shentongSql.contains("AUTO_INCREMENT")) {
            log.warn("发现未转换的AUTO_INCREMENT，神通应转换为SERIAL或序列");
        }
        
        if (shentongSql.contains("TINYINT(1)")) {
            log.warn("发现未转换的TINYINT(1)，神通应转换为BOOLEAN或CHAR(1)");
        }
        
        // 验证神通支持的数据类型
        if (shentongSql.contains("CHARACTER") || shentongSql.contains("VARCHAR")) {
            log.info("发现字符串类型，神通支持CHARACTER(n), VARCHAR(n), TEXT等");
        }
        
        if (shentongSql.contains("SERIAL") || shentongSql.contains("BIGSERIAL")) {
            log.info("发现序列类型，神通支持SERIAL和BIGSERIAL");
        }
    }

    /**
     * 验证神通伪列支持
     */
    protected void assertShentongPseudoColumnSupport(String shentongSql) {
        // 神通支持的伪列：ROWID、SYSATTR_ROWVERSION、ROWNUM
        if (shentongSql.contains("ROWID")) {
            log.info("发现ROWID伪列，神通原生支持");
        }
        
        if (shentongSql.contains("ROWNUM")) {
            log.info("发现ROWNUM伪列，神通原生支持");
        }
        
        if (shentongSql.contains("SYSATTR_ROWVERSION")) {
            log.info("发现SYSATTR_ROWVERSION伪列，神通原生支持");
        }
    }

    /**
     * 验证神通函数转换
     */
    protected void assertShentongFunctionConversions(String shentongSql) {
        // 检查MySQL特有函数是否正确转换
        if (shentongSql.contains("IFNULL")) {
            log.warn("发现未转换的IFNULL函数，神通应转换为NVL或COALESCE");
        }
        
        if (shentongSql.contains("DATE_FORMAT")) {
            log.warn("发现未转换的DATE_FORMAT函数，神通应转换为TO_CHAR");
        }
        
        if (shentongSql.contains("LAST_INSERT_ID")) {
            log.info("发现LAST_INSERT_ID函数，神通支持");
        }
        
        if (shentongSql.contains("CURRENT_DATE")) {
            log.info("发现CURRENT_DATE函数，神通支持");
        }
    }

    /**
     * 验证神通分页查询转换
     */
    protected void assertShentongPaginationConversion(String shentongSql) {
        if (shentongSql.contains("LIMIT")) {
            log.warn("发现未转换的LIMIT语法，神通应转换为ROWNUM方式");
        }
        
        if (shentongSql.contains("ROWNUM")) {
            log.info("发现ROWNUM分页，神通原生支持");
        }
    }

    /**
     * 验证神通Oracle兼容性特性
     */
    protected void assertShentongOracleCompatibility(String shentongSql) {
        // 神通支持部分Oracle语法
        if (shentongSql.contains("DUAL")) {
            log.info("发现DUAL表，神通支持Oracle兼容语法");
        }
        
        if (shentongSql.contains("DECODE")) {
            log.info("发现DECODE函数，神通支持Oracle兼容语法");
        }
        
        if (shentongSql.contains("CONNECT BY")) {
            log.info("发现层次查询，神通支持Oracle兼容语法");
        }
        
        if (shentongSql.contains("START WITH")) {
            log.info("发现层次查询起始条件，神通支持Oracle兼容语法");
        }
    }

    /**
     * 验证神通不支持的特性
     */
    protected void assertShentongUnsupportedFeatures(String shentongSql) {
        // 检查神通不支持的特性
        if (shentongSql.contains("GEOMETRY") || shentongSql.contains("POLYGON")) {
            log.warn("⚠️ 神通数据库不支持：空间数据类型");
        }
        
        if (shentongSql.contains("USING HASH")) {
            log.warn("⚠️ 神通数据库不支持：HASH索引算法");
        }
        
        if (shentongSql.contains("USING RTREE")) {
            log.warn("⚠️ 神通数据库不支持：RTREE索引算法");
        }
        
        if (shentongSql.contains("PARTITION") && shentongSql.contains("HASH")) {
            log.warn("⚠️ 神通数据库不支持：HASH分区的合并/分裂操作");
        }
    }

    /**
     * 验证神通SQL92标准合规性
     */
    protected void assertShentongSQL92Compliance(String shentongSql) {
        // 神通基本支持SQL92的入门级和过渡级标准
        log.info("验证神通SQL92标准合规性");
        
        // 基本的SQL92语法应该支持
        if (shentongSql.contains("SELECT") || shentongSql.contains("INSERT") || 
            shentongSql.contains("UPDATE") || shentongSql.contains("DELETE")) {
            log.info("基本DML语句符合SQL92标准");
        }
        
        if (shentongSql.contains("CREATE TABLE") || shentongSql.contains("ALTER TABLE")) {
            log.info("基本DDL语句符合SQL92标准");
        }
    }

    /**
     * 检查SQL是否包含标识符
     */
    private boolean containsIdentifiers(String sql) {
        return sql.matches(".*\\b(CREATE|TABLE|INDEX|ALTER)\\b.*");
    }
}
