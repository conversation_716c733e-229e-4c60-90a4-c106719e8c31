package com.xylink.sqltranspiler.validation;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.validation.StrictSqlValidator;
import com.xylink.sqltranspiler.core.validation.StrictValidationResult;

/**
 * 扩展校验规则测试
 * 基于官方文档的校验规则测试用例
 * 
 * 测试覆盖：
 * 1. MySQL 8.4 高级语法校验
 * 2. 达梦数据库扩展校验
 * 3. 金仓数据库扩展校验
 * 4. 神通数据库扩展校验
 * 5. 安全性校验
 * 6. 性能优化校验
 * 7. 兼容性校验
 */
public class ExtendedValidationRulesTest {

    private StrictSqlValidator validator;

    @BeforeEach
    void setUp() {
        validator = new StrictSqlValidator();
    }

    @Test
    @DisplayName("测试MySQL 8.4窗口函数校验 - 基于MySQL官方文档")
    void testMySQLWindowFunctionValidation() {
        // 测试有效的窗口函数语法
        String validWindowFunction = """
            SELECT 
                employee_id,
                salary,
                ROW_NUMBER() OVER (PARTITION BY department_id ORDER BY salary DESC) as rank_in_dept,
                LAG(salary, 1) OVER (ORDER BY salary) as prev_salary
            FROM employees;
            """;
        
        StrictValidationResult result = validator.validate(validWindowFunction, "mysql", "dameng");
        assertFalse(result.hasErrors(), "有效的窗口函数语法不应产生错误");

        // 测试无效的窗口函数语法
        String invalidWindowFunction = """
            SELECT
                employee_id,
                ROW_NUMBER() OVER () as invalid_rank
            FROM employees;
            """;

        result = validator.validate(invalidWindowFunction, "mysql", "dameng");
        // 注意：这里可能需要根据实际实现调整断言
    }

    @Test
    @DisplayName("测试MySQL 8.4 CTE递归深度校验 - 基于MySQL官方文档")
    void testMySQLCTERecursionValidation() {
        // 测试正常的CTE语法
        String validCTE = """
            WITH RECURSIVE employee_hierarchy AS (
                SELECT employee_id, manager_id, name, 1 as level
                FROM employees 
                WHERE manager_id IS NULL
                
                UNION ALL
                
                SELECT e.employee_id, e.manager_id, e.name, eh.level + 1
                FROM employees e
                INNER JOIN employee_hierarchy eh ON e.manager_id = eh.employee_id
                WHERE eh.level < 10
            )
            SELECT * FROM employee_hierarchy;
            """;
        
        StrictValidationResult result = validator.validate(validCTE, "mysql", "dameng");
        assertFalse(result.hasErrors(), "正常的CTE语法不应产生错误");
    }

    @Test
    @DisplayName("测试达梦数据库IDENTITY列校验 - 基于达梦官方文档")
    void testDamengIdentityValidation() {
        // 测试IDENTITY列定义
        String identityColumn = """
            CREATE TABLE test_table (
                id INT IDENTITY(1,1) PRIMARY KEY,
                name VARCHAR(100)
            );
            """;

        StrictValidationResult result = validator.validate(identityColumn, "mysql", "dameng");
        assertFalse(result.hasErrors(), "达梦IDENTITY列定义应该有效");

        // 测试插入IDENTITY列的显式值
        String insertWithIdentity = """
            INSERT INTO test_table (id, name) VALUES (1, 'Test');
            """;

        result = validator.validate(insertWithIdentity, "mysql", "dameng");
        // 应该提示需要SET IDENTITY_INSERT ON
        assertTrue(result.hasSuggestions(), "插入IDENTITY列显式值应该有建议");
    }

    @Test
    @DisplayName("测试金仓数据库MySQL兼容性校验 - 基于金仓官方文档")
    void testKingbaseCompatibilityValidation() {
        // 测试MySQL特有语法在金仓数据库中的兼容性
        String mysqlSpecificSyntax = """
            CREATE TABLE test_table (
                id INT AUTO_INCREMENT PRIMARY KEY,
                amount DECIMAL(10,2) UNSIGNED,
                status ENUM('active', 'inactive'),
                data LONGTEXT
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        StrictValidationResult result = validator.validate(mysqlSpecificSyntax, "mysql", "kingbase");
        // 应该有兼容性警告
        assertTrue(result.hasWarnings() || result.hasSuggestions(),
                  "MySQL特有语法在金仓数据库中应该有兼容性提示");
    }

    @Test
    @DisplayName("测试神通数据库ROWNUM校验 - 基于神通数据库文档")
    void testShentongRownumValidation() {
        // 测试正确的ROWNUM使用
        String validRownum = """
            SELECT * FROM employees WHERE ROWNUM <= 10;
            """;

        StrictValidationResult result = validator.validate(validRownum, "mysql", "shentong");
        assertFalse(result.hasErrors(), "正确的ROWNUM使用不应产生错误");

        // 测试错误的ROWNUM使用（在ORDER BY之后）
        String invalidRownum = """
            SELECT * FROM employees ORDER BY salary DESC WHERE ROWNUM <= 10;
            """;

        result = validator.validate(invalidRownum, "mysql", "shentong");
        assertTrue(result.hasErrors(), "ORDER BY之后使用ROWNUM应该产生错误");
    }

    @Test
    @DisplayName("测试SQL注入安全校验 - 基于安全最佳实践")
    void testSQLInjectionValidation() {
        // 测试危险的SQL模式
        String dangerousSQL = """
            SELECT * FROM users WHERE id = 1 OR 1=1;
            """;
        
        StrictValidationResult result = validator.validate(dangerousSQL, "mysql", "dameng");
        assertTrue(result.hasWarnings() || result.hasErrors(),
                  "危险的SQL模式应该被检测到");

        // 测试危险函数
        String dangerousFunction = """
            SELECT LOAD_FILE('/etc/passwd') as sensitive_data;
            """;

        result = validator.validate(dangerousFunction, "mysql", "dameng");
        assertTrue(result.hasErrors(), "危险函数使用应该产生错误");
    }

    @Test
    @DisplayName("测试性能优化校验 - 基于性能最佳实践")
    void testPerformanceValidation() {
        // 测试低效的查询模式
        String inefficientQuery = """
            SELECT * FROM large_table ORDER BY RAND() LIMIT 10;
            """;

        StrictValidationResult result = validator.validate(inefficientQuery, "mysql", "dameng");
        assertTrue(result.hasWarnings(), "低效的查询模式应该产生警告");

        // 测试LIKE模式
        String likePattern = """
            SELECT * FROM users WHERE name LIKE '%john%';
            """;

        result = validator.validate(likePattern, "mysql", "dameng");
        assertTrue(result.hasWarnings(), "低效的LIKE模式应该产生警告");
    }

    @Test
    @DisplayName("测试字符集和排序规则校验 - 基于各数据库官方文档")
    void testCharsetCollationValidation() {
        // 测试字符集不匹配
        String charsetMismatch = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                name VARCHAR(100) CHARACTER SET latin1 COLLATE utf8_general_ci
            );
            """;
        
        StrictValidationResult result = validator.validate(charsetMismatch, "mysql", "dameng");
        assertTrue(result.hasWarnings() || result.hasErrors(),
                  "字符集和排序规则不匹配应该被检测到");
    }

    @Test
    @DisplayName("测试数据类型长度限制校验 - 基于各数据库官方文档")
    void testDataTypeLengthValidation() {
        // 测试VARCHAR长度超限
        String oversizedVarchar = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                description VARCHAR(100000)
            );
            """;

        StrictValidationResult result = validator.validate(oversizedVarchar, "mysql", "dameng");
        // 根据目标数据库可能有不同的限制

        // 测试DECIMAL精度超限
        String oversizedDecimal = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                amount DECIMAL(100, 50)
            );
            """;

        result = validator.validate(oversizedDecimal, "mysql", "dameng");
        assertTrue(result.hasErrors() || result.hasWarnings(), 
                  "超出精度限制的DECIMAL应该被检测到");
    }

    @Test
    @DisplayName("测试索引类型校验 - 基于各数据库官方文档")
    void testIndexTypeValidation() {
        // 测试不支持的索引类型
        String unsupportedIndex = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                content TEXT,
                FULLTEXT INDEX ft_content (content)
            );
            """;
        
        StrictValidationResult result = validator.validate(unsupportedIndex, "mysql", "dameng");
        // 达梦数据库可能不支持FULLTEXT索引

        // 测试空间索引
        String spatialIndex = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                location GEOMETRY,
                SPATIAL INDEX sp_location (location)
            );
            """;

        result = validator.validate(spatialIndex, "mysql", "dameng");
        // 检查空间索引支持情况
    }

    @Test
    @DisplayName("测试分区表校验 - 基于各数据库官方文档")
    void testPartitionValidation() {
        // 测试分区表定义
        String partitionedTable = """
            CREATE TABLE sales (
                id INT,
                sale_date DATE,
                amount DECIMAL(10,2)
            )
            PARTITION BY RANGE (YEAR(sale_date)) (
                PARTITION p2020 VALUES LESS THAN (2021),
                PARTITION p2021 VALUES LESS THAN (2022),
                PARTITION p2022 VALUES LESS THAN (2023)
            );
            """;

        StrictValidationResult result = validator.validate(partitionedTable, "mysql", "dameng");
        // 检查分区语法支持情况
    }
}
