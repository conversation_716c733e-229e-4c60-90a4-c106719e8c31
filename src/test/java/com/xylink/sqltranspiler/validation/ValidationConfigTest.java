package com.xylink.sqltranspiler.validation;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

/**
 * 校验配置测试
 * 验证strict-validation-rules.properties文件的完整性和正确性
 * 
 * 基于官方文档要求：
 * 1. 必须遵守数据库规则，不允许推测
 * 2. 必须查看官方文档，并将准确的官方描述写入到测试用例和代码注释中
 * 3. 严格遵循官方文档：基于MySQL、达梦、金仓、神通的官方文档进行实现
 */
public class ValidationConfigTest {

    private Properties loadValidationConfig() throws IOException {
        Properties props = new Properties();
        try (InputStream is = getClass().getClassLoader().getResourceAsStream("strict-validation-rules.properties")) {
            assertNotNull(is, "strict-validation-rules.properties文件必须存在");
            props.load(is);
        }
        return props;
    }

    @Test
    @DisplayName("验证MySQL 8.4官方文档配置完整性")
    void testMySQLConfigCompleteness() throws IOException {
        Properties config = loadValidationConfig();
        
        // 验证MySQL 8.4基础限制配置 - 基于MySQL 8.4官方文档
        assertNotNull(config.getProperty("validation.mysql.max_table_name_length"), 
                     "MySQL表名最大长度必须配置");
        assertEquals("64", config.getProperty("validation.mysql.max_table_name_length"),
                    "MySQL 8.4官方文档规定表名最大长度为64字符");
        
        assertNotNull(config.getProperty("validation.mysql.max_column_name_length"),
                     "MySQL列名最大长度必须配置");
        assertEquals("64", config.getProperty("validation.mysql.max_column_name_length"),
                    "MySQL 8.4官方文档规定列名最大长度为64字符");
        
        // 验证MySQL 8.4数据类型限制 - 基于MySQL 8.4官方文档
        assertNotNull(config.getProperty("validation.mysql.varchar_max_length"),
                     "MySQL VARCHAR最大长度必须配置");
        assertEquals("65535", config.getProperty("validation.mysql.varchar_max_length"),
                    "MySQL 8.4官方文档规定VARCHAR最大长度为65535字符");
        
        // 验证MySQL 8.4窗口函数配置 - 基于MySQL 8.4官方文档
        assertNotNull(config.getProperty("validation.mysql.window_functions"),
                     "MySQL窗口函数列表必须配置");
        String windowFunctions = config.getProperty("validation.mysql.window_functions");
        assertTrue(windowFunctions.contains("ROW_NUMBER"),
                  "MySQL 8.4官方文档支持ROW_NUMBER窗口函数");
        assertTrue(windowFunctions.contains("RANK"),
                  "MySQL 8.4官方文档支持RANK窗口函数");
        assertTrue(windowFunctions.contains("DENSE_RANK"),
                  "MySQL 8.4官方文档支持DENSE_RANK窗口函数");
        
        // 验证MySQL 8.4 CTE配置 - 基于MySQL 8.4官方文档
        assertNotNull(config.getProperty("validation.mysql.max_cte_recursion_depth"),
                     "MySQL CTE递归深度限制必须配置");
        assertEquals("1000", config.getProperty("validation.mysql.max_cte_recursion_depth"),
                    "MySQL 8.4官方文档规定CTE递归深度限制为1000");
    }

    @Test
    @DisplayName("验证达梦数据库官方文档配置完整性")
    void testDamengConfigCompleteness() throws IOException {
        Properties config = loadValidationConfig();
        
        // 验证达梦数据库版本配置 - 基于达梦官方文档
        assertNotNull(config.getProperty("validation.dameng.supported_versions"),
                     "达梦数据库支持版本必须配置");
        String supportedVersions = config.getProperty("validation.dameng.supported_versions");
        assertTrue(supportedVersions.contains("DM8.0"),
                  "达梦官方文档支持DM8.0版本");
        assertTrue(supportedVersions.contains("DM8.1"),
                  "达梦官方文档支持DM8.1版本");
        
        // 验证达梦数据库字符集配置 - 基于达梦官方文档
        assertNotNull(config.getProperty("validation.dameng.charset_encoding"),
                     "达梦数据库字符集编码必须配置");
        String charsetEncoding = config.getProperty("validation.dameng.charset_encoding");
        assertTrue(charsetEncoding.contains("UTF8"),
                  "达梦官方文档支持UTF8字符集");
        assertTrue(charsetEncoding.contains("GBK"),
                  "达梦官方文档支持GBK字符集");
        
        // 验证达梦数据库数据类型配置 - 基于达梦官方文档
        assertNotNull(config.getProperty("validation.dameng.types.extended"),
                     "达梦数据库扩展数据类型必须配置");
        String extendedTypes = config.getProperty("validation.dameng.types.extended");
        assertTrue(extendedTypes.contains("LONGVARCHAR"),
                  "达梦官方文档支持LONGVARCHAR类型");
        assertTrue(extendedTypes.contains("BFILE"),
                  "达梦官方文档支持BFILE类型");
        
        // 验证达梦数据库索引配置 - 基于达梦官方文档
        assertNotNull(config.getProperty("validation.dameng.index_types"),
                     "达梦数据库索引类型必须配置");
        String indexTypes = config.getProperty("validation.dameng.index_types");
        assertTrue(indexTypes.contains("BTREE"),
                  "达梦官方文档支持BTREE索引");
        assertTrue(indexTypes.contains("BITMAP"),
                  "达梦官方文档支持BITMAP索引");
    }

    @Test
    @DisplayName("验证金仓数据库官方文档配置完整性")
    void testKingbaseConfigCompleteness() throws IOException {
        Properties config = loadValidationConfig();
        
        // 验证金仓数据库版本配置 - 基于金仓官方文档
        assertNotNull(config.getProperty("validation.kingbase.supported_versions"),
                     "金仓数据库支持版本必须配置");
        String supportedVersions = config.getProperty("validation.kingbase.supported_versions");
        assertTrue(supportedVersions.contains("V8R6"),
                  "金仓官方文档支持V8R6版本");
        assertTrue(supportedVersions.contains("V9R4"),
                  "金仓官方文档支持V9R4版本");
        
        // 验证金仓数据库兼容模式配置 - 基于金仓官方文档
        assertNotNull(config.getProperty("validation.kingbase.compatibility_modes"),
                     "金仓数据库兼容模式必须配置");
        String compatibilityModes = config.getProperty("validation.kingbase.compatibility_modes");
        assertTrue(compatibilityModes.contains("MYSQL"),
                  "金仓官方文档支持MySQL兼容模式");
        assertTrue(compatibilityModes.contains("POSTGRESQL"),
                  "金仓官方文档支持PostgreSQL兼容模式");
        
        // 验证金仓数据库MySQL兼容语法 - 基于金仓官方文档
        assertNotNull(config.getProperty("validation.kingbase.mysql_compatible_syntax"),
                     "金仓数据库MySQL兼容语法必须配置");
        String mysqlCompatSyntax = config.getProperty("validation.kingbase.mysql_compatible_syntax");
        assertTrue(mysqlCompatSyntax.contains("AUTO_INCREMENT"),
                  "金仓官方文档支持AUTO_INCREMENT语法");
        assertTrue(mysqlCompatSyntax.contains("UNSIGNED"),
                  "金仓官方文档支持UNSIGNED语法");
    }

    @Test
    @DisplayName("验证神通数据库文档配置完整性")
    void testShentongConfigCompleteness() throws IOException {
        Properties config = loadValidationConfig();
        
        // 验证神通数据库版本配置 - 基于神通数据库文档
        assertNotNull(config.getProperty("validation.shentong.supported_versions"),
                     "神通数据库支持版本必须配置");
        String supportedVersions = config.getProperty("validation.shentong.supported_versions");
        assertTrue(supportedVersions.contains("8.0"),
                  "神通数据库文档支持8.0版本");
        assertTrue(supportedVersions.contains("9.0"),
                  "神通数据库文档支持9.0版本");
        
        // 验证神通数据库伪列配置 - 基于神通数据库文档
        assertNotNull(config.getProperty("validation.shentong.pseudocolumns"),
                     "神通数据库伪列必须配置");
        String pseudocolumns = config.getProperty("validation.shentong.pseudocolumns");
        assertTrue(pseudocolumns.contains("ROWID"),
                  "神通数据库文档支持ROWID伪列");
        assertTrue(pseudocolumns.contains("ROWNUM"),
                  "神通数据库文档支持ROWNUM伪列");
        
        // 验证神通数据库序列配置 - 基于神通数据库文档
        assertNotNull(config.getProperty("validation.shentong.sequence_functions"),
                     "神通数据库序列函数必须配置");
        String sequenceFunctions = config.getProperty("validation.shentong.sequence_functions");
        assertTrue(sequenceFunctions.contains("NEXTVAL"),
                  "神通数据库文档支持NEXTVAL函数");
        assertTrue(sequenceFunctions.contains("CURRVAL"),
                  "神通数据库文档支持CURRVAL函数");
    }

    @Test
    @DisplayName("验证错误严重程度配置完整性")
    void testSeverityConfigCompleteness() throws IOException {
        Properties config = loadValidationConfig();
        
        // 验证关键错误严重程度配置
        assertEquals("CRITICAL", config.getProperty("validation.severity.SQL_INJECTION_RISK"),
                    "SQL注入风险必须标记为CRITICAL级别");
        assertEquals("ERROR", config.getProperty("validation.severity.DANGEROUS_FUNCTION_USAGE"),
                    "危险函数使用必须标记为ERROR级别");
        assertEquals("WARNING", config.getProperty("validation.severity.PERFORMANCE_ISSUE"),
                    "性能问题必须标记为WARNING级别");
        assertEquals("WARNING", config.getProperty("validation.severity.COMPATIBILITY_ISSUE"),
                    "兼容性问题必须标记为WARNING级别");
        
        // 验证数据库特定错误严重程度
        assertEquals("ERROR", config.getProperty("validation.severity.UNSUPPORTED_ENGINE"),
                    "不支持的存储引擎必须标记为ERROR级别");
        assertEquals("ERROR", config.getProperty("validation.severity.INVALID_PARTITION_KEY"),
                    "无效分区键必须标记为ERROR级别");
    }

    @Test
    @DisplayName("验证修复建议配置完整性")
    void testFixSuggestionConfigCompleteness() throws IOException {
        Properties config = loadValidationConfig();
        
        // 验证通用修复建议
        assertNotNull(config.getProperty("validation.fix.SQL_INJECTION_RISK"),
                     "SQL注入风险修复建议必须配置");
        assertNotNull(config.getProperty("validation.fix.DANGEROUS_FUNCTION_USAGE"),
                     "危险函数使用修复建议必须配置");
        assertNotNull(config.getProperty("validation.fix.PERFORMANCE_ISSUE"),
                     "性能问题修复建议必须配置");
        
        // 验证数据库特定修复建议
        assertNotNull(config.getProperty("validation.fix.dameng.IDENTITY_INSERT_REQUIRED"),
                     "达梦数据库IDENTITY插入修复建议必须配置");
        assertNotNull(config.getProperty("validation.fix.kingbase.MYSQL_COMPATIBILITY"),
                     "金仓数据库MySQL兼容性修复建议必须配置");
        assertNotNull(config.getProperty("validation.fix.shentong.ROWNUM_USAGE"),
                     "神通数据库ROWNUM使用修复建议必须配置");
    }

    @Test
    @DisplayName("验证官方文档链接配置")
    void testDocumentationLinksConfig() throws IOException {
        Properties config = loadValidationConfig();
        
        // 验证官方文档链接配置
        assertEquals("https://dev.mysql.com/doc/refman/8.4/en/",
                    config.getProperty("validation.docs.mysql"),
                    "MySQL官方文档链接必须正确");
        assertEquals("https://eco.dameng.com/document/dm/zh-cn/sql-dev/",
                    config.getProperty("validation.docs.dameng"),
                    "达梦官方文档链接必须正确");
        assertEquals("https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/",
                    config.getProperty("validation.docs.kingbase"),
                    "金仓官方文档链接必须正确");
        
        // 验证版本信息配置
        assertEquals("8.4", config.getProperty("validation.version.mysql"),
                    "MySQL版本信息必须正确");
        assertEquals("8.2", config.getProperty("validation.version.dameng"),
                    "达梦版本信息必须正确");
        assertEquals("V9R4", config.getProperty("validation.version.kingbase"),
                    "金仓版本信息必须正确");
        assertEquals("9.0", config.getProperty("validation.version.shentong"),
                    "神通版本信息必须正确");
    }

    @Test
    @DisplayName("验证高级校验配置")
    void testAdvancedValidationConfig() throws IOException {
        Properties config = loadValidationConfig();
        
        // 验证动态校验规则启用配置
        assertEquals("true", config.getProperty("validation.dynamic.enable_mysql_specific"),
                    "MySQL特定校验规则必须启用");
        assertEquals("true", config.getProperty("validation.dynamic.enable_dameng_specific"),
                    "达梦特定校验规则必须启用");
        assertEquals("true", config.getProperty("validation.dynamic.enable_kingbase_specific"),
                    "金仓特定校验规则必须启用");
        assertEquals("true", config.getProperty("validation.dynamic.enable_shentong_specific"),
                    "神通特定校验规则必须启用");
        
        // 验证校验规则优先级配置
        assertEquals("1", config.getProperty("validation.priority.security"),
                    "安全性校验优先级必须最高");
        assertEquals("2", config.getProperty("validation.priority.syntax"),
                    "语法校验优先级必须为2");
        assertEquals("3", config.getProperty("validation.priority.performance"),
                    "性能校验优先级必须为3");
        assertEquals("4", config.getProperty("validation.priority.compatibility"),
                    "兼容性校验优先级必须为4");
        
        // 验证校验结果缓存配置
        assertEquals("true", config.getProperty("validation.cache.enable"),
                    "校验结果缓存必须启用");
        assertEquals("3600", config.getProperty("validation.cache.ttl_seconds"),
                    "缓存TTL必须为3600秒");
        assertEquals("10000", config.getProperty("validation.cache.max_entries"),
                    "最大缓存条目数必须为10000");
    }

    @Test
    @DisplayName("验证配置文件结构完整性")
    void testConfigFileStructure() throws IOException {
        Properties config = loadValidationConfig();
        
        // 验证配置文件不为空
        assertFalse(config.isEmpty(), "配置文件不能为空");
        
        // 验证关键配置节存在
        assertTrue(hasPropertiesWithPrefix(config, "validation.mysql."),
                  "MySQL配置节必须存在");
        assertTrue(hasPropertiesWithPrefix(config, "validation.dameng."),
                  "达梦配置节必须存在");
        assertTrue(hasPropertiesWithPrefix(config, "validation.kingbase."),
                  "金仓配置节必须存在");
        assertTrue(hasPropertiesWithPrefix(config, "validation.shentong."),
                  "神通配置节必须存在");
        assertTrue(hasPropertiesWithPrefix(config, "validation.severity."),
                  "错误严重程度配置节必须存在");
        assertTrue(hasPropertiesWithPrefix(config, "validation.fix."),
                  "修复建议配置节必须存在");
    }

    private boolean hasPropertiesWithPrefix(Properties config, String prefix) {
        return config.stringPropertyNames().stream()
                .anyMatch(key -> key.startsWith(prefix));
    }
}
