package com.xylink.sqltranspiler.regression.bugfix;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;
import com.xylink.sqltranspiler.infrastructure.parser.PreprocessingResult;
import com.xylink.sqltranspiler.infrastructure.parser.Preprocessor;

/**
 * 测试字符串内注释符号的保留
 * 
 * 根据MySQL官方文档 (https://dev.mysql.com/doc/refman/8.4/en/string-literals.html)：
 * - 字符串字面量可以包含任何字符，包括注释符号
 * - 字符串内的注释符号不应该被当作真正的注释处理
 * 
 * 根据达梦官方文档 (https://eco.dameng.com/document/dm/zh-cn/pm/)：
 * - 达梦同样支持字符串字面量
 * - 字符串内的内容应该被完整保留
 */
@DisplayName("字符串内注释符号保留测试")
public class StringCommentsPreservationTest {

    @Test
    @DisplayName("预处理阶段应保留字符串内的单行注释符号")
    public void testPreprocessorPreservesSingleLineCommentsInStrings() {
        String sql = "INSERT INTO test VALUES ('value with -- inside', 'another -- comment');";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        
        // 验证字符串内的单行注释符号被保留
        assertTrue(result.cleanedSql().contains("'value with -- inside'"), 
            "字符串内的单行注释符号应该被保留");
        assertTrue(result.cleanedSql().contains("'another -- comment'"), 
            "字符串内的单行注释符号应该被保留");
    }

    @Test
    @DisplayName("预处理阶段应保留字符串内的多行注释符号")
    public void testPreprocessorPreservesMultiLineCommentsInStrings() {
        String sql = "INSERT INTO test VALUES ('value with /* comment */ inside');";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        
        // 验证字符串内的多行注释符号被保留
        assertTrue(result.cleanedSql().contains("'value with /* comment */ inside'"), 
            "字符串内的多行注释符号应该被保留");
    }

    @Test
    @DisplayName("预处理阶段应保留JSON字符串内的注释符号")
    public void testPreprocessorPreservesCommentsInJsonStrings() {
        String sql = "CREATE TABLE test (json_field TEXT DEFAULT '{\"comment\": \"/* 这不是注释 */\"}');";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        
        // 验证JSON字符串内的注释符号被保留
        assertTrue(result.cleanedSql().contains("\"/* 这不是注释 */\""), 
            "JSON字符串内的注释符号应该被保留");
    }

    @Test
    @DisplayName("预处理阶段应保留混合引号字符串内的注释符号")
    public void testPreprocessorPreservesCommentsInMixedQuoteStrings() {
        String sql = "INSERT INTO test VALUES ('single /* comment */', \"double /* comment */\");";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        
        // 验证混合引号字符串内的注释符号被保留
        assertTrue(result.cleanedSql().contains("'single /* comment */'"), 
            "单引号字符串内的注释符号应该被保留");
        assertTrue(result.cleanedSql().contains("\"double /* comment */\""), 
            "双引号字符串内的注释符号应该被保留");
    }

    @Test
    @DisplayName("预处理阶段应保留转义字符串内的注释符号")
    public void testPreprocessorPreservesCommentsInEscapedStrings() {
        String sql = "INSERT INTO test VALUES ('escaped \\' quote /* comment */');";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        
        // 验证转义字符串内的注释符号被保留
        assertTrue(result.cleanedSql().contains("/* comment */"), 
            "转义字符串内的注释符号应该被保留");
    }

    @Test
    @DisplayName("完整转换过程应保留字符串内的注释符号")
    public void testFullTranspilationPreservesCommentsInStrings() {
        String sql = "CREATE TABLE test (" +
                    "  id INT AUTO_INCREMENT, " +
                    "  comment_field VARCHAR(100) DEFAULT '-- 这不是注释', " +
                    "  json_field TEXT DEFAULT '{\"comment\": \"/* 这也不是注释 */\"}' " +
                    "); " +
                    "INSERT INTO test VALUES (1, 'value with -- inside', '{\"key\": \"value\"}');";
        
        Transpiler transpiler = new Transpiler();
        TranspilationResult result = transpiler.transpile(sql, "mysql", "dameng");
        
        String converted = result.translatedSql();
        
        // 验证字符串内的注释符号在完整转换过程中被保留
        assertTrue(converted.contains("'-- 这不是注释'"), 
            "字符串内的单行注释符号应该在转换后被保留");
        assertTrue(converted.contains("'value with -- inside'"), 
            "INSERT语句中字符串内的注释符号应该被保留");
        
        // 验证转换结果符合达梦语法
        assertTrue(converted.contains("IDENTITY(1,1)"), 
            "AUTO_INCREMENT应该转换为IDENTITY");
        assertTrue(converted.contains("test") || converted.contains("TEST"),
            "表名应该正确转换");
    }

    @Test
    @DisplayName("应正确区分真正的注释和字符串内的注释符号")
    public void testDistinguishRealCommentsFromStringComments() {
        String sql = "-- 这是真正的注释\n" +
                    "CREATE TABLE test (\n" +
                    "  id INT, -- 这也是真正的注释\n" +
                    "  comment_field VARCHAR(100) DEFAULT '-- 这不是注释'\n" +
                    "); /* 这是真正的多行注释 */\n" +
                    "INSERT INTO test VALUES (1, 'value with /* 这不是注释 */ inside');";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        String cleaned = result.cleanedSql();
        
        // 验证真正的注释被移除
        assertFalse(cleaned.contains("-- 这是真正的注释"), 
            "真正的单行注释应该被移除");
        assertFalse(cleaned.contains("-- 这也是真正的注释"), 
            "真正的单行注释应该被移除");
        assertFalse(cleaned.contains("/* 这是真正的多行注释 */"), 
            "真正的多行注释应该被移除");
        
        // 验证字符串内的注释符号被保留
        assertTrue(cleaned.contains("'-- 这不是注释'"), 
            "字符串内的注释符号应该被保留");
        assertTrue(cleaned.contains("'value with /* 这不是注释 */ inside'"), 
            "字符串内的注释符号应该被保留");
    }

    @Test
    @DisplayName("复杂嵌套场景下应正确处理注释")
    public void testComplexNestedScenarios() {
        String sql = "CREATE TABLE complex_test (" +
                    "  data JSON DEFAULT '{\"config\": {\"debug\": \"/* not a comment */\", \"log\": \"-- also not comment\"}}'" +
                    "); -- Real comment here";
        
        PreprocessingResult result = Preprocessor.preprocess(sql);
        String cleaned = result.cleanedSql();
        
        // 验证复杂嵌套JSON中的注释符号被保留
        assertTrue(cleaned.contains("\"/* not a comment */\""), 
            "嵌套JSON中的多行注释符号应该被保留");
        assertTrue(cleaned.contains("\"-- also not comment\""), 
            "嵌套JSON中的单行注释符号应该被保留");
        
        // 验证真正的注释被移除
        assertFalse(cleaned.contains("-- Real comment here"), 
            "真正的注释应该被移除");
    }
}
