package com.xylink.sqltranspiler.regression.bugfix;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseConversionTest;

/**
 * 各数据库自增语法正确性测试
 * 基于官方文档的正确语法验证
 */
public class AutoIncrementCorrectSyntaxTest extends BaseConversionTest {

    @Test
    @DisplayName("验证各数据库的正确自增语法")
    void testCorrectAutoIncrementSyntax() throws Exception {
        
        System.out.println("=== 各数据库自增语法正确性验证 ===\n");
        
        // 测试基本的AUTO_INCREMENT语法
        String sql = "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50));";
        
        System.out.println("原始MySQL SQL:");
        System.out.println(sql);
        System.out.println();
        
        // 基于达梦官方文档验证转换 - 应该使用IDENTITY(1,1)
        // https://eco.dameng.com/document/dm/zh-cn/sql-dev/
        String damengSql = convertMySqlToDameng(sql);
        System.out.println("达梦转换结果:");
        System.out.println(damengSql);

        // 基于达梦官方文档验证AUTO_INCREMENT转换
        validateDamengOfficialAutoIncrementConversion(damengSql);
        System.out.println("✅ 达梦语法正确：使用IDENTITY(1,1)（符合官方文档）");
        System.out.println();
        
        // 金仓转换 - 应该使用SERIAL
        String kingbaseSql = convertMySqlToKingbase(sql);
        System.out.println("金仓转换结果:");
        System.out.println(kingbaseSql);
        assertTrue(kingbaseSql.contains("SERIAL"), "金仓数据库应该使用SERIAL语法");
        assertFalse(kingbaseSql.contains("AUTO_INCREMENT"), "金仓数据库不支持AUTO_INCREMENT");
        assertFalse(kingbaseSql.contains("IDENTITY"), "金仓数据库不使用IDENTITY");
        System.out.println("✅ 金仓语法正确：使用SERIAL");
        System.out.println();
        
        // 神通转换 - 应该保持AUTO_INCREMENT
        String shentongSql = convertMySqlToShentong(sql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "神通数据库完全支持AUTO_INCREMENT语法");
        assertFalse(shentongSql.contains("IDENTITY"), "神通数据库不需要转换为IDENTITY");
        assertFalse(shentongSql.contains("SERIAL"), "神通数据库不使用SERIAL");
        System.out.println("✅ 神通语法正确：保持AUTO_INCREMENT");
        System.out.println();
    }
    
    @Test
    @DisplayName("验证BIGINT自增语法")
    void testBigintAutoIncrement() throws Exception {
        
        System.out.println("=== BIGINT自增语法验证 ===\n");
        
        String sql = "CREATE TABLE logs (id BIGINT AUTO_INCREMENT PRIMARY KEY, message TEXT);";
        
        System.out.println("原始MySQL SQL:");
        System.out.println(sql);
        System.out.println();
        
        // 达梦转换
        String damengSql = convertMySqlToDameng(sql);
        System.out.println("达梦转换结果:");
        System.out.println(damengSql);
        assertTrue(damengSql.contains("BIGINT IDENTITY(1,1)"), "达梦数据库BIGINT应该使用IDENTITY(1,1)");
        System.out.println("✅ 达梦BIGINT自增语法正确");
        System.out.println();
        
        // 金仓转换
        String kingbaseSql = convertMySqlToKingbase(sql);
        System.out.println("金仓转换结果:");
        System.out.println(kingbaseSql);
        assertTrue(kingbaseSql.contains("BIGSERIAL"), "金仓数据库BIGINT应该使用BIGSERIAL");
        System.out.println("✅ 金仓BIGINT自增语法正确");
        System.out.println();
        
        // 神通转换
        String shentongSql = convertMySqlToShentong(sql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        assertTrue(shentongSql.contains("BIGINT AUTO_INCREMENT"), "神通数据库BIGINT应该保持AUTO_INCREMENT");
        System.out.println("✅ 神通BIGINT自增语法正确");
        System.out.println();
    }
    
    @Test
    @DisplayName("验证函数转换的正确性")
    void testFunctionConversion() throws Exception {
        
        System.out.println("=== 函数转换正确性验证 ===\n");
        
        String sql = "SELECT IFNULL(name, 'Unknown') FROM users;";
        
        System.out.println("原始MySQL SQL:");
        System.out.println(sql);
        System.out.println();
        
        // 达梦转换 - 应该使用NVL
        String damengSql = convertMySqlToDameng(sql);
        System.out.println("达梦转换结果:");
        System.out.println(damengSql);
        assertTrue(damengSql.contains("NVL"), "达梦数据库应该使用NVL函数");
        assertFalse(damengSql.contains("IFNULL"), "达梦数据库不支持IFNULL");
        System.out.println("✅ 达梦函数转换正确：IFNULL -> NVL");
        System.out.println();
        
        // 金仓转换 - 应该使用COALESCE
        String kingbaseSql = convertMySqlToKingbase(sql);
        System.out.println("金仓转换结果:");
        System.out.println(kingbaseSql);
        assertTrue(kingbaseSql.contains("COALESCE"), "金仓数据库应该使用COALESCE函数");
        assertFalse(kingbaseSql.contains("IFNULL"), "金仓数据库不支持IFNULL");
        System.out.println("✅ 金仓函数转换正确：IFNULL -> COALESCE");
        System.out.println();
        
        // 神通转换 - 应该保持IFNULL
        String shentongSql = convertMySqlToShentong(sql);
        System.out.println("神通转换结果:");
        System.out.println(shentongSql);
        assertTrue(shentongSql.contains("IFNULL"), "神通数据库完全支持IFNULL函数");
        assertFalse(shentongSql.contains("NVL"), "神通数据库不需要转换为NVL");
        assertFalse(shentongSql.contains("COALESCE"), "神通数据库不需要转换为COALESCE");
        System.out.println("✅ 神通函数转换正确：保持IFNULL");
        System.out.println();
    }
    
    @Test
    @DisplayName("验证ALTER TABLE AUTO_INCREMENT语法")
    void testAlterTableAutoIncrement() throws Exception {
        
        System.out.println("=== ALTER TABLE AUTO_INCREMENT语法验证 ===\n");
        
        String sql = "ALTER TABLE users AUTO_INCREMENT = 1000;";
        
        System.out.println("原始MySQL SQL:");
        System.out.println(sql);
        System.out.println();
        
        try {
            // 达梦转换
            String damengSql = convertMySqlToDameng(sql);
            System.out.println("达梦转换结果:");
            System.out.println(damengSql);
            System.out.println("✅ 达梦ALTER TABLE转换成功");
            System.out.println();
        } catch (Exception e) {
            System.out.println("❌ 达梦ALTER TABLE转换失败: " + e.getMessage());
        }
        
        try {
            // 金仓转换
            String kingbaseSql = convertMySqlToKingbase(sql);
            System.out.println("金仓转换结果:");
            System.out.println(kingbaseSql);
            System.out.println("✅ 金仓ALTER TABLE转换成功");
            System.out.println();
        } catch (Exception e) {
            System.out.println("❌ 金仓ALTER TABLE转换失败: " + e.getMessage());
        }
        
        try {
            // 神通转换
            String shentongSql = convertMySqlToShentong(sql);
            System.out.println("神通转换结果:");
            System.out.println(shentongSql);
            assertTrue(shentongSql.contains("AUTO_INCREMENT = 1000"), "神通数据库应该保持AUTO_INCREMENT语法");
            System.out.println("✅ 神通ALTER TABLE转换成功");
            System.out.println();
        } catch (Exception e) {
            System.out.println("❌ 神通ALTER TABLE转换失败: " + e.getMessage());
        }
    }

    /**
     * 基于达梦官方文档验证AUTO_INCREMENT转换
     *
     * 达梦官方文档规范：
     * - AUTO_INCREMENT应该转换为IDENTITY(1,1)
     * - 不支持AUTO_INCREMENT语法
     * - 不使用SERIAL类型
     */
    private void validateDamengOfficialAutoIncrementConversion(String damengSql) {
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertFalse(damengSql.trim().isEmpty(), "达梦转换结果不应为空字符串");

        // 基于达梦官方文档验证IDENTITY转换
        assertTrue(damengSql.contains("IDENTITY(1,1)"),
                  "达梦数据库应该使用IDENTITY(1,1)语法（符合官方文档）");

        // 验证不包含MySQL的AUTO_INCREMENT语法
        assertFalse(damengSql.contains("AUTO_INCREMENT"),
                   "达梦数据库不支持AUTO_INCREMENT（符合官方文档）");

        // 验证不使用PostgreSQL的SERIAL语法
        assertFalse(damengSql.contains("SERIAL"),
                   "达梦数据库不使用SERIAL（符合官方文档）");

        // 验证基本CREATE TABLE结构
        assertTrue(damengSql.toUpperCase().contains("CREATE TABLE"),
                  "应该包含CREATE TABLE语句");

        System.out.println("    ✅ 达梦AUTO_INCREMENT转换验证通过（符合官方文档）");
    }
}
