package com.xylink.sqltranspiler.regression.bugfix;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 测试修复 StackOverflowError 问题
 */
public class StackOverflowFixTest {
    
    private static final Logger log = LoggerFactory.getLogger(StackOverflowFixTest.class);
    
    @Test
    @DisplayName("测试简单SQL语句不会导致StackOverflowError")
    public void testSimpleSqlNoStackOverflow() {
        String sql = "CREATE DATABASE test_db; USE test_db; CREATE TABLE test_table (id INT PRIMARY KEY, name VARCHAR(100));";
        
        assertDoesNotThrow(() -> {
            List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
            assertNotNull(statements);
            assertTrue(statements.size() > 0);
            log.info("Successfully parsed {} statements", statements.size());
        }, "Simple SQL should not cause StackOverflowError");
    }
    
    @Test
    @DisplayName("测试复杂SQL语句不会导致StackOverflowError")
    public void testComplexSqlNoStackOverflow() {
        String sql = """
            CREATE TABLE libra_user_profile (
              id bigint(20) NOT NULL AUTO_INCREMENT,
              user_id varchar(32) NOT NULL,
              display_name varchar(128) DEFAULT NULL,
              email varchar(128) DEFAULT NULL,
              phone varchar(32) DEFAULT NULL,
              PRIMARY KEY (id),
              UNIQUE KEY libra_user_profile_user_id_key (user_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            
            INSERT INTO libra_user_profile VALUES (1, 'user1', 'Test User', '<EMAIL>', '1234567890');
            
            SELECT * FROM libra_user_profile WHERE user_id = 'user1';
            """;
        
        assertDoesNotThrow(() -> {
            List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
            assertNotNull(statements);
            assertTrue(statements.size() > 0);
            log.info("Successfully parsed {} statements", statements.size());
        }, "Complex SQL should not cause StackOverflowError");
    }
    
    @Test
    @DisplayName("测试包含DDL和DML混合的SQL语句")
    public void testMixedDdlDmlSql() {
        String sql = """
            DROP TABLE IF EXISTS test_table;
            CREATE TABLE test_table (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            INSERT INTO test_table (name) VALUES ('test1'), ('test2');
            UPDATE test_table SET name = 'updated' WHERE id = 1;
            DELETE FROM test_table WHERE id = 2;
            """;

        assertDoesNotThrow(() -> {
            List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
            assertNotNull(statements);
            assertTrue(statements.size() >= 5);
            log.info("Successfully parsed {} mixed DDL/DML statements", statements.size());
        }, "Mixed DDL/DML SQL should not cause StackOverflowError");
    }

    @Test
    @DisplayName("测试大文件不会导致StackOverflowError - 回归测试")
    public void testLargeFileNoStackOverflow() {
        // 创建一个相对较大的SQL文件内容来模拟原始问题
        StringBuilder largeSQL = new StringBuilder();

        // 添加一些复杂的SQL语句
        for (int i = 0; i < 100; i++) {
            largeSQL.append("CREATE TABLE test_table_").append(i).append(" (\n");
            largeSQL.append("    id BIGINT AUTO_INCREMENT PRIMARY KEY,\n");
            largeSQL.append("    name VARCHAR(255) NOT NULL,\n");
            largeSQL.append("    description TEXT,\n");
            largeSQL.append("    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n");
            largeSQL.append("    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n");
            largeSQL.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;\n\n");

            largeSQL.append("INSERT INTO test_table_").append(i).append(" (name, description) VALUES ");
            for (int j = 0; j < 10; j++) {
                if (j > 0) largeSQL.append(", ");
                largeSQL.append("('name_").append(j).append("', 'description_").append(j).append("')");
            }
            largeSQL.append(";\n\n");
        }

        String sql = largeSQL.toString();
        log.info("Testing large SQL with {} characters", sql.length());

        assertDoesNotThrow(() -> {
            List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
            assertNotNull(statements);
            assertTrue(statements.size() > 0);
            log.info("Successfully parsed {} statements from large SQL", statements.size());
        }, "Large SQL file should not cause StackOverflowError");
    }
}
