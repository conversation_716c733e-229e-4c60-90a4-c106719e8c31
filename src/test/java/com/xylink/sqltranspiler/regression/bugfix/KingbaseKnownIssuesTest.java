package com.xylink.sqltranspiler.regression.bugfix;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.shared.base.BaseKingbaseConversionTest;

/**
 * 金仓数据库已知问题回归测试 - 严格遵循官方文档规范
 *
 * 测试原则：
 * 1. 不允许推测，必须基于官方文档
 * 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 * 3. 坚持正确的实现，确保功能的准确性和完整性
 *
 * 回归测试目标：
 * - 验证已知问题的修复情况，确保不会出现回归
 * - 每个测试用例对应一个曾经出现过的问题，通过测试确保问题已修复
 * - 基于金仓官方文档验证修复的正确性
 *
 * 官方文档依据：
 * - 金仓数据库: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
 * - MySQL兼容性: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 数据类型映射: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-3.html#id13
 */
@DisplayName("金仓数据库已知问题回归测试")
public class KingbaseKnownIssuesTest extends BaseKingbaseConversionTest {

    private static final Logger log = LoggerFactory.getLogger(KingbaseKnownIssuesTest.class);

    @Test
    @DisplayName("TINYINT(1)转换为BOOLEAN的回归测试 - 基于金仓官方文档")
    public void testTinyintToBooleanRegressionWithOfficialDocumentValidation() throws Exception {
        // 基于金仓官方文档的MySQL兼容性测试
        // https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-3.html#id13
        String sql = """
            CREATE TABLE test_boolean (
                id INT PRIMARY KEY,
                is_active TINYINT(1),
                flag TINYINT(1) DEFAULT 1
            );
            """;

        String result = convertMySqlToKingbase(sql);
        log.info("转换结果: {}", result);

        // 基于金仓官方文档验证TINYINT(1)转换
        validateKingbaseOfficialTinyintConversion(result);

        // 验证默认值保持 - 基于金仓官方文档
        assertTrue(result.contains("DEFAULT"), "默认值应保持（符合金仓官方文档）");

        System.out.println("    ✅ 金仓TINYINT(1)转换回归测试通过");
    }

    @Test
    @DisplayName("标识符引用转换回归测试")
    public void testIdentifierQuotingRegression() throws Exception {
        String sql = """
            SELECT `t1`.`id`, `t1`.`name` 
            FROM `table1` `t1` 
            JOIN `table2` `t2` ON `t1`.`id` = `t2`.`id`;
            """;
        
        String result = convertMySqlToKingbase(sql);
        log.info("转换结果: {}", result);
        
        // 验证标识符引用转换
        assertFalse(result.contains("`"), "不应包含反引号");
        assertTrue(result.contains("\"") || !result.contains("`"), 
                  "应使用双引号或不使用引号");
        
        // 验证表别名保持
        assertTrue(result.contains("t1") && result.contains("t2"), 
                  "表别名应保持");
    }

    @Test
    @DisplayName("分页语法回归测试")
    public void testPaginationSyntaxRegression() throws Exception {
        String sql = """
            SELECT * FROM employees LIMIT 10 OFFSET 20;
            """;
        
        String result = convertMySqlToKingbase(sql);
        log.info("转换结果: {}", result);
        
        // 验证分页语法
        assertTrue(result.contains("LIMIT") && result.contains("OFFSET"),
                  "LIMIT OFFSET语法应保持");
    }

    /**
     * 基于金仓官方文档验证TINYINT(1)转换
     *
     * 金仓官方文档规范：
     * - 良好的MySQL兼容性
     * - TINYINT(1)可以转换为BOOLEAN、BIT、SMALLINT等类型
     * - 支持MySQL的数据类型映射
     */
    private void validateKingbaseOfficialTinyintConversion(String result) {
        assertNotNull(result, "转换结果不应为空");
        assertFalse(result.trim().isEmpty(), "转换结果不应为空字符串");

        String upperResult = result.toUpperCase();

        // 基于金仓官方文档验证TINYINT(1)转换
        // https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-3.html#id13
        boolean hasValidConversion = upperResult.contains("BOOLEAN") ||
                                   upperResult.contains("BOOL") ||
                                   upperResult.contains("BIT") ||
                                   upperResult.contains("SMALLINT") ||
                                   upperResult.contains("TINYINT"); // 金仓可能保持TINYINT

        assertTrue(hasValidConversion,
                  "TINYINT(1)应转换为BOOLEAN或其他等效类型（符合金仓官方文档）");

        if (upperResult.contains("BOOLEAN")) {
            System.out.println("    ✅ 金仓将TINYINT(1)转换为BOOLEAN");
        } else if (upperResult.contains("BIT")) {
            System.out.println("    ✅ 金仓将TINYINT(1)转换为BIT");
        } else if (upperResult.contains("SMALLINT")) {
            System.out.println("    ✅ 金仓将TINYINT(1)转换为SMALLINT");
        } else if (upperResult.contains("TINYINT")) {
            System.out.println("    ✅ 金仓保持了TINYINT类型（良好兼容性）");
        }

        // 验证基本CREATE TABLE结构
        assertTrue(upperResult.contains("CREATE TABLE"),
                  "应该包含CREATE TABLE语句");

        System.out.println("    ✅ 金仓TINYINT(1)转换验证通过");
    }
}
