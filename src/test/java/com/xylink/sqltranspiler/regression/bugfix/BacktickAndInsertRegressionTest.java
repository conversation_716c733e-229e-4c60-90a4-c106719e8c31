package com.xylink.sqltranspiler.regression.bugfix;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 回归测试套件 - 防止已修复问题的重现
 * 
 * 这个测试类专门用于验证我们修复的关键问题不会再次出现：
 * 1. ANTLR词法规则冲突导致反引号标识符解析失败
 * 2. INSERT语句VALUES部分解析不完整
 * 3. MySQL关键字作为字段名时的处理问题
 */
@DisplayName("反引号和INSERT回归测试")
public class BacktickAndInsertRegressionTest {

    private Transpiler transpiler;
    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("回归测试: 反引号标识符不应被识别为字符串字面量")
    void regressionTest_BacktickNotStringLiteral() {
        // 这个测试确保修复的ANTLR词法规则问题不会重现
        // 之前的问题: `test` 被识别为 STRING_LITERAL 而不是 REVERSE_QUOTE_ID
        
        String sql = "CREATE TABLE `test` (id INT);";
        
        // 这个调用之前会失败，因为解析器无法处理反引号
        assertDoesNotThrow(() -> {
            List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
            assertEquals(1, statements.size());

            String result = generator.generate(statements.get(0));
            assertTrue(result.contains("CREATE TABLE") &&
                       (result.toUpperCase().contains("TEST") || result.contains("test")));
        }, "反引号标识符解析应该成功，不应抛出解析异常");
    }

    @Test
    @DisplayName("回归测试: INSERT语句VALUES部分应该完整解析")
    void regressionTest_InsertValuesComplete() {
        // 这个测试确保INSERT语句VALUES部分解析问题不会重现
        // 之前的问题: VALUES后面的内容被截断，只生成 "INSERT INTO table VALUES "
        
        String sql = "INSERT INTO users (name, email) VALUES ('John', '<EMAIL>'), ('Jane', '<EMAIL>');";
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());
        
        String result = generator.generate(statements.get(0));
        
        // 验证VALUES部分完整存在
        assertTrue(result.contains("VALUES"));
        assertTrue(result.contains("('John', '<EMAIL>')"));
        assertTrue(result.contains("('Jane', '<EMAIL>')"));
        
        // 确保不是截断的结果
        assertFalse(result.trim().endsWith("VALUES"));
        assertFalse(result.contains("VALUES -- VALUES data"));
    }

    @Test
    @DisplayName("回归测试: MySQL关键字作为反引号字段名应该正确处理")
    void regressionTest_KeywordsAsBacktickFields() {
        // 这个测试确保MySQL关键字作为字段名的处理问题不会重现
        // 之前的问题: `name`, `status`, `type` 等关键字导致解析失败
        
        String sql = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                `name` VARCHAR(100) NOT NULL,
                `status` VARCHAR(50),
                `type` VARCHAR(50),
                `class` VARCHAR(50),
                `group` VARCHAR(50),
                `user` VARCHAR(50)
            );
            """;
        
        assertDoesNotThrow(() -> {
            List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
            assertEquals(1, statements.size());
            
            String result = generator.generate(statements.get(0));
            
            // 验证所有MySQL关键字都被正确转换为标识符
            assertTrue(result.toUpperCase().contains("NAME") || result.contains("name"));
            assertTrue(result.toUpperCase().contains("STATUS") || result.contains("status"));
            assertTrue(result.toUpperCase().contains("TYPE") || result.contains("type"));
            assertTrue(result.toUpperCase().contains("CLASS") || result.contains("class"));
            assertTrue(result.toUpperCase().contains("GROUP") || result.contains("group"));
            assertTrue(result.toUpperCase().contains("USER") || result.contains("user"));
        }, "MySQL关键字作为反引号字段名应该能正确解析");
    }

    @Test
    @DisplayName("回归测试: 复杂INSERT语句与反引号组合场景")
    void regressionTest_ComplexInsertWithBackticks() {
        // 这个测试确保复杂的INSERT+反引号组合场景不会重现问题
        // 之前的问题: 同时包含反引号和VALUES的语句解析失败
        
        String sql = """
            INSERT INTO `user_profiles` (`user_id`, `name`, `status`, `type`, `class`) VALUES
            (1, 'Admin User', 'active', 'admin', 'premium'),
            (2, 'Regular User', 'inactive', 'user', 'standard'),
            (3, 'Test User', 'pending', 'test', 'basic');
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());
        
        String result = generator.generate(statements.get(0));
        
        // 验证表名和字段名的转换
        assertTrue(result.contains("INSERT INTO") &&
                   (result.toUpperCase().contains("USER_PROFILES") || result.contains("user_profiles")));
        assertTrue((result.toUpperCase().contains("USER_ID") || result.contains("user_id")) &&
                   (result.toUpperCase().contains("NAME") || result.contains("name")) &&
                   (result.toUpperCase().contains("STATUS") || result.contains("status")) &&
                   (result.toUpperCase().contains("TYPE") || result.contains("type")) &&
                   (result.toUpperCase().contains("CLASS") || result.contains("class")));
        
        // 验证所有VALUES行都存在
        assertTrue(result.contains("(1, 'Admin User', 'active', 'admin', 'premium')"));
        assertTrue(result.contains("(2, 'Regular User', 'inactive', 'user', 'standard')"));
        assertTrue(result.contains("(3, 'Test User', 'pending', 'test', 'basic')"));
    }

    @Test
    @DisplayName("回归测试: 原始测试用例应该完全正确")
    void regressionTest_OriginalTestCase() {
        // 这个测试使用最初失败的测试用例，确保完全修复
        String sql = """
            -- Test MySQL to Dameng conversion
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(100) NOT NULL,
                email VARCHAR(255) UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            
            INSERT INTO users (username, email) VALUES
            ('John Doe', '<EMAIL>'),
            ('Jane Smith', '<EMAIL>');
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(2, statements.size());
        
        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            result.append(generator.generate(statement)).append("\n");
        }
        
        String damengSql = result.toString();
        
        // 验证CREATE TABLE完整转换
        assertTrue(damengSql.contains("CREATE TABLE") &&
                   (damengSql.toUpperCase().contains("USERS") || damengSql.contains("users")));
        assertTrue(damengSql.contains("IDENTITY(1,1)"));
        assertFalse(damengSql.contains("CHARACTER SET")); // 达梦不支持表级字符集
        assertFalse(damengSql.contains("ENGINE=InnoDB"));
        
        // 验证INSERT完整转换 - 这是最初失败的部分
        assertTrue(damengSql.contains("INSERT INTO") &&
                   (damengSql.toUpperCase().contains("USERS") || damengSql.contains("users")) &&
                   (damengSql.toUpperCase().contains("USERNAME") || damengSql.contains("username")) &&
                   (damengSql.toUpperCase().contains("EMAIL") || damengSql.contains("email")));
        assertTrue(damengSql.contains("VALUES") && damengSql.contains("'John Doe'") && damengSql.contains("'<EMAIL>'"));
        assertTrue(damengSql.contains("'Jane Smith'") && damengSql.contains("'<EMAIL>'"));
        
        // 确保没有截断问题
        assertFalse(damengSql.contains("VALUES \n"));
        assertFalse(damengSql.contains("VALUES -- "));
    }

    @Test
    @DisplayName("回归测试: 边界情况处理")
    void regressionTest_EdgeCases() {
        // 测试各种边界情况，确保修复是健壮的
        
        // 1. 只有反引号表名，无字段
        String sql1 = "CREATE TABLE `test` (id INT);";
        assertDoesNotThrow(() -> MySqlHelper.parseMultiStatement(sql1));
        
        // 2. 只有反引号字段名，无表名反引号
        String sql2 = "CREATE TABLE test (`name` VARCHAR(100));";
        assertDoesNotThrow(() -> MySqlHelper.parseMultiStatement(sql2));
        
        // 3. 单行INSERT
        String sql3 = "INSERT INTO `test` VALUES (1);";
        List<Statement> statements3 = MySqlHelper.parseMultiStatement(sql3);
        String result3 = generator.generate(statements3.get(0));
        assertTrue(result3.contains("VALUES (1)"));
        
        // 4. 无列名INSERT
        String sql4 = "INSERT INTO test VALUES (1, 'test');";
        List<Statement> statements4 = MySqlHelper.parseMultiStatement(sql4);
        String result4 = generator.generate(statements4.get(0));
        assertTrue(result4.contains("VALUES (1, 'test')"));
        
        // 5. 空VALUES（虽然语法上不正确，但不应该崩溃）
        String sql5 = "INSERT INTO test () VALUES ();";
        assertDoesNotThrow(() -> MySqlHelper.parseMultiStatement(sql5));
    }

    @Test
    @DisplayName("回归测试: 性能不应该显著下降")
    void regressionTest_PerformanceRegression() {
        // 确保修复没有引入显著的性能问题
        String largeSql = generateLargeSql();
        
        long startTime = System.currentTimeMillis();
        List<Statement> statements = MySqlHelper.parseMultiStatement(largeSql);
        long parseTime = System.currentTimeMillis() - startTime;
        
        startTime = System.currentTimeMillis();
        for (Statement statement : statements) {
            generator.generate(statement);
        }
        long generateTime = System.currentTimeMillis() - startTime;
        
        // 性能断言 - 这些值应该根据实际基准调整
        assertTrue(parseTime < 3000, "解析时间不应超过3秒，实际: " + parseTime + "ms");
        assertTrue(generateTime < 1000, "生成时间不应超过1秒，实际: " + generateTime + "ms");
        
        // 验证结果正确性
        assertTrue(statements.size() > 0);
    }

    private String generateLargeSql() {
        StringBuilder sql = new StringBuilder();
        
        // 生成多个包含反引号的CREATE TABLE语句
        for (int i = 1; i <= 10; i++) {
            sql.append("CREATE TABLE `table_").append(i).append("` (");
            sql.append("`id` INT AUTO_INCREMENT PRIMARY KEY, ");
            sql.append("`name` VARCHAR(100), ");
            sql.append("`status` VARCHAR(50), ");
            sql.append("`type` VARCHAR(50)");
            sql.append(");\n");
        }
        
        // 生成多个包含反引号的INSERT语句
        for (int i = 1; i <= 10; i++) {
            sql.append("INSERT INTO `table_").append(i).append("` (`name`, `status`, `type`) VALUES ");
            for (int j = 1; j <= 20; j++) {
                if (j > 1) sql.append(", ");
                sql.append("('Name ").append(j).append("', 'active', 'test')");
            }
            sql.append(";\n");
        }
        
        return sql.toString();
    }
}
