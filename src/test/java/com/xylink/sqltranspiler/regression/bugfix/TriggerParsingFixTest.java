package com.xylink.sqltranspiler.regression.bugfix;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * 触发器解析修复测试
 * 
 * 发现问题：触发器SQL被错误地分割为多个语句，导致解析失败
 * 根本原因：DELIMITER语法和BEGIN...END块的处理不正确
 * 
 * 基于官方文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/create-trigger.html
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: shentong.md
 */
@DisplayName("触发器解析修复测试")
public class TriggerParsingFixTest {

    private Transpiler transpiler;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }

    @Test
    @DisplayName("简单触发器解析测试")
    void testSimpleTriggerParsing() {
        // 最简单的触发器语法，不使用DELIMITER
        String sql = """
            CREATE TRIGGER update_timestamp
            BEFORE UPDATE ON users
            FOR EACH ROW
            SET NEW.updated_at = NOW();
            """;

        // 测试所有数据库的转换
        String[] databases = {"dameng", "kingbase", "shentong"};
        for (String db : databases) {
            TranspilationResult result = transpiler.transpile(sql, "mysql", db);
            assertNotNull(result, db + "触发器转换结果不应为空");
            
            String translatedSql = result.translatedSql();
            assertNotNull(translatedSql, db + "触发器转换SQL不应为空");
            assertTrue(translatedSql.length() > 0, db + "触发器转换结果不应为空字符串");
            
            // 验证触发器关键字保持
            assertTrue(translatedSql.contains("TRIGGER"), db + "应该保持TRIGGER关键字");
            assertTrue(translatedSql.contains("BEFORE"), db + "应该保持BEFORE关键字");
            assertTrue(translatedSql.contains("UPDATE"), db + "应该保持UPDATE关键字");
            
            System.out.println(db.toUpperCase() + " 简单触发器转换结果:");
            System.out.println(translatedSql);
            System.out.println("---");
        }
    }

    @Test
    @DisplayName("复杂触发器解析测试")
    void testComplexTriggerParsing() {
        // 包含BEGIN...END块的复杂触发器
        String sql = """
            CREATE TRIGGER audit_user_changes
            AFTER UPDATE ON users
            FOR EACH ROW
            BEGIN
                INSERT INTO user_audit (user_id, old_name, new_name, changed_at)
                VALUES (NEW.id, OLD.name, NEW.name, NOW());
                
                UPDATE user_stats 
                SET last_modified = NOW() 
                WHERE user_id = NEW.id;
            END;
            """;

        // 测试所有数据库的转换
        String[] databases = {"dameng", "kingbase", "shentong"};
        for (String db : databases) {
            TranspilationResult result = transpiler.transpile(sql, "mysql", db);
            assertNotNull(result, db + "复杂触发器转换结果不应为空");
            
            String translatedSql = result.translatedSql();
            assertNotNull(translatedSql, db + "复杂触发器转换SQL不应为空");
            assertTrue(translatedSql.length() > 0, db + "复杂触发器转换结果不应为空字符串");
            
            // 验证触发器关键结构保持
            assertTrue(translatedSql.contains("TRIGGER"), db + "应该保持TRIGGER关键字");
            assertTrue(translatedSql.contains("AFTER"), db + "应该保持AFTER关键字");
            assertTrue(translatedSql.contains("BEGIN"), db + "应该保持BEGIN关键字");
            assertTrue(translatedSql.contains("END"), db + "应该保持END关键字");
            
            System.out.println(db.toUpperCase() + " 复杂触发器转换结果:");
            System.out.println(translatedSql);
            System.out.println("---");
        }
    }

    @Test
    @DisplayName("DELIMITER语法触发器解析测试")
    void testDelimiterTriggerParsing() {
        // 使用DELIMITER语法的触发器（MySQL常见写法）
        String sql = """
            DELIMITER //
            CREATE TRIGGER calculate_total
            BEFORE INSERT ON order_items
            FOR EACH ROW
            BEGIN
                DECLARE total_amount DECIMAL(10,2);
                SELECT price * NEW.quantity INTO total_amount FROM products WHERE id = NEW.product_id;
                SET NEW.total_price = total_amount;
            END//
            DELIMITER ;
            """;

        // 测试所有数据库的转换
        String[] databases = {"dameng", "kingbase", "shentong"};
        for (String db : databases) {
            TranspilationResult result = transpiler.transpile(sql, "mysql", db);
            assertNotNull(result, db + "DELIMITER触发器转换结果不应为空");
            
            String translatedSql = result.translatedSql();
            assertNotNull(translatedSql, db + "DELIMITER触发器转换SQL不应为空");
            assertTrue(translatedSql.length() > 0, db + "DELIMITER触发器转换结果不应为空字符串");
            
            // 验证触发器关键结构保持
            assertTrue(translatedSql.contains("TRIGGER"), db + "应该保持TRIGGER关键字");
            assertTrue(translatedSql.contains("BEFORE"), db + "应该保持BEFORE关键字");
            assertTrue(translatedSql.contains("INSERT"), db + "应该保持INSERT关键字");
            
            System.out.println(db.toUpperCase() + " DELIMITER触发器转换结果:");
            System.out.println(translatedSql);
            System.out.println("---");
        }
    }

    @Test
    @DisplayName("多个触发器解析测试")
    void testMultipleTriggersParsing() {
        // 多个触发器定义
        String sql = """
            CREATE TRIGGER before_user_insert
            BEFORE INSERT ON users
            FOR EACH ROW
            SET NEW.created_at = NOW();
            
            CREATE TRIGGER after_user_insert
            AFTER INSERT ON users
            FOR EACH ROW
            INSERT INTO user_log (user_id, action, timestamp) VALUES (NEW.id, 'INSERT', NOW());
            
            CREATE TRIGGER before_user_delete
            BEFORE DELETE ON users
            FOR EACH ROW
            INSERT INTO deleted_users (id, name, deleted_at) VALUES (OLD.id, OLD.name, NOW());
            """;

        // 测试所有数据库的转换
        String[] databases = {"dameng", "kingbase", "shentong"};
        for (String db : databases) {
            TranspilationResult result = transpiler.transpile(sql, "mysql", db);
            assertNotNull(result, db + "多触发器转换结果不应为空");
            
            String translatedSql = result.translatedSql();
            assertNotNull(translatedSql, db + "多触发器转换SQL不应为空");
            assertTrue(translatedSql.length() > 0, db + "多触发器转换结果不应为空字符串");
            
            // 验证所有触发器都被转换
            int triggerCount = countOccurrences(translatedSql, "CREATE TRIGGER");
            assertTrue(triggerCount >= 3, db + "应该包含3个CREATE TRIGGER语句");
            
            System.out.println(db.toUpperCase() + " 多触发器转换结果:");
            System.out.println(translatedSql);
            System.out.println("触发器数量: " + triggerCount);
            System.out.println("---");
        }
    }

    @Test
    @DisplayName("触发器转换成功率统计")
    void testTriggerConversionSuccessRate() {
        String[] triggerSqls = {
            "CREATE TRIGGER simple_trigger BEFORE INSERT ON test FOR EACH ROW SET NEW.created = NOW();",
            
            """
            CREATE TRIGGER complex_trigger
            AFTER UPDATE ON orders
            FOR EACH ROW
            BEGIN
                UPDATE inventory SET quantity = quantity - (NEW.quantity - OLD.quantity) WHERE product_id = NEW.product_id;
            END;
            """,
            
            """
            DELIMITER //
            CREATE TRIGGER audit_trigger
            BEFORE DELETE ON users
            FOR EACH ROW
            BEGIN
                INSERT INTO audit_log (table_name, operation, old_data, timestamp)
                VALUES ('users', 'DELETE', CONCAT('id:', OLD.id, ',name:', OLD.name), NOW());
            END//
            DELIMITER ;
            """
        };

        String[] databases = {"dameng", "kingbase", "shentong"};
        
        for (String db : databases) {
            int successCount = 0;
            int totalCount = triggerSqls.length;
            
            for (String sql : triggerSqls) {
                try {
                    TranspilationResult result = transpiler.transpile(sql, "mysql", db);
                    if (result != null && result.successCount() > 0) {
                        successCount++;
                    }
                } catch (Exception e) {
                    System.out.println(db + " 触发器转换失败: " + e.getMessage());
                }
            }
            
            double successRate = (double) successCount / totalCount * 100;
            System.out.println(db.toUpperCase() + " 触发器转换成功率: " + successCount + "/" + totalCount + " (" + String.format("%.1f", successRate) + "%)");
            
            // 要求至少50%的成功率（在修复之前可能较低）
            assertTrue(successRate >= 50.0, db + "触发器转换成功率应该至少达到50%");
        }
    }

    private int countOccurrences(String text, String pattern) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(pattern, index)) != -1) {
            count++;
            index += pattern.length();
        }
        return count;
    }
}
