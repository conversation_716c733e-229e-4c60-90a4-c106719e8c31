package com.xylink.sqltranspiler.compliance.mysql;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * 统一的官方文档合规性测试
 * 合并所有重复的OfficialDocumentationComplianceTest类
 * 
 * 基于四个数据库官方文档的SQL转换功能合规性测试：
 * - MySQL 8.4: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: 基于神通数据库SQL参考手册
 * 
 * 质量保证原则：
 * 1. 严格遵循官方文档 - 不允许推测，必须查看官方文档
 * 2. 测试驱动开发 - 当测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 * 3. 不妥协代码质量 - 坚持正确的实现，确保功能的准确性和完整性
 */
@DisplayName("官方文档合规性测试")
public class OfficialDocumentationComplianceTest {
    
    private static final Logger log = LoggerFactory.getLogger(OfficialDocumentationComplianceTest.class);
    
    private Transpiler transpiler;
    
    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }
    
    /**
     * 验证基本转换要求
     *
     * 根据官方文档要求：
     * - 神通数据库官方文档第144行明确规定："分号（;）结束一条SQL命令"
     * - MySQL官方文档：分号是标准SQL语句分隔符
     * - 达梦、金仓：作为标准SQL数据库，同样要求分号结束语句
     */
    private void assertBasicConversionRequirements(String convertedSql) {
        assertFalse(convertedSql == null || convertedSql.trim().isEmpty(),
                   "转换结果不应为空");
        assertTrue(convertedSql.trim().endsWith(";"),
                   "根据官方文档，SQL语句必须以分号结尾: " + convertedSql);
    }

    // ==================== MySQL官方文档合规性测试 ====================
    
    @Test
    @DisplayName("MySQL官方文档 - CREATE TABLE语法合规性")
    void testMySqlCreateTableCompliance() {
        // 根据MySQL官方文档 13.1.20 CREATE TABLE Statement
        // https://dev.mysql.com/doc/refman/8.4/en/create-table.html
        String sql = """
            CREATE TABLE test.users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) NOT NULL UNIQUE,
                email VARCHAR(255) NOT NULL DEFAULT '',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        // 测试达梦转换
        TranspilationResult damengResult = transpiler.transpile(sql, "mysql", "dameng");
        assertBasicConversionRequirements(damengResult.translatedSql());
        assertTrue(damengResult.translatedSql().contains("CREATE TABLE"), "应包含CREATE TABLE");
        assertTrue(damengResult.translatedSql().contains("IDENTITY(1,1)"), "AUTO_INCREMENT应转换为IDENTITY(1,1)");
        
        // 测试金仓转换
        TranspilationResult kingbaseResult = transpiler.transpile(sql, "mysql", "kingbase");
        assertBasicConversionRequirements(kingbaseResult.translatedSql());
        assertTrue(kingbaseResult.translatedSql().contains("CREATE TABLE"), "应包含CREATE TABLE");
        assertTrue(kingbaseResult.translatedSql().contains("SERIAL"), "AUTO_INCREMENT应转换为SERIAL");
        
        // 测试神通转换
        TranspilationResult shentongResult = transpiler.transpile(sql, "mysql", "shentong");
        assertBasicConversionRequirements(shentongResult.translatedSql());
        assertTrue(shentongResult.translatedSql().contains("CREATE TABLE"), "应包含CREATE TABLE");
        assertTrue(shentongResult.translatedSql().contains("AUTO_INCREMENT"), "神通应保持AUTO_INCREMENT语法");
        
        log.info("MySQL CREATE TABLE测试完成");
    }

    @Test
    @DisplayName("MySQL官方文档 - ALTER TABLE语法合规性")
    void testMySqlAlterTableCompliance() {
        // 根据MySQL官方文档 13.1.9 ALTER TABLE Statement
        // https://dev.mysql.com/doc/refman/8.4/en/alter-table.html
        String sql = """
            ALTER TABLE test.users 
            ADD COLUMN phone VARCHAR(20) AFTER email,
            MODIFY COLUMN username VARCHAR(100) NOT NULL,
            DROP COLUMN temp_col,
            ADD INDEX idx_email (email);
            """;
        
        // 测试达梦转换
        TranspilationResult damengResult = transpiler.transpile(sql, "mysql", "dameng");
        assertBasicConversionRequirements(damengResult.translatedSql());
        assertTrue(damengResult.translatedSql().contains("ALTER TABLE"), "应包含ALTER TABLE");
        assertTrue(damengResult.translatedSql().contains("ADD COLUMN"), "应包含ADD COLUMN");
        
        // 测试金仓转换
        TranspilationResult kingbaseResult = transpiler.transpile(sql, "mysql", "kingbase");
        assertBasicConversionRequirements(kingbaseResult.translatedSql());
        assertTrue(kingbaseResult.translatedSql().contains("ALTER TABLE"), "应包含ALTER TABLE");
        
        // 测试神通转换
        TranspilationResult shentongResult = transpiler.transpile(sql, "mysql", "shentong");
        assertBasicConversionRequirements(shentongResult.translatedSql());
        assertTrue(shentongResult.translatedSql().contains("ALTER TABLE"), "应包含ALTER TABLE");
        
        log.info("MySQL ALTER TABLE测试完成");
    }

    // ==================== 达梦官方文档合规性测试 ====================
    
    @Test
    @DisplayName("达梦官方文档 - 数据类型转换合规性")
    void testDamengDataTypeCompliance() {
        // 根据达梦官方文档数据类型规范
        // https://eco.dameng.com/document/dm/zh-cn/sql-dev/
        String sql = """
            CREATE TABLE type_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                flag TINYINT(1),
                amount DECIMAL(10,2),
                description TEXT,
                created_at TIMESTAMP
            );
            """;
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "dameng");
        assertBasicConversionRequirements(result.translatedSql());
        
        // 验证达梦特定转换
        assertTrue(result.translatedSql().contains("IDENTITY(1,1)"), "AUTO_INCREMENT应转换为IDENTITY(1,1)");
        assertTrue(result.translatedSql().contains("BIT") || result.translatedSql().contains("BOOLEAN"), 
                   "TINYINT(1)应转换为BIT或BOOLEAN");
        assertTrue(result.translatedSql().contains("DECIMAL(10,2)"), "DECIMAL应保持不变");
        
        log.info("达梦数据类型测试完成: {}", result.translatedSql());
    }

    @Test
    @DisplayName("达梦官方文档 - 函数转换合规性")
    void testDamengFunctionCompliance() {
        // 根据达梦官方文档函数规范
        String sql = """
            SELECT 
                IFNULL(email, '未知') as email_display,
                DATE_FORMAT(created_at, '%Y-%m-%d') as date_display,
                CONCAT(first_name, ' ', last_name) as full_name
            FROM users;
            """;
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "dameng");
        assertBasicConversionRequirements(result.translatedSql());
        
        // 验证达梦函数转换
        assertTrue(result.translatedSql().contains("NVL") || result.translatedSql().contains("IFNULL"), 
                   "IFNULL应转换为NVL或保持IFNULL");
        assertTrue(result.translatedSql().contains("TO_CHAR") || result.translatedSql().contains("DATE_FORMAT"), 
                   "DATE_FORMAT应转换为TO_CHAR或保持DATE_FORMAT");
        
        log.info("达梦函数测试完成: {}", result.translatedSql());
    }

    // ==================== 金仓官方文档合规性测试 ====================
    
    @Test
    @DisplayName("金仓官方文档 - MySQL兼容性合规性")
    void testKingbasePostgreSQLCompliance() {
        // 根据金仓官方文档MySQL兼容性规范
        // https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
        // 使用MySQL标准语法测试金仓数据库的MySQL兼容性
        String sql = """
            CREATE TABLE mysql_compat_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """;

        TranspilationResult result = transpiler.transpile(sql, "mysql", "kingbase");
        assertBasicConversionRequirements(result.translatedSql());

        // 验证金仓MySQL兼容性
        // 根据金仓官方文档：支持MySQL的AUTO_INCREMENT、JSON等类型

        // 先输出实际转换结果用于调试
        System.out.println("实际转换结果: " + result.translatedSql());

        assertTrue(result.translatedSql().contains("AUTO_INCREMENT") ||
                   result.translatedSql().contains("SERIAL") ||
                   result.translatedSql().contains("INT"),
                   "根据金仓官方文档，应支持AUTO_INCREMENT或转换为等价形式。实际结果: " + result.translatedSql());
        assertTrue(result.translatedSql().contains("VARCHAR(100)"), "应支持VARCHAR类型");
        assertTrue(result.translatedSql().contains("PRIMARY KEY"), "应支持PRIMARY KEY");
        assertTrue(result.translatedSql().contains("JSON") || result.translatedSql().contains("TEXT"),
                   "根据金仓官方文档，应支持JSON类型或转换为TEXT");

        log.info("金仓MySQL兼容性测试完成: {}", result.translatedSql());
    }

    @Test
    @DisplayName("金仓官方文档 - MySQL迁移最佳实践合规性")
    void testKingbaseMySQLMigrationCompliance() {
        // 根据金仓官方文档MySQL迁移最佳实践
        String sql = """
            SELECT * FROM users 
            WHERE created_at >= '2023-01-01'
            ORDER BY id DESC
            LIMIT 10 OFFSET 20;
            """;
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "kingbase");
        assertBasicConversionRequirements(result.translatedSql());
        
        // 验证金仓MySQL迁移处理
        assertTrue(result.translatedSql().contains("SELECT"), "应支持SELECT语句");
        assertTrue(result.translatedSql().contains("ORDER BY"), "应支持ORDER BY");
        assertTrue(result.translatedSql().contains("LIMIT") || result.translatedSql().contains("OFFSET"), 
                   "应支持分页语法");
        
        log.info("金仓MySQL迁移测试完成: {}", result.translatedSql());
    }

    // ==================== 神通官方文档合规性测试 ====================
    
    @Test
    @DisplayName("神通官方文档 - AUTO_INCREMENT支持合规性")
    void testShentongAutoIncrementCompliance() {
        // 根据神通官方文档：神通数据库完全支持AUTO_INCREMENT语法
        String sql = """
            CREATE TABLE shentong_auto_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100)
            ) AUTO_INCREMENT = 1000;
            """;
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "shentong");
        assertBasicConversionRequirements(result.translatedSql());
        
        // 验证神通AUTO_INCREMENT支持
        assertTrue(result.translatedSql().contains("AUTO_INCREMENT"), "神通应保持AUTO_INCREMENT语法");
        assertTrue(result.translatedSql().contains("PRIMARY KEY"), "应支持PRIMARY KEY");
        assertTrue(result.translatedSql().contains("1000"), "应支持AUTO_INCREMENT初始值");
        
        log.info("神通AUTO_INCREMENT测试完成: {}", result.translatedSql());
    }

    @Test
    @DisplayName("神通官方文档 - 伪列支持合规性")
    void testShentongPseudoColumnCompliance() {
        // 根据神通官方文档第2.6节伪列规范
        String sql = """
            SELECT ROWID, ROWNUM, name, email
            FROM users
            WHERE ROWNUM <= 10
            ORDER BY name;
            """;
        
        TranspilationResult result = transpiler.transpile(sql, "mysql", "shentong");
        assertBasicConversionRequirements(result.translatedSql());
        
        // 验证神通伪列支持
        assertTrue(result.translatedSql().contains("ROWID"), "应支持ROWID伪列");
        assertTrue(result.translatedSql().contains("ROWNUM"), "应支持ROWNUM伪列");
        assertTrue(result.translatedSql().contains("SELECT"), "应支持SELECT语句");
        
        log.info("神通伪列测试完成: {}", result.translatedSql());
    }

    // ==================== 跨数据库一致性测试 ====================
    
    @Test
    @DisplayName("跨数据库一致性 - 基本SQL语句")
    void testCrossDatabaseConsistency() {
        String sql = """
            SELECT id, name, email
            FROM users
            WHERE status = 'active'
            ORDER BY created_at DESC;
            """;
        
        // 测试所有数据库的基本一致性
        TranspilationResult damengResult = transpiler.transpile(sql, "mysql", "dameng");
        TranspilationResult kingbaseResult = transpiler.transpile(sql, "mysql", "kingbase");
        TranspilationResult shentongResult = transpiler.transpile(sql, "mysql", "shentong");
        
        // 验证基本转换要求
        assertBasicConversionRequirements(damengResult.translatedSql());
        assertBasicConversionRequirements(kingbaseResult.translatedSql());
        assertBasicConversionRequirements(shentongResult.translatedSql());
        
        // 验证基本SQL元素的一致性
        assertTrue(damengResult.translatedSql().contains("SELECT"), "达梦应支持SELECT");
        assertTrue(kingbaseResult.translatedSql().contains("SELECT"), "金仓应支持SELECT");
        assertTrue(shentongResult.translatedSql().contains("SELECT"), "神通应支持SELECT");
        
        assertTrue(damengResult.translatedSql().contains("WHERE"), "达梦应支持WHERE");
        assertTrue(kingbaseResult.translatedSql().contains("WHERE"), "金仓应支持WHERE");
        assertTrue(shentongResult.translatedSql().contains("WHERE"), "神通应支持WHERE");
        
        log.info("跨数据库一致性测试完成");
    }
}
