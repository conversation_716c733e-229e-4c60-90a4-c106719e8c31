package com.xylink.sqltranspiler.compliance.mysql;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer.OrderAnnotation;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * MySQL函数官方文档合规性测试
 * 基于MySQL 8.4官方文档: https://dev.mysql.com/doc/refman/8.4/en/functions.html
 * 
 * 严格遵循官方文档规范：
 * - 字符串函数: https://dev.mysql.com/doc/refman/8.4/en/string-functions.html
 * - 数值函数: https://dev.mysql.com/doc/refman/8.4/en/numeric-functions.html
 * - 日期时间函数: https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
 * - 条件函数: https://dev.mysql.com/doc/refman/8.4/en/flow-control-functions.html
 * 
 * 测试原则：
 * 1. 不允许推测，必须基于官方文档
 * 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 * 3. 坚持正确的实现，确保功能的准确性和完整性
 */
@TestMethodOrder(OrderAnnotation.class)
@DisplayName("MySQL函数官方文档合规性测试")
class MySqlFunctionOfficialDocumentationTest {

    private Transpiler transpiler;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }

    /**
     * 测试MySQL条件函数 - 基于官方文档14.4节
     * https://dev.mysql.com/doc/refman/8.4/en/flow-control-functions.html
     */
    @ParameterizedTest
    @CsvSource(delimiter = '|', value = {
        // IFNULL函数 - 官方文档14.4.3节
        "SELECT IFNULL(name, 'Unknown') FROM users;|IFNULL|如果第一个参数不为NULL则返回第一个参数否则返回第二个参数",
        "SELECT IFNULL(NULL, 'default');|IFNULL|处理NULL值的标准函数",
        // NULLIF函数 - 官方文档14.4.4节
        "SELECT NULLIF(name, '') FROM users;|NULLIF|如果两个参数相等则返回NULL否则返回第一个参数",
        // COALESCE函数 - 官方文档14.4.1节
        "SELECT COALESCE(name, email, 'Unknown') FROM users;|COALESCE|返回第一个非NULL参数",
        // IF函数 - 官方文档14.4.2节
        "SELECT IF(age >= 18, 'Adult', 'Minor') FROM users;|IF|条件表达式函数"
    })
    @Order(1)
    @DisplayName("MySQL条件函数官方文档合规性测试")
    void testMySqlConditionalFunctionsOfficialDocumentation(String sql, String functionName, String description) {
        System.out.println("测试MySQL条件函数: " + functionName + " (" + description + ")");
        System.out.println("  SQL: " + sql);
        
        // 测试转换到达梦
        TranspilationResult damengResult = transpiler.transpile(sql, "mysql", "dameng");
        assertNotNull(damengResult, "MySQL到达梦的转换结果不应为空");
        
        if (damengResult.successCount() > 0) {
            String damengSql = damengResult.translatedSql();
            System.out.println("  达梦转换结果: " + damengSql.trim());
            
            // 基于达梦官方文档验证函数转换
            validateDamengFunctionConversion(functionName, damengSql);
        }
        
        // 测试转换到金仓
        TranspilationResult kingbaseResult = transpiler.transpile(sql, "mysql", "kingbase");
        assertNotNull(kingbaseResult, "MySQL到金仓的转换结果不应为空");
        
        if (kingbaseResult.successCount() > 0) {
            String kingbaseSql = kingbaseResult.translatedSql();
            System.out.println("  金仓转换结果: " + kingbaseSql.trim());
            
            // 基于金仓官方文档验证函数转换
            validateKingbaseFunctionConversion(functionName, kingbaseSql);
        }
        
        // 测试转换到神通
        TranspilationResult shentongResult = transpiler.transpile(sql, "mysql", "shentong");
        assertNotNull(shentongResult, "MySQL到神通的转换结果不应为空");
        
        if (shentongResult.successCount() > 0) {
            String shentongSql = shentongResult.translatedSql();
            System.out.println("  神通转换结果: " + shentongSql.trim());
            
            // 基于神通官方文档验证函数转换
            validateShentongFunctionConversion(functionName, shentongSql);
        }
        
        System.out.println(String.format("  转换统计 - 达梦: 成功=%d,失败=%d | 金仓: 成功=%d,失败=%d | 神通: 成功=%d,失败=%d", 
                           damengResult.successCount(), damengResult.failureCount(),
                           kingbaseResult.successCount(), kingbaseResult.failureCount(),
                           shentongResult.successCount(), shentongResult.failureCount()));
        System.out.println();
    }

    /**
     * 测试MySQL日期时间函数 - 基于官方文档14.7节
     * https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
     */
    @ParameterizedTest
    @CsvSource(delimiter = '|', value = {
        // NOW函数 - 官方文档14.7.4节
        "SELECT NOW();|NOW|返回当前日期和时间",
        "SELECT NOW(6);|NOW|返回带微秒精度的当前日期和时间",
        // CURRENT_TIMESTAMP函数 - 官方文档14.7.4节
        "SELECT CURRENT_TIMESTAMP;|CURRENT_TIMESTAMP|返回当前时间戳",
        // DATE_FORMAT函数 - 官方文档14.7.8节
        "SELECT DATE_FORMAT(created_at, '%Y-%m-%d') FROM orders;|DATE_FORMAT|格式化日期",
        // UNIX_TIMESTAMP函数 - 官方文档14.7.4节
        "SELECT UNIX_TIMESTAMP();|UNIX_TIMESTAMP|返回Unix时间戳"
    })
    @Order(2)
    @DisplayName("MySQL日期时间函数官方文档合规性测试")
    void testMySqlDateTimeFunctionsOfficialDocumentation(String sql, String functionName, String description) {
        System.out.println("测试MySQL日期时间函数: " + functionName + " (" + description + ")");
        System.out.println("  SQL: " + sql);
        
        // 测试转换到各个目标数据库
        String[] targetDbs = {"dameng", "kingbase", "shentong"};
        
        for (String targetDb : targetDbs) {
            TranspilationResult result = transpiler.transpile(sql, "mysql", targetDb);
            assertNotNull(result, String.format("MySQL函数%s到%s的转换结果不应为空", functionName, targetDb));
            
            if (result.successCount() > 0) {
                String translatedSql = result.translatedSql();
                System.out.println(String.format("  %s转换结果: %s", targetDb, translatedSql.trim()));
                
                // 验证日期时间函数转换的正确性
                validateDateTimeFunctionConversion(functionName, translatedSql, targetDb);
            } else {
                System.out.println(String.format("  %s转换失败", targetDb));
                if (result.issues() != null) {
                    result.issues().forEach(issue -> 
                        System.out.println("    问题: " + issue.message()));
                }
            }
        }
        
        System.out.println();
    }

    /**
     * 基于达梦官方文档验证函数转换
     */
    private void validateDamengFunctionConversion(String functionName, String damengSql) {
        String upperSql = damengSql.toUpperCase();
        
        switch (functionName.toUpperCase()) {
            case "IFNULL":
                // 达梦使用NVL函数替代IFNULL
                if (upperSql.contains("NVL")) {
                    System.out.println("    ✅ 达梦正确将IFNULL转换为NVL");
                } else if (upperSql.contains("IFNULL")) {
                    System.out.println("    ⚠️ 达梦保持了IFNULL函数，需要验证兼容性");
                }
                break;
            case "NOW":
                // 达梦使用SYSDATE或CURRENT_TIMESTAMP
                if (upperSql.contains("SYSDATE") || upperSql.contains("CURRENT_TIMESTAMP")) {
                    System.out.println("    ✅ 达梦正确转换NOW函数");
                }
                break;
            case "DATE_FORMAT":
                // 达梦使用TO_CHAR函数
                if (upperSql.contains("TO_CHAR")) {
                    System.out.println("    ✅ 达梦正确将DATE_FORMAT转换为TO_CHAR");
                }
                break;
            default:
                System.out.println("    ℹ️ 达梦函数转换: " + functionName + " -> 需要验证");
        }
    }

    /**
     * 基于金仓官方文档验证函数转换
     */
    private void validateKingbaseFunctionConversion(String functionName, String kingbaseSql) {
        String upperSql = kingbaseSql.toUpperCase();
        
        // 根据金仓官方文档，金仓对MySQL函数有很好的兼容性
        switch (functionName.toUpperCase()) {
            case "IFNULL":
                // 金仓可能使用COALESCE或保持IFNULL
                if (upperSql.contains("COALESCE") || upperSql.contains("IFNULL")) {
                    System.out.println("    ✅ 金仓正确处理IFNULL函数");
                }
                break;
            case "NOW":
            case "CURRENT_TIMESTAMP":
                // 金仓原生支持这些函数
                System.out.println("    ✅ 金仓原生支持" + functionName + "函数");
                break;
            default:
                System.out.println("    ℹ️ 金仓函数转换: " + functionName + " -> 需要验证");
        }
    }

    /**
     * 基于神通官方文档验证函数转换
     */
    private void validateShentongFunctionConversion(String functionName, String shentongSql) {
        String upperSql = shentongSql.toUpperCase();
        
        switch (functionName.toUpperCase()) {
            case "IFNULL":
                // 根据神通官方文档，神通原生支持IFNULL函数
                if (upperSql.contains("IFNULL")) {
                    System.out.println("    ✅ 神通原生支持IFNULL函数");
                }
                break;
            case "NOW":
                // 神通可能转换为SYSDATE
                if (upperSql.contains("SYSDATE") || upperSql.contains("NOW")) {
                    System.out.println("    ✅ 神通正确处理NOW函数");
                }
                break;
            default:
                System.out.println("    ℹ️ 神通函数转换: " + functionName + " -> 需要验证");
        }
    }

    /**
     * 验证日期时间函数转换的正确性
     */
    private void validateDateTimeFunctionConversion(String functionName, String translatedSql, String targetDb) {
        String upperSql = translatedSql.toUpperCase();
        
        // 验证转换后的SQL仍然是有效的
        assertTrue(upperSql.contains("SELECT"), "转换后的SQL应该包含SELECT语句");
        
        // 根据不同的目标数据库验证特定的转换规则
        switch (targetDb.toLowerCase()) {
            case "dameng":
                if (functionName.equals("NOW") && upperSql.contains("SYSDATE")) {
                    System.out.println("    ✅ 达梦正确将NOW转换为SYSDATE");
                }
                break;
            case "shentong":
                if (functionName.equals("NOW") && upperSql.contains("SYSDATE")) {
                    System.out.println("    ✅ 神通正确将NOW转换为SYSDATE");
                }
                break;
        }
    }
}
