package com.xylink.sqltranspiler.compliance.shentong;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库官方文档合规性测试
 * 基于神通数据库官方文档 shentong.md 的完整规范验证
 *
 * 严格遵循官方文档规范：
 * - 数据类型: shentong.md 第2.9节 数据类型
 * - 伪列支持: shentong.md 第2.6节 ROWNUM伪列分页查询
 * - 自增列: shentong.md 第2.9.4节 AUTO_INCREMENT自增列类型
 * - 序列类型: shentong.md 第2.9.3节 数值类型（SERIAL、BIGSERIAL）
 * - 标识符: shentong.md 第2.2节 关键字和标识符（双引号分隔标识符）
 * - 函数支持: shentong.md 第23548行 SYSDATE - 取系统时间
 *
 * 测试原则：
 * 1. 不允许推测，必须基于官方文档
 * 2. 测试期望与官方文档不符时，修正测试用例而不是降低代码质量
 * 3. 坚持正确的实现，确保功能的准确性和完整性
 *
 * 测试覆盖：
 * 1. 伪列支持（ROWID、SYSATTR_ROWVERSION、ROWNUM）- 基于第2.6节
 * 2. 自增列类型（AUTO_INCREMENT）- 基于第2.9.4节
 * 3. 数据类型支持（数值、字符串、二进制、日期时间）- 基于第2.9节
 * 4. 序列类型（SERIAL、BIGSERIAL）- 基于第2.9.3节
 * 5. 特殊函数（IFNULL、SYSDATE等）- 基于官方函数列表
 * 6. 分页查询（LIMIT转ROWNUM）- 基于第2.6节
 */
public class ShentongOfficialComplianceTest extends BaseShentongConversionTest {

    private static final org.slf4j.Logger logger = org.slf4j.LoggerFactory.getLogger(ShentongOfficialComplianceTest.class);

    /**
     * 测试神通数据库伪列完整支持
     * 基于官方文档第2.6节伪列规范
     */
    @Test
    @DisplayName("验证神通数据库伪列完整支持")
    public void testShentongPseudoColumnsSupport() throws Exception {
        String mysqlSql = """
            SELECT
                id,
                name
            FROM users
            ORDER BY id
            LIMIT 10;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证LIMIT转换为ROWNUM分页
        assertTrue(shentongSql.contains("ROWNUM"), "LIMIT应转换为ROWNUM分页");
        assertTrue(shentongSql.contains("ROW_NUM"), "应包含ROW_NUM别名");
        assertTrue(shentongSql.contains("BETWEEN") || shentongSql.contains("<="), "应包含分页条件");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY");
    }

    /**
     * 测试神通数据库自增列完整特性
     * 基于官方文档第2.9.4节自增列类型
     */
    @Test
    @DisplayName("验证神通数据库自增列完整特性")
    public void testShentongAutoIncrementFeatures() throws Exception {
        String mysqlSql = """
            CREATE TABLE auto_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                big_id BIGINT AUTO_INCREMENT UNIQUE,
                name VARCHAR(10)
            ) AUTO_INCREMENT = 5;
            
            INSERT INTO auto_test (name) VALUES ('test1');
            INSERT INTO auto_test VALUES (10, 20, 'test2');
            
            ALTER TABLE auto_test AUTO_INCREMENT = 100;
            
            UPDATE auto_test SET id = 15 WHERE name = 'test1';
            
            SELECT LAST_INSERT_ID();
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证自增列支持
        assertTrue(shentongSql.contains("AUTO_INCREMENT") || 
                   shentongSql.contains("SERIAL") || 
                   shentongSql.contains("IDENTITY"),
                   "应支持自增列语法");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY约束");
        assertTrue(shentongSql.contains("UNIQUE"), "应支持UNIQUE约束");
        assertTrue(shentongSql.contains("LAST_INSERT_ID"), "应支持LAST_INSERT_ID函数");
        assertTrue(shentongSql.contains("ALTER TABLE"), "应支持ALTER TABLE AUTO_INCREMENT");
    }

    /**
     * 测试神通数据库数据类型完整支持
     * 基于官方文档第2.9节数据类型
     */
    @Test
    @DisplayName("验证神通数据库数据类型完整支持")
    public void testShentongDataTypesSupport() throws Exception {
        String mysqlSql = """
            CREATE TABLE data_types_test (
                -- 数值类型
                tiny_col TINYINT,
                small_col SMALLINT,
                int_col INT,
                big_col BIGINT,
                decimal_col DECIMAL(10,2),
                numeric_col NUMERIC(15,3),
                real_col REAL,
                double_col DOUBLE PRECISION,
                float_col FLOAT(24),
                
                -- 字符串类型
                char_col CHAR(10),
                varchar_col VARCHAR(100),
                text_col TEXT,
                
                -- 二进制类型
                binary_col BINARY(16),
                varbinary_col VARBINARY(255),
                
                -- 位串类型
                bit_col BIT(8),

                -- 自增序列类型（MySQL语法）
                serial_col INT AUTO_INCREMENT,
                bigserial_col BIGINT AUTO_INCREMENT
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证数值类型支持
        assertTrue(shentongSql.contains("TINYINT") || shentongSql.contains("SMALLINT"), 
                   "应支持TINYINT类型");
        assertTrue(shentongSql.contains("SMALLINT"), "应支持SMALLINT类型");
        assertTrue(shentongSql.contains("INT"), "应支持INT类型");
        assertTrue(shentongSql.contains("BIGINT"), "应支持BIGINT类型");
        assertTrue(shentongSql.contains("DECIMAL") || shentongSql.contains("NUMERIC"), 
                   "应支持DECIMAL/NUMERIC类型");
        assertTrue(shentongSql.contains("REAL"), "应支持REAL类型");
        assertTrue(shentongSql.contains("DOUBLE PRECISION"), "应支持DOUBLE PRECISION类型");
        
        // 验证字符串类型支持 - 根据神通官方文档第996行示例，保持MySQL兼容性
        assertTrue(shentongSql.contains("CHAR"), "CHAR应该保持为CHAR");
        assertTrue(shentongSql.contains("VARCHAR"),
                  "VARCHAR应该保持为VARCHAR（神通官方文档第996行示例支持）");
        assertTrue(shentongSql.contains("TEXT"), "应支持TEXT类型");
        
        // 验证二进制类型支持
        assertTrue(shentongSql.contains("BINARY"), "应支持BINARY类型");
        assertTrue(shentongSql.contains("VARBINARY"), "应支持VARBINARY类型");
        
        // 验证自增序列类型支持（MySQL AUTO_INCREMENT转换为神通SERIAL）
        assertTrue(shentongSql.contains("SERIAL") ||
                   shentongSql.contains("AUTO_INCREMENT") ||
                   shentongSql.contains("IDENTITY"),
                   "应支持SERIAL/AUTO_INCREMENT/IDENTITY类型");
        assertTrue(shentongSql.contains("BIGSERIAL") ||
                   shentongSql.contains("BIGINT") && (shentongSql.contains("AUTO_INCREMENT") || shentongSql.contains("IDENTITY")),
                   "应支持BIGSERIAL或BIGINT AUTO_INCREMENT类型");
        
        // 验证字符集设置
        assertTrue(shentongSql.contains("CHARACTER SET UTF8"), "应使用CHARACTER SET UTF8");
    }

    /**
     * 测试神通数据库分页查询支持
     * 基于官方文档ROWNUM伪列实现分页显示
     */
    @Test
    @DisplayName("验证神通数据库分页查询支持")
    public void testShentongPaginationSupport() throws Exception {
        String mysqlSql = """
            SELECT id, name, email 
            FROM users 
            ORDER BY name DESC 
            LIMIT 10 OFFSET 20;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证分页转换
        assertTrue(shentongSql.contains("ROWNUM") || shentongSql.contains("ROW_NUMBER"),
                   "LIMIT应转换为ROWNUM分页");
        assertTrue(shentongSql.contains("ORDER BY"), "应保持ORDER BY");
        assertTrue(shentongSql.contains("BETWEEN") || shentongSql.contains("<=") || shentongSql.contains("LIMIT"),
                   "应包含分页条件");
    }

    /**
     * 测试神通数据库特殊函数支持
     * 基于官方文档的函数支持情况
     */
    @Test
    @DisplayName("验证神通数据库特殊函数支持")
    public void testShentongSpecialFunctionsSupport() throws Exception {
        String mysqlSql = """
            SELECT 
                CURRENT_DATE,
                CURRENT_TIME,
                CURRENT_TIMESTAMP,
                LAST_INSERT_ID(),
                NEXTVAL('user_seq'),
                CURRVAL('user_seq'),
                IFNULL(name, 'default') as safe_name,
                CHAR_LENGTH(name) as name_length
            FROM users;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证函数支持
        assertTrue(shentongSql.contains("CURRENT_DATE"), "应支持CURRENT_DATE函数");
        assertTrue(shentongSql.contains("CURRENT_TIME"), "应支持CURRENT_TIME函数");
        assertTrue(shentongSql.contains("CURRENT_TIMESTAMP"), "应支持CURRENT_TIMESTAMP函数");
        assertTrue(shentongSql.contains("LAST_INSERT_ID"), "应支持LAST_INSERT_ID函数");
        assertTrue(shentongSql.contains("NEXTVAL"), "应支持NEXTVAL函数");
        assertTrue(shentongSql.contains("CURRVAL"), "应支持CURRVAL函数");
        assertTrue(shentongSql.contains("IFNULL"), "应支持IFNULL函数");
        assertTrue(shentongSql.contains("CHAR_LENGTH"), "应支持CHAR_LENGTH函数");
    }

    /**
     * 测试神通数据库约束和索引支持
     * 基于官方文档的约束和索引特性
     */
    @Test
    @DisplayName("验证神通数据库约束和索引支持")
    public void testShentongConstraintsAndIndexesSupport() throws Exception {
        String mysqlSql = """
            CREATE TABLE constraint_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                email VARCHAR(100) UNIQUE,
                age INT CHECK (age >= 0),
                category_id INT,
                FOREIGN KEY (category_id) REFERENCES categories(id)
            );
            
            CREATE INDEX idx_email ON constraint_test(email);
            CREATE UNIQUE INDEX udx_age ON constraint_test(age);
            
            ALTER TABLE constraint_test ADD CONSTRAINT uk_email UNIQUE (email);
            ALTER TABLE constraint_test DROP INDEX idx_email;
            
            SHOW INDEXES FROM constraint_test;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证约束支持
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持PRIMARY KEY约束");
        assertTrue(shentongSql.contains("UNIQUE"), "应支持UNIQUE约束");
        assertTrue(shentongSql.contains("CHECK"), "应支持CHECK约束");
        assertTrue(shentongSql.contains("FOREIGN KEY"), "应支持FOREIGN KEY约束");
        
        // 验证索引支持
        assertTrue(shentongSql.contains("CREATE INDEX"), "应支持CREATE INDEX");
        assertTrue(shentongSql.contains("DROP INDEX"), "应支持DROP INDEX");
        assertTrue(shentongSql.contains("ADD CONSTRAINT"), "应支持ADD CONSTRAINT");
        
        // 验证SHOW INDEX转换
        assertTrue(shentongSql.contains("SELECT") || shentongSql.contains("SHOW"), 
                   "SHOW INDEX应转换为查询语句");
    }

    /**
     * 测试神通数据库完整的SQL语句支持
     * 综合测试各种SQL语句的转换
     */
    @Test
    @DisplayName("验证神通数据库完整SQL语句支持")
    public void testShentongCompleteSqlSupport() throws Exception {
        String mysqlSql = """
            CREATE TABLE complete_test (
                id SERIAL PRIMARY KEY,
                name VARCHAR(50) NOT NULL,
                email VARCHAR(100) UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            INSERT INTO complete_test (name, email) VALUES ('test', '<EMAIL>');
            
            UPDATE complete_test SET name = 'updated' WHERE id = 1;
            
            SELECT ROWNUM, id, name, email 
            FROM complete_test 
            WHERE ROWNUM <= 5 
            ORDER BY id;
            
            DELETE FROM complete_test WHERE id = 1;
            
            DROP TABLE complete_test;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证各种SQL语句支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE");
        assertTrue(shentongSql.contains("INSERT INTO"), "应支持INSERT INTO");
        assertTrue(shentongSql.contains("UPDATE"), "应支持UPDATE");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT");
        assertTrue(shentongSql.contains("DELETE FROM"), "应支持DELETE FROM");
        assertTrue(shentongSql.contains("DROP TABLE"), "应支持DROP TABLE");
        
        // 验证神通特有特性
        assertTrue(shentongSql.contains("AUTO_INCREMENT") || shentongSql.contains("SERIAL"), "应支持AUTO_INCREMENT或SERIAL类型");
        assertTrue(shentongSql.contains("ROWNUM"), "应支持ROWNUM伪列");
        assertTrue(shentongSql.contains("CURRENT_TIMESTAMP"), "应支持CURRENT_TIMESTAMP函数");
        assertTrue(shentongSql.contains("CHARACTER SET UTF8"), "应使用CHARACTER SET UTF8");
    }

    /**
     * 测试神通数据库IFNULL函数原生支持
     * 基于神通官方文档：神通数据库原生支持IFNULL函数
     */
    @Test
    @DisplayName("验证神通数据库IFNULL函数原生支持")
    public void testShentongIfnullNativeSupport() throws Exception {
        String mysqlSql = """
            SELECT
                id,
                IFNULL(name, 'Unknown') as display_name,
                IFNULL(email, 'No Email') as contact_email,
                IFNULL(age, 0) as user_age
            FROM users
            WHERE IFNULL(status, 'active') = 'active';
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 根据神通官方文档，神通数据库原生支持IFNULL函数
        // 因此转换结果应该保持IFNULL函数不变
        assertTrue(shentongSql.toUpperCase().contains("IFNULL"),
                  "神通数据库原生支持IFNULL函数，应该保持不变");

        // 验证IFNULL函数的参数结构完整性
        assertTrue(shentongSql.contains("IFNULL(name, 'Unknown')") ||
                  shentongSql.contains("IFNULL(\"name\", 'Unknown')"),
                  "IFNULL函数参数应该保持完整");

        // 验证WHERE子句中的IFNULL函数
        assertTrue(shentongSql.toUpperCase().contains("WHERE") &&
                  shentongSql.toUpperCase().contains("IFNULL"),
                  "WHERE子句中的IFNULL函数应该被正确处理");

        System.out.println("✅ 神通IFNULL函数原生支持验证通过");
        System.out.println("转换结果: " + shentongSql);
    }

    /**
     * 测试神通数据库SYSDATE函数支持
     * 基于神通官方文档：shentong.md 第23548行 SYSDATE - 取系统时间
     *
     * 神通数据库原生支持SYSDATE函数，MySQL的NOW()函数应该转换为SYSDATE
     */
    @Test
    @DisplayName("验证神通数据库SYSDATE函数原生支持")
    public void testShentongSysdateNativeSupport() throws Exception {
        String mysqlSql = """
            SELECT
                id,
                name,
                NOW() as current_time,
                CURRENT_TIMESTAMP as timestamp_value
            FROM users
            WHERE created_at <= NOW();
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 根据神通官方文档，神通数据库原生支持SYSDATE函数
        // MySQL的NOW()函数应该转换为SYSDATE
        String upperSql = shentongSql.toUpperCase();
        assertTrue(upperSql.contains("SYSDATE"),
                  "神通数据库应该支持SYSDATE函数");

        // 验证NOW()函数转换
        if (mysqlSql.toUpperCase().contains("NOW()") &&
            upperSql.contains("SYSDATE")) {
            System.out.println("    ✅ 神通正确将NOW()转换为SYSDATE");
        }

        // 验证CURRENT_TIMESTAMP处理
        if (upperSql.contains("CURRENT_TIMESTAMP") || upperSql.contains("SYSDATE")) {
            System.out.println("    ✅ 神通正确处理CURRENT_TIMESTAMP");
        }

        // 验证WHERE子句中的时间函数
        assertTrue(upperSql.contains("WHERE"),
                  "WHERE子句应该被正确处理");

        System.out.println("✅ 神通SYSDATE函数原生支持验证通过");
        System.out.println("转换结果: " + shentongSql);
    }

    /**
     * 测试神通数据库ROWNUM伪列分页查询
     * 基于神通官方文档：shentong.md 第2.6节 ROWNUM伪列分页查询
     *
     * 神通数据库使用ROWNUM伪列实现分页查询，MySQL的LIMIT OFFSET应该转换为ROWNUM
     */
    @Test
    @DisplayName("验证神通数据库ROWNUM伪列分页查询")
    public void testShentongRownumPagination() throws Exception {
        String mysqlSql = """
            SELECT id, name, email
            FROM users
            WHERE status = 'active'
            ORDER BY created_at DESC
            LIMIT 10 OFFSET 20;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 根据神通官方文档，神通数据库使用ROWNUM伪列实现分页
        String upperSql = shentongSql.toUpperCase();
        assertTrue(upperSql.contains("ROWNUM"),
                  "神通数据库应该使用ROWNUM伪列实现分页");

        // 验证LIMIT OFFSET转换为ROWNUM范围查询
        if (upperSql.contains("ROWNUM") && upperSql.contains("BETWEEN")) {
            System.out.println("    ✅ 神通正确将LIMIT OFFSET转换为ROWNUM BETWEEN查询");
        } else if (upperSql.contains("ROWNUM")) {
            System.out.println("    ✅ 神通使用ROWNUM实现分页查询");
        }

        // 验证不应包含MySQL的LIMIT OFFSET语法
        assertFalse(upperSql.contains("LIMIT") && upperSql.contains("OFFSET"),
                   "神通转换结果不应包含MySQL的LIMIT OFFSET语法");

        // 验证ORDER BY子句保持
        assertTrue(upperSql.contains("ORDER BY"),
                  "ORDER BY子句应该被保持");

        System.out.println("✅ 神通ROWNUM伪列分页查询验证通过");
        System.out.println("转换结果: " + shentongSql);
    }

    /**
     * 测试神通数据库SERIAL和BIGSERIAL类型支持
     * 基于神通官方文档：shentong.md 第834-835行 自增整数类型
     *
     * 官方文档明确说明：
     * - SERIAL: 自增整数 1 到+2147483647，4字节
     * - BIGSERIAL: 自增整数 1 到 9223372036854775807，8字节
     * - SERIAL等价于INTEGER DEFAULT NEXTVAL('sequence') NOT NULL
     * - BIGSERIAL等价于BIGINT DEFAULT NEXTVAL('sequence') NOT NULL
     */
    @Test
    @DisplayName("验证神通数据库SERIAL和BIGSERIAL类型原生支持")
    public void testShentongSerialTypesNativeSupport() throws Exception {
        String mysqlSql = """
            CREATE TABLE serial_test (
                small_id SMALLINT AUTO_INCREMENT,
                normal_id INT AUTO_INCREMENT,
                big_id BIGINT AUTO_INCREMENT,
                PRIMARY KEY (normal_id)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 根据神通官方文档，神通数据库原生支持SERIAL和BIGSERIAL类型
        String upperSql = shentongSql.toUpperCase();

        // 验证AUTO_INCREMENT转换为SERIAL类型
        boolean hasSerial = upperSql.contains("SERIAL");
        boolean hasBigserial = upperSql.contains("BIGSERIAL");
        boolean hasAutoIncrement = upperSql.contains("AUTO_INCREMENT");

        if (hasSerial || hasBigserial) {
            System.out.println("    ✅ 神通正确将AUTO_INCREMENT转换为SERIAL/BIGSERIAL类型");

            // 根据官方文档验证具体的类型映射
            if (upperSql.contains("BIGSERIAL")) {
                System.out.println("    ✅ BIGINT AUTO_INCREMENT正确转换为BIGSERIAL");
            }
            if (upperSql.contains("SERIAL") && !upperSql.contains("BIGSERIAL")) {
                System.out.println("    ✅ INT AUTO_INCREMENT正确转换为SERIAL");
            }
        } else if (hasAutoIncrement) {
            System.out.println("    ✅ 神通保持了AUTO_INCREMENT语法（也符合官方文档）");
        } else {
            System.out.println("    ⚠️ 神通AUTO_INCREMENT转换结果需要验证");
        }

        // 验证PRIMARY KEY约束保持
        assertTrue(upperSql.contains("PRIMARY KEY"),
                  "PRIMARY KEY约束应该被保持");

        // 验证字符集设置
        assertTrue(upperSql.contains("CHARACTER SET UTF8"),
                  "神通应该设置UTF8字符集");

        System.out.println("✅ 神通SERIAL和BIGSERIAL类型原生支持验证通过");
        System.out.println("转换结果: " + shentongSql);
    }

    /**
     * 测试神通数据库标识符引用规则
     * 基于神通官方文档：shentong.md 第2.2节 关键字和标识符（双引号分隔标识符）
     *
     * 神通数据库使用双引号来引用标识符，MySQL的反引号应该转换为双引号
     */
    @Test
    @DisplayName("验证神通数据库标识符引用规则")
    public void testShentongIdentifierQuotingRules() throws Exception {
        String mysqlSql = """
            CREATE TABLE `user_orders` (
                `order_id` INT AUTO_INCREMENT PRIMARY KEY,
                `user_name` VARCHAR(100) NOT NULL,
                `order_date` DATETIME DEFAULT CURRENT_TIMESTAMP,
                `total_amount` DECIMAL(10,2)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        String upperSql = shentongSql.toUpperCase();

        // 根据神通官方文档，神通使用双引号引用标识符
        assertTrue(shentongSql.contains("\""),
                  "神通应该使用双引号引用标识符");

        // 验证不应包含MySQL的反引号
        assertFalse(shentongSql.contains("`"),
                   "神通转换结果不应包含MySQL的反引号");

        // 验证具体的标识符转换
        if (shentongSql.contains("\"user_orders\"") || shentongSql.contains("user_orders")) {
            System.out.println("    ✅ 表名标识符正确转换");
        }

        if (shentongSql.contains("\"order_id\"") || shentongSql.contains("order_id")) {
            System.out.println("    ✅ 列名标识符正确转换");
        }

        // 验证AUTO_INCREMENT转换
        assertTrue(upperSql.contains("AUTO_INCREMENT") || upperSql.contains("SERIAL"),
                  "AUTO_INCREMENT应该被正确处理");

        // 验证字符集设置
        assertTrue(upperSql.contains("CHARACTER SET UTF8"),
                  "神通应该设置UTF8字符集");

        System.out.println("✅ 神通标识符引用规则验证通过");
        System.out.println("转换结果: " + shentongSql);
    }

    /**
     * 测试神通数据库数据类型完整支持
     * 基于官方文档第2.9节数据类型规范
     *
     * 官方文档依据：
     * - 数值类型：INTEGER、BIGINT、DECIMAL、NUMERIC、REAL、DOUBLE PRECISION
     * - 字符串类型：CHAR、VARCHAR、TEXT、CLOB
     * - 二进制类型：BYTEA、BLOB
     * - 日期时间类型：DATE、TIME、TIMESTAMP、TIMESTAMPTZ
     * - 布尔类型：BOOLEAN
     * - 序列类型：SERIAL、BIGSERIAL（第2.9.3节）
     * - 自增类型：AUTO_INCREMENT（第2.9.4节）
     */
    @Test
    @DisplayName("神通数据库数据类型合规性测试")
    public void testShentongDataTypeCompliance() throws Exception {
        logger.info("开始执行神通数据库数据类型合规性测试");

        // 测试数值类型转换 - 基于官方文档第2.9.1节
        String mysqlNumericSql = "CREATE TABLE test_numeric (" +
                "id INTEGER, " +
                "big_id BIGINT, " +
                "price DECIMAL(10,2), " +
                "score NUMERIC(5,2), " +
                "rate REAL, " +
                "amount DOUBLE PRECISION" +
                ");";

        String result = convertMySqlToShentong(mysqlNumericSql);

        // 验证数值类型保持不变（神通支持标准SQL数值类型）
        assertTrue(result.contains("INTEGER"), "神通数据库应支持INTEGER类型");
        assertTrue(result.contains("BIGINT"), "神通数据库应支持BIGINT类型");
        assertTrue(result.contains("DECIMAL(10,2)"), "神通数据库应支持DECIMAL类型");
        assertTrue(result.contains("NUMERIC(5,2)"), "神通数据库应支持NUMERIC类型");
        assertTrue(result.contains("REAL"), "神通数据库应支持REAL类型");
        assertTrue(result.contains("DOUBLE PRECISION"), "神通数据库应支持DOUBLE PRECISION类型");

        logger.info("神通数据库数值类型合规性测试通过");
    }

    /**
     * 测试神通数据库字符串类型支持
     * 基于官方文档第2.9.2节字符串类型规范
     */
    @Test
    @DisplayName("神通数据库字符串类型合规性测试")
    public void testShentongStringTypeCompliance() throws Exception {
        logger.info("开始执行神通数据库字符串类型合规性测试");

        String mysqlStringSql = "CREATE TABLE test_string (" +
                "name CHAR(50), " +
                "description VARCHAR(255), " +
                "content TEXT, " +
                "large_text CLOB" +
                ");";

        String result = convertMySqlToShentong(mysqlStringSql);

        // 验证字符串类型支持
        assertTrue(result.contains("CHAR(50)"), "神通数据库应支持CHAR类型");
        assertTrue(result.contains("VARCHAR(255)"), "神通数据库应支持VARCHAR类型");
        assertTrue(result.contains("TEXT"), "神通数据库应支持TEXT类型");
        assertTrue(result.contains("CLOB"), "神通数据库应支持CLOB类型");

        logger.info("神通数据库字符串类型合规性测试通过");
    }

    /**
     * 测试神通数据库序列类型支持
     * 基于官方文档第2.9.3节序列类型规范
     */
    @Test
    @DisplayName("神通数据库序列类型合规性测试")
    public void testShentongSerialTypeCompliance() throws Exception {
        logger.info("开始执行神通数据库序列类型合规性测试");

        // MySQL的AUTO_INCREMENT应转换为神通的SERIAL类型
        String mysqlAutoIncrementSql = "CREATE TABLE test_serial (" +
                "id INTEGER AUTO_INCREMENT PRIMARY KEY, " +
                "big_id BIGINT AUTO_INCREMENT" +
                ");";

        String result = convertMySqlToShentong(mysqlAutoIncrementSql);

        // 验证AUTO_INCREMENT转换为SERIAL类型
        assertTrue(result.contains("SERIAL") || result.contains("AUTO_INCREMENT"),
                "MySQL的AUTO_INCREMENT应转换为神通支持的序列类型");

        logger.info("神通数据库序列类型合规性测试通过");
    }

    /**
     * 测试神通数据库日期时间类型支持
     * 基于官方文档日期时间类型规范
     */
    @Test
    @DisplayName("神通数据库日期时间类型合规性测试")
    public void testShentongDateTimeTypeCompliance() throws Exception {
        logger.info("开始执行神通数据库日期时间类型合规性测试");

        String mysqlDateTimeSql = "CREATE TABLE test_datetime (" +
                "birth_date DATE, " +
                "work_time TIME, " +
                "created_at TIMESTAMP, " +
                "updated_at TIMESTAMPTZ" +
                ");";

        String result = convertMySqlToShentong(mysqlDateTimeSql);

        // 验证日期时间类型支持
        assertTrue(result.contains("DATE"), "神通数据库应支持DATE类型");
        assertTrue(result.contains("TIME"), "神通数据库应支持TIME类型");
        assertTrue(result.contains("TIMESTAMP"), "神通数据库应支持TIMESTAMP类型");

        logger.info("神通数据库日期时间类型合规性测试通过");
    }

    /**
     * 测试神通数据库函数转换合规性
     * 基于官方文档第23548行 SYSDATE函数规范
     */
    @Test
    @DisplayName("神通数据库函数转换合规性测试")
    public void testShentongFunctionCompliance() throws Exception {
        logger.info("开始执行神通数据库函数转换合规性测试");

        // 测试NOW()转换为SYSDATE函数支持（基于官方文档第23548行）
        String mysqlFunctionSql = "SELECT NOW() FROM dual;";

        String result = convertMySqlToShentong(mysqlFunctionSql);

        logger.info("神通函数转换结果: {}", result);

        // 验证NOW()转换为SYSDATE函数支持（基于官方文档第23548行）
        assertTrue(result.contains("SYSDATE"), "神通数据库应将NOW()转换为SYSDATE函数");

        // 测试直接的SYSDATE函数支持
        String directSysdateSql = "SELECT SYSDATE FROM dual;";
        String result2 = convertMySqlToShentong(directSysdateSql);
        assertTrue(result2.contains("SYSDATE"), "神通数据库应直接支持SYSDATE函数");

        logger.info("神通数据库函数转换合规性测试通过");
    }

    /**
     * 测试神通数据库分页查询合规性
     * 基于官方文档第2.6节 ROWNUM伪列分页查询规范
     */
    @Test
    @DisplayName("神通数据库分页查询合规性测试")
    public void testShentongPaginationCompliance() throws Exception {
        logger.info("开始执行神通数据库分页查询合规性测试");

        // MySQL的LIMIT应转换为神通的ROWNUM方式
        String mysqlLimitSql = "SELECT * FROM users LIMIT 10 OFFSET 20;";

        String result = convertMySqlToShentong(mysqlLimitSql);

        // 验证LIMIT转换为ROWNUM（基于官方文档第2.6节）
        assertTrue(result.contains("ROWNUM") || result.contains("LIMIT"),
                "MySQL的LIMIT应转换为神通支持的ROWNUM分页方式");

        logger.info("神通数据库分页查询合规性测试通过");
    }
}
