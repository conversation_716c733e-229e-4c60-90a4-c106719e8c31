package com.xylink.sqltranspiler.compliance.shentong;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;
import com.xylink.sqltranspiler.infrastructure.parser.PreprocessingResult;
import com.xylink.sqltranspiler.infrastructure.parser.Preprocessor;

/**
 * 基于神通数据库官方文档的深度问题分析和修复测试
 * 
 * 严格验证SQL转换功能是否完全符合神通数据库官方文档规范
 * 
 * 神通数据库官方文档依据：
 * - 2.9.1节：特有字符串类型（NAME、"CHAR"）
 * - 2.6节：ROWNUM伪列分页查询
 * - 2.9.4节：AUTO_INCREMENT自增列类型
 * - 2.9.3节：数值类型（SERIAL、BIGSERIAL）
 * - 2.2节：关键字和标识符（双引号分隔标识符）
 */
public class ShentongOfficialDocumentationDeepAnalysisTest {

    private static final Logger log = LoggerFactory.getLogger(ShentongOfficialDocumentationDeepAnalysisTest.class);
    
    private Transpiler transpiler;
    
    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }
    
    /**
     * 辅助方法：执行SQL转换并返回转换后的SQL字符串
     */
    private String convertSql(String sql, String targetDialect) {
        TranspilationResult result = transpiler.transpile(sql, "mysql", targetDialect);
        return result.translatedSql();
    }

    /**
     * 深度分析1：神通数据库官方文档2.9.1节特有字符串类型完整实现验证
     * 验证NAME类型和"CHAR"类型是否严格按照神通数据库官方文档2.9.1节实现
     */
    @Test
    public void deepAnalysisShentongOfficial291SpecificStringTypesCompleteImplementation() {
        log.info("=== 神通数据库官方文档2.9.1节特有字符串类型深度分析 ===");
        
        // 基于神通数据库官方文档2.9.1节的特有字符串类型
        String shentongSpecificTypesSQL = "CREATE TABLE shentong_specific_types (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name_field NAME, " +                     // NAME类型：最大长度127的字符数据
                "char_field \"CHAR\", " +                 // "CHAR"类型：固定长度为1的字符数据
                "regular_varchar VARCHAR(100)" +          // 普通VARCHAR类型
                ");";

        // 第一步：验证预处理器是否正确处理神通特有类型
        log.info("步骤1：验证预处理器对神通特有类型的处理");
        PreprocessingResult preprocessResult = Preprocessor.preprocess(shentongSpecificTypesSQL, true, "shentong");
        String preprocessedSQL = preprocessResult.cleanedSql();
        
        // 验证预处理器转换
        assertTrue(preprocessedSQL.contains("VARCHAR(127)"), 
                "预处理器应将NAME类型临时转换为VARCHAR(127)以便MySQL解析器理解");
        assertTrue(preprocessedSQL.contains("CHAR(1)"), 
                "预处理器应将\"CHAR\"类型临时转换为CHAR(1)以便MySQL解析器理解");
        assertFalse(preprocessedSQL.contains("NAME"), 
                "预处理后不应包含原始NAME关键字");
        assertFalse(preprocessedSQL.contains("\"CHAR\""), 
                "预处理后不应包含原始\"CHAR\"关键字");
        
        log.info("预处理器转换验证通过：NAME→VARCHAR(127), \"CHAR\"→CHAR(1)");

        // 第二步：验证完整转换流程
        log.info("步骤2：验证完整的SQL转换流程");
        String shentongResult = convertSql(shentongSpecificTypesSQL, "shentong");
        
        // 验证神通数据库特有类型的正确恢复
        assertTrue(shentongResult.contains("NAME") || shentongResult.contains("VARCHAR(127)"), 
                "神通：NAME类型应正确处理（符合神通官方文档2.9.1节：最大长度127字符）");
        assertTrue(shentongResult.contains("\"CHAR\"") || shentongResult.contains("CHAR(1)"), 
                "神通：\"CHAR\"类型应正确处理（符合神通官方文档2.9.1节：固定长度1字符）");
        assertTrue(shentongResult.contains("AUTO_INCREMENT"), 
                "神通：AUTO_INCREMENT应保持（符合神通官方文档2.9.4节）");
        assertTrue(shentongResult.contains("CHARACTER SET UTF8"), 
                "神通：应添加UTF8字符集（符合神通官方文档）");

        log.info("神通数据库官方文档2.9.1节特有字符串类型深度分析完成");
    }

    /**
     * 深度分析2：神通数据库官方文档2.6节ROWNUM伪列分页查询完整实现验证
     * 验证MySQL LIMIT语法转换为神通数据库ROWNUM分页是否严格按照神通数据库官方文档2.6节实现
     */
    @Test
    public void deepAnalysisShentongOfficial26RowNumPagingCompleteImplementation() {
        log.info("=== 神通数据库官方文档2.6节ROWNUM伪列分页查询深度分析 ===");
        
        // 基于神通数据库官方文档2.6节示例1：使用ROWNUM限制查询一次所获取的记录总数
        String mysqlLimitSQL = "SELECT employee_id, last_name FROM employees ORDER BY employee_id LIMIT 5;";
        String shentongResult1 = convertSql(mysqlLimitSQL, "shentong");
        
        // 验证转换为神通数据库ROWNUM分页语法（符合神通官方文档2.6节）
        assertTrue(shentongResult1.contains("ROWNUM") || shentongResult1.contains("ROW_NUM"), 
                "神通：LIMIT应转换为ROWNUM分页（符合神通官方文档2.6节规范）");
        assertTrue(shentongResult1.contains("ORDER BY"), 
                "神通：ORDER BY语法必须保持（符合神通官方文档）");
        assertTrue(shentongResult1.contains("<=") || shentongResult1.contains("<"), 
                "神通：ROWNUM条件应使用<=或<（符合神通官方文档2.6节示例）");
        
        log.info("LIMIT 5转换验证通过：" + shentongResult1);
        
        // 基于神通数据库官方文档2.6节示例3：伪列ROWNUM实现分页显示
        String mysqlLimitOffsetSQL = "SELECT employee_id, last_name FROM employees ORDER BY employee_id LIMIT 3 OFFSET 1;";
        String pagingResult = convertSql(mysqlLimitOffsetSQL, "shentong");
        
        assertTrue(pagingResult.contains("ROWNUM") || pagingResult.contains("ROW_NUM"), 
                "神通：LIMIT OFFSET应转换为ROWNUM分页（符合神通官方文档分页示例）");
        assertTrue(pagingResult.contains("BETWEEN") || pagingResult.contains("<="), 
                "神通：分页条件应使用BETWEEN或<=（符合神通官方文档）");
        assertTrue(pagingResult.contains("ORDER BY"), 
                "神通：ORDER BY语法必须保持（符合神通官方文档）");
        
        log.info("LIMIT OFFSET转换验证通过：" + pagingResult);

        // 验证复杂分页查询（基于神通官方文档2.6节示例3的嵌套查询模式）
        String complexPagingSQL = "SELECT * FROM (SELECT * FROM users ORDER BY id) WHERE ROWNUM BETWEEN 4 AND 5;";
        String complexResult = convertSql(complexPagingSQL, "shentong");
        
        assertTrue(complexResult.contains("ROWNUM") || complexResult.contains("ROW_NUM"), 
                "神通：复杂分页查询应保持ROWNUM语法（符合神通官方文档）");
        assertTrue(complexResult.contains("BETWEEN"), 
                "神通：BETWEEN条件应保持（符合神通官方文档2.6节示例3）");

        log.info("神通数据库官方文档2.6节ROWNUM伪列分页查询深度分析完成");
    }

    /**
     * 深度分析3：神通数据库官方文档2.9.4节AUTO_INCREMENT自增列完整实现验证
     * 验证AUTO_INCREMENT是否严格按照神通数据库官方文档2.9.4节实现
     */
    @Test
    public void deepAnalysisShentongOfficial294AutoIncrementCompleteImplementation() {
        log.info("=== 神通数据库官方文档2.9.4节AUTO_INCREMENT自增列深度分析 ===");
        
        // 基于神通数据库官方文档2.9.4节示例：在建表时不指定AUTO_INCREMENT的值
        String shentongAutoIncrementSQL = "CREATE TABLE tab7_1(" +
                "a INT AUTO_INCREMENT PRIMARY KEY, " +
                "b VARCHAR(10)" +
                ");";

        String shentongResult = convertSql(shentongAutoIncrementSQL, "shentong");
        
        // 验证神通数据库AUTO_INCREMENT特性
        assertTrue(shentongResult.contains("AUTO_INCREMENT"), 
                "神通：应保持AUTO_INCREMENT（符合神通官方文档2.9.4节示例）");
        assertTrue(shentongResult.contains("PRIMARY KEY"), 
                "神通：PRIMARY KEY应保持（符合神通官方文档）");
        assertTrue(shentongResult.contains("CHARACTER SET UTF8"), 
                "神通：应添加UTF8字符集（符合神通官方文档）");
        
        // 基于神通数据库官方文档2.9.4节：在建表时指定AUTO_INCREMENT的值
        String shentongAutoIncrementWithValueSQL = "CREATE TABLE tab7_2(" +
                "a INT AUTO_INCREMENT PRIMARY KEY, " +
                "b VARCHAR(10)" +
                ") AUTO_INCREMENT = 5;";
        
        String withValueResult = convertSql(shentongAutoIncrementWithValueSQL, "shentong");
        assertTrue(withValueResult.contains("AUTO_INCREMENT"), 
                "神通：AUTO_INCREMENT应保持（符合神通官方文档2.9.4节）");
        assertTrue(withValueResult.contains("AUTO_INCREMENT = 5") || withValueResult.contains("AUTO_INCREMENT=5"), 
                "神通：AUTO_INCREMENT初始值应保持（符合神通官方文档2.9.4节示例）");

        // 验证ALTER TABLE AUTO_INCREMENT语法（基于神通官方文档2.9.4节）
        String alterAutoIncrementSQL = "ALTER TABLE tab7_1 AUTO_INCREMENT = 6;";
        String alterResult = convertSql(alterAutoIncrementSQL, "shentong");
        assertTrue(alterResult.contains("AUTO_INCREMENT"), 
                "神通：ALTER TABLE AUTO_INCREMENT应保持（符合神通官方文档2.9.4节）");

        log.info("神通数据库官方文档2.9.4节AUTO_INCREMENT自增列深度分析完成");
    }

    /**
     * 深度分析4：神通数据库官方文档2.9.3节SERIAL类型完整实现验证
     * 验证SERIAL和BIGSERIAL类型是否严格按照神通数据库官方文档2.9.3节实现
     */
    @Test
    public void deepAnalysisShentongOfficial293SerialTypesCompleteImplementation() {
        log.info("=== 神通数据库官方文档2.9.3节SERIAL类型深度分析 ===");
        
        // 基于神通数据库官方文档2.9.3节的序列类型
        String shentongSerialSQL = "CREATE TABLE serial_test(" +
                "id SERIAL PRIMARY KEY, " +
                "big_id BIGSERIAL, " +
                "name VARCHAR(50)" +
                ");";

        String shentongResult = convertSql(shentongSerialSQL, "shentong");
        
        // 验证神通数据库SERIAL类型支持
        assertTrue(shentongResult.contains("SERIAL") || shentongResult.contains("AUTO_INCREMENT"), 
                "神通：SERIAL类型应保持或转换为AUTO_INCREMENT（符合神通官方文档2.9.3节）");
        assertTrue(shentongResult.contains("BIGSERIAL") || shentongResult.contains("BIGINT"), 
                "神通：BIGSERIAL类型应保持或转换（符合神通官方文档2.9.3节）");
        assertTrue(shentongResult.contains("PRIMARY KEY"), 
                "神通：PRIMARY KEY约束应保持（符合神通官方文档）");

        // 验证预处理器对SERIAL类型的处理
        PreprocessingResult preprocessResult = Preprocessor.preprocess(shentongSerialSQL, true, "shentong");
        String preprocessedSQL = preprocessResult.cleanedSql();
        
        assertTrue(preprocessedSQL.contains("INT AUTO_INCREMENT") || preprocessedSQL.contains("SERIAL"), 
                "预处理器应将SERIAL转换为INT AUTO_INCREMENT或保持SERIAL");
        assertTrue(preprocessedSQL.contains("BIGINT AUTO_INCREMENT") || preprocessedSQL.contains("BIGSERIAL"), 
                "预处理器应将BIGSERIAL转换为BIGINT AUTO_INCREMENT或保持BIGSERIAL");

        log.info("神通数据库官方文档2.9.3节SERIAL类型深度分析完成");
    }

    /**
     * 深度分析5：神通数据库官方文档2.2节标识符处理完整实现验证
     * 验证双引号分隔标识符是否严格按照神通数据库官方文档2.2节实现
     */
    @Test
    public void deepAnalysisShentongOfficial22IdentifierHandlingCompleteImplementation() {
        log.info("=== 神通数据库官方文档2.2节标识符处理深度分析 ===");
        
        // 基于神通数据库官方文档2.2节的分隔标识符
        String shentongIdentifierSQL = "CREATE TABLE \"test_table\" (" +
                "\"SELECT\" INT, " +                      // 使用保留关键字作为列名
                "\"user name\" VARCHAR(50), " +           // 包含空格的标识符
                "\"normal_column\" VARCHAR(100)" +
                ");";

        // 验证预处理器对双引号标识符的处理
        PreprocessingResult preprocessResult = Preprocessor.preprocess(shentongIdentifierSQL, true, "shentong");
        String preprocessedSQL = preprocessResult.cleanedSql();
        
        // 预处理器应将双引号转换为反引号以便MySQL解析器理解
        assertTrue(preprocessedSQL.contains("`test_table`") || preprocessedSQL.contains("\"test_table\""), 
                "预处理器应处理双引号标识符");
        assertTrue(preprocessedSQL.contains("`SELECT`") || preprocessedSQL.contains("\"SELECT\""), 
                "预处理器应处理保留关键字标识符");

        String shentongResult = convertSql(shentongIdentifierSQL, "shentong");
        
        // 验证神通数据库标识符处理
        assertTrue(shentongResult.contains("\"test_table\"") || shentongResult.contains("`test_table`"), 
                "神通：表名应使用双引号（符合神通官方文档2.2节分隔标识符规范）");
        assertTrue(shentongResult.contains("\"SELECT\"") || shentongResult.contains("`SELECT`"), 
                "神通：保留关键字列名应使用双引号（符合神通官方文档2.2节）");
        assertTrue(shentongResult.contains("\"user name\"") || shentongResult.contains("`user name`"), 
                "神通：包含空格的标识符应使用双引号（符合神通官方文档2.2节）");

        log.info("神通数据库官方文档2.2节标识符处理深度分析完成");
    }

    /**
     * 深度分析6：预处理器与生成器协同工作完整性验证
     * 验证预处理器和神通生成器是否完美协同工作，确保类型转换的往返一致性
     */
    @Test
    public void deepAnalysisPreprocessorGeneratorCooperationCompleteImplementation() {
        log.info("=== 预处理器与神通生成器协同工作深度分析 ===");
        
        // 包含所有神通特有类型的复杂SQL
        String complexShentongSQL = "CREATE TABLE complex_shentong_test (" +
                "id SERIAL PRIMARY KEY, " +
                "big_id BIGSERIAL, " +
                "name_field NAME, " +
                "char_field \"CHAR\", " +
                "nvarchar_field NVARCHAR2(100), " +
                "\"reserved keyword\" INT AUTO_INCREMENT, " +
                "normal_varchar VARCHAR(255)" +
                ");";

        // 第一步：验证预处理器转换
        PreprocessingResult preprocessResult = Preprocessor.preprocess(complexShentongSQL, true, "shentong");
        String preprocessedSQL = preprocessResult.cleanedSql();
        
        log.info("预处理器转换结果：" + preprocessedSQL);
        
        // 验证预处理器正确转换所有特有类型
        assertTrue(preprocessedSQL.contains("INT AUTO_INCREMENT"), 
                "预处理器应将SERIAL转换为INT AUTO_INCREMENT");
        assertTrue(preprocessedSQL.contains("BIGINT AUTO_INCREMENT"), 
                "预处理器应将BIGSERIAL转换为BIGINT AUTO_INCREMENT");
        assertTrue(preprocessedSQL.contains("VARCHAR(127)"), 
                "预处理器应将NAME转换为VARCHAR(127)");
        assertTrue(preprocessedSQL.contains("CHAR(1)"), 
                "预处理器应将\"CHAR\"转换为CHAR(1)");
        assertTrue(preprocessedSQL.contains("VARCHAR(100)"), 
                "预处理器应将NVARCHAR2转换为VARCHAR");

        // 第二步：验证完整转换流程
        String shentongResult = convertSql(complexShentongSQL, "shentong");
        
        log.info("最终神通转换结果：" + shentongResult);
        
        // 验证生成器正确恢复或保持神通特有类型
        assertTrue(shentongResult.contains("SERIAL") || shentongResult.contains("AUTO_INCREMENT"), 
                "神通生成器应正确处理SERIAL类型");
        assertTrue(shentongResult.contains("BIGSERIAL") || shentongResult.contains("BIGINT"), 
                "神通生成器应正确处理BIGSERIAL类型");
        assertTrue(shentongResult.contains("NAME") || shentongResult.contains("VARCHAR(127)"), 
                "神通生成器应正确恢复NAME类型");
        assertTrue(shentongResult.contains("\"CHAR\"") || shentongResult.contains("CHAR(1)"), 
                "神通生成器应正确恢复\"CHAR\"类型");
        assertTrue(shentongResult.contains("NVARCHAR2") || shentongResult.contains("VARCHAR"), 
                "神通生成器应正确处理NVARCHAR2类型");

        log.info("预处理器与神通生成器协同工作深度分析完成");
    }

    /**
     * 深度分析7：错误处理和边界情况完整性验证
     * 验证在各种边界情况下的错误处理是否符合神通数据库官方文档规范
     */
    @Test
    public void deepAnalysisErrorHandlingAndBoundaryCasesCompleteImplementation() {
        log.info("=== 错误处理和边界情况深度分析 ===");
        
        // 测试边界情况1：NAME类型在非列定义上下文中
        String nameInNonColumnContextSQL = "SELECT name FROM users WHERE name = 'test';";
        String result1 = convertSql(nameInNonColumnContextSQL, "shentong");
        assertFalse(result1.contains("VARCHAR(127)"), 
                "非列定义上下文中的name不应被转换为VARCHAR(127)");
        
        // 测试边界情况2：混合使用双引号和反引号
        String mixedQuotesSQL = "CREATE TABLE `test` (\"column\" INT, normal_col VARCHAR(50));";
        String result2 = convertSql(mixedQuotesSQL, "shentong");
        assertTrue(result2.contains("\"test\"") || result2.contains("`test`"), 
                "混合引号应正确处理");
        
        // 测试边界情况3：空的CREATE TABLE语句
        String emptyTableSQL = "CREATE TABLE empty_table ();";
        assertDoesNotThrow(() -> convertSql(emptyTableSQL, "shentong"), 
                "空表定义不应抛出异常");

        log.info("错误处理和边界情况深度分析完成");
    }
}
