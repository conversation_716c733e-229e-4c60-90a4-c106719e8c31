package com.xylink.sqltranspiler.infrastructure.parser.error;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * MySQL严格语法校验器测试
 * 根据 .augment/rules/rule-db.md 要求，基于官方文档进行测试
 * 
 * 测试策略：
 * 1. 验证正确的MySQL语法能够通过校验
 * 2. 验证非MySQL语法被正确拒绝
 * 3. 验证动态函数检测机制
 * 
 * <AUTHOR> Transpiler Team
 */
class MySqlStrictSyntaxValidatorTest {

    @Test
    @DisplayName("测试正确的MySQL语法应该通过校验")
    void testValidMySqlSyntax() {
        // 基于MySQL 8.4官方文档的标准语法
        String validSql = "SELECT NOW(), CONCAT('Hello', 'World'), COUNT(*) FROM users WHERE id > 0";
        
        MySqlSyntaxValidationResult result = MySqlStrictSyntaxValidator.validateMySqlSyntax(validSql);
        
        assertTrue(result.isValid(), "标准MySQL语法应该通过校验");
        assertEquals(0, result.getViolationCount(), "不应该有任何违规项");
    }

    @Test
    @DisplayName("测试PostgreSQL特有的random()函数应该被拒绝")
    void testPostgreSqlRandomFunction() {
        // PostgreSQL特有的random()函数
        String postgreSql = "SELECT random() FROM users";
        
        MySqlSyntaxValidationResult result = MySqlStrictSyntaxValidator.validateMySqlSyntax(postgreSql);
        
        assertFalse(result.isValid(), "PostgreSQL的random()函数应该被拒绝");
        assertTrue(result.hasViolations(), "应该检测到违规项");
        
        // 验证错误信息包含官方文档引用
        String errorReport = result.generateErrorReport();
        assertTrue(errorReport.contains("PostgreSQL"), "错误信息应该指出这是PostgreSQL特有函数");
        assertTrue(errorReport.contains("MySQL"), "错误信息应该提供MySQL替代方案");
    }

    @Test
    @DisplayName("测试PostgreSQL特有的::text类型转换语法应该被拒绝")
    void testPostgreSqlTypeCasting() {
        // PostgreSQL特有的::类型转换语法
        String postgreSql = "SELECT id::text, name::varchar FROM users";
        
        MySqlSyntaxValidationResult result = MySqlStrictSyntaxValidator.validateMySqlSyntax(postgreSql);
        
        assertFalse(result.isValid(), "PostgreSQL的::类型转换语法应该被拒绝");
        assertTrue(result.hasViolations(), "应该检测到违规项");
        
        // 验证建议使用CAST()函数
        String errorReport = result.generateErrorReport();
        assertTrue(errorReport.contains("CAST"), "错误信息应该建议使用CAST()函数");
        assertTrue(errorReport.contains("CONVERT"), "错误信息应该建议使用CONVERT()函数");
    }

    @Test
    @DisplayName("测试Oracle特有的NVL函数应该被拒绝")
    void testOracleNvlFunction() {
        // Oracle特有的NVL函数
        String oracleSql = "SELECT NVL(name, 'Unknown') FROM users";
        
        MySqlSyntaxValidationResult result = MySqlStrictSyntaxValidator.validateMySqlSyntax(oracleSql);
        
        assertFalse(result.isValid(), "Oracle的NVL函数应该被拒绝");
        assertTrue(result.hasViolations(), "应该检测到违规项");
        
        // 验证错误信息
        String errorReport = result.generateErrorReport();
        assertTrue(errorReport.contains("Oracle"), "错误报告应该指出这是Oracle特有函数");
    }

    @Test
    @DisplayName("测试SQL Server特有的TOP语法应该被拒绝")
    void testSqlServerTopSyntax() {
        // SQL Server特有的TOP语法
        String sqlServerSql = "SELECT TOP 10 * FROM users";
        
        MySqlSyntaxValidationResult result = MySqlStrictSyntaxValidator.validateMySqlSyntax(sqlServerSql);
        
        assertFalse(result.isValid(), "SQL Server的TOP语法应该被拒绝");
        assertTrue(result.hasViolations(), "应该检测到违规项");
        
        // 验证建议使用LIMIT
        String errorReport = result.generateErrorReport();
        assertTrue(errorReport.contains("LIMIT"), "错误信息应该建议使用LIMIT子句");
    }

    @Test
    @DisplayName("测试动态函数检测 - 未知函数应该被标记")
    void testDynamicFunctionDetection() {
        // 包含未知函数的SQL
        String unknownFunctionSql = "SELECT unknown_function(id), CONCAT(name) FROM users";
        
        MySqlSyntaxValidationResult result = MySqlStrictSyntaxValidator.validateMySqlSyntax(unknownFunctionSql);
        
        assertFalse(result.isValid(), "未知函数应该被检测到");
        assertTrue(result.hasViolations(), "应该检测到违规项");
        
        // 验证错误信息
        String errorReport = result.generateErrorReport();
        assertTrue(errorReport.contains("unknown_function") || errorReport.contains("UNKNOWN_FUNCTION"),
                  "错误信息应该包含具体的未知函数名");
        assertTrue(errorReport.contains("MySQL") && errorReport.contains("官方"),
                  "错误信息应该引用MySQL官方文档");
    }

    @Test
    @DisplayName("测试MySQL官方函数应该被正确识别")
    void testMySqlOfficialFunctions() {
        // 使用多个MySQL官方函数
        String mySqlFunctions = """
            SELECT 
                NOW(), CURDATE(), CURTIME(),
                CONCAT('Hello', 'World'),
                SUBSTRING(name, 1, 10),
                COUNT(*), MAX(id), MIN(id),
                IFNULL(email, 'no-email'),
                JSON_EXTRACT(data, '$.name')
            FROM users 
            WHERE LENGTH(name) > 0
            """;
        
        MySqlSyntaxValidationResult result = MySqlStrictSyntaxValidator.validateMySqlSyntax(mySqlFunctions);
        
        assertTrue(result.isValid(), "所有MySQL官方函数应该通过校验");
        assertEquals(0, result.getViolationCount(), "不应该有任何违规项");
    }

    @Test
    @DisplayName("测试混合语法 - 部分正确部分错误")
    void testMixedSyntax() {
        // 混合了正确的MySQL语法和错误的PostgreSQL语法
        String mixedSql = "SELECT NOW(), random(), CONCAT(name) FROM users WHERE id::integer > 0";
        
        MySqlSyntaxValidationResult result = MySqlStrictSyntaxValidator.validateMySqlSyntax(mixedSql);
        
        assertFalse(result.isValid(), "混合语法应该被拒绝");
        assertTrue(result.getViolationCount() >= 2, "应该检测到多个违规项");
        
        // 验证检测到了random()和::integer
        String errorReport = result.generateErrorReport();
        assertTrue(errorReport.contains("random"), "应该检测到random()函数");
        assertTrue(errorReport.contains("::"), "应该检测到::类型转换语法");
    }

    @Test
    @DisplayName("测试复杂SQL语句的综合校验")
    void testComplexSqlValidation() {
        // 复杂的MySQL标准SQL
        String complexSql = """
            WITH RECURSIVE category_tree AS (
                SELECT id, name, parent_id, 1 as level
                FROM categories 
                WHERE parent_id IS NULL
                UNION ALL
                SELECT c.id, c.name, c.parent_id, ct.level + 1
                FROM categories c
                JOIN category_tree ct ON c.parent_id = ct.id
            )
            SELECT 
                ct.name,
                ct.level,
                COUNT(p.id) as product_count,
                AVG(p.price) as avg_price,
                JSON_OBJECT('level', ct.level, 'count', COUNT(p.id)) as summary
            FROM category_tree ct
            LEFT JOIN products p ON p.category_id = ct.id
            WHERE ct.level <= 3
            GROUP BY ct.id, ct.name, ct.level
            HAVING COUNT(p.id) > 0
            ORDER BY ct.level, ct.name
            LIMIT 100
            """;
        
        MySqlSyntaxValidationResult result = MySqlStrictSyntaxValidator.validateMySqlSyntax(complexSql);
        
        // 注意：CTE在MySQL 8.0+中是支持的
        assertTrue(result.isValid(), "复杂的MySQL标准SQL应该通过校验");
    }
}
