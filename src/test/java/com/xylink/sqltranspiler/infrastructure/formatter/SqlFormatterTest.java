package com.xylink.sqltranspiler.infrastructure.formatter;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

/**
 * SQL格式化器测试
 */
public class SqlFormatterTest {

    @Test
    public void testFormatCreateTableWithComment() {
        String input = "-- 神通数据库CREATE TABLE语句 CREATE TABLE \"orders\" ( \"id\" INTEGER AUTO_INCREMENT PRIMARY KEY, \"customer_name\" VARCHAR(100), \"order_date\" DATE, \"total_amount\" DECIMAL(10,2), \"status\" VARCHAR(50) DEFAULT 'pending' ) CHARACTER SET UTF8;";

        String result = SqlFormatter.format(input);

        System.out.println("Input:");
        System.out.println(input);
        System.out.println("\nFormatted:");
        System.out.println(result);

        // 调试信息
        System.out.println("\nDebug info:");
        System.out.println("Input starts with '--': " + input.trim().startsWith("--"));
        System.out.println("Input contains 'CREATE TABLE': " + input.contains("CREATE TABLE"));
        System.out.println("CREATE TABLE index: " + input.indexOf("CREATE TABLE"));

        // 验证格式化结果
        assertTrue(result.contains("-- 神通数据库CREATE TABLE语句"));
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"id\" INTEGER"));

        // 验证注释和CREATE TABLE是否分离
        String[] lines = result.split("\n");
        System.out.println("Number of lines: " + lines.length);
        for (int i = 0; i < lines.length; i++) {
            System.out.println("Line " + i + ": " + lines[i]);
        }
        assertTrue(lines.length > 1, "Should have multiple lines");
        assertTrue(lines[0].startsWith("--"), "First line should be comment");
    }

    @Test
    public void testFormatSelectStatement() {
        String input = "SELECT u.id, u.name, p.title, COUNT(*) as total FROM users u LEFT JOIN posts p ON u.id = p.user_id WHERE u.created_at > '2023-01-01' GROUP BY u.id, u.name, p.title HAVING COUNT(*) > 5 ORDER BY total DESC LIMIT 10;";
        
        String result = SqlFormatter.format(input);
        
        System.out.println("Input:");
        System.out.println(input);
        System.out.println("\nFormatted:");
        System.out.println(result);
        
        // 验证格式化结果
        assertTrue(result.contains("SELECT"));
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("WHERE"));
        assertTrue(result.contains("GROUP BY"));
        assertTrue(result.contains("ORDER BY"));
    }

    @Test
    public void testFormatCreateTableNormal() {
        String input = "CREATE TABLE users ( id INT PRIMARY KEY, name VARCHAR(100), email VARCHAR(255), created_at TIMESTAMP DEFAULT SYSDATE );";
        
        String result = SqlFormatter.format(input);
        
        System.out.println("Input:");
        System.out.println(input);
        System.out.println("\nFormatted:");
        System.out.println(result);
        
        // 验证格式化结果
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("id INT"));
        assertTrue(result.contains("name VARCHAR(100)"));
    }
}
