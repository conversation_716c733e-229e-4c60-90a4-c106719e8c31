package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 达梦标识符引用测试
 * 严格遵守达梦官方文档规范：https://eco.dameng.com/document/dm/zh-cn/pm/sql-appendix
 * 
 * 测试策略：
 * 1. 普通标识符不需要双引号
 * 2. 保留关键字需要双引号
 * 3. 混合大小写标识符需要双引号
 * 4. 特殊字符标识符需要双引号
 */
@DisplayName("达梦标识符引用测试 - 官方文档规范")
public class DamengIdentifierQuotingTest {

    private DamengGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("测试普通标识符 - 不需要双引号")
    public void testNormalIdentifiers() {
        String sql = "CREATE TABLE t_user (" +
                    "id INT, " +
                    "name VARCHAR(50), " +
                    "email VARCHAR(100), " +
                    "created_at TIMESTAMP" +
                    ");";
        CreateTable createTable = (CreateTable) MySqlHelper.parseStatement(sql);
        String result = generator.generate(createTable);
        
        // 普通标识符不应该有双引号
        assertTrue(result.contains("CREATE TABLE t_user"));
        assertTrue(result.contains("id INT"));
        assertTrue(result.contains("name VARCHAR"));
        assertTrue(result.contains("email VARCHAR"));
        assertTrue(result.contains("created_at TIMESTAMP"));
        
        // 确保没有不必要的双引号
        assertFalse(result.contains("\"t_user\""));
        assertFalse(result.contains("\"id\""));
        assertFalse(result.contains("\"name\""));
        assertFalse(result.contains("\"email\""));
    }

    @Test
    @DisplayName("测试保留关键字 - 需要双引号")
    public void testReservedKeywords() {
        String sql = "CREATE TABLE `order` (" +
                    "`table` VARCHAR(50), " +
                    "`index` INT, " +
                    "`user` VARCHAR(100), " +
                    "`group` VARCHAR(50)" +
                    ");";
        CreateTable createTable = (CreateTable) MySqlHelper.parseStatement(sql);
        String result = generator.generate(createTable);
        
        // 保留关键字应该有双引号
        assertTrue(result.contains("CREATE TABLE \"order\""));
        assertTrue(result.contains("\"table\" VARCHAR"));
        assertTrue(result.contains("\"index\" INT"));
        assertTrue(result.contains("\"user\" VARCHAR"));
        assertTrue(result.contains("\"group\" VARCHAR"));
    }

    @Test
    @DisplayName("测试混合大小写标识符 - 根据达梦官方文档，普通标识符不需要双引号")
    public void testMixedCaseIdentifiers() {
        String sql = "CREATE TABLE myTable (" +
                    "userId INT, " +
                    "firstName VARCHAR(50), " +
                    "lastName VARCHAR(50)" +
                    ");";
        CreateTable createTable = (CreateTable) MySqlHelper.parseStatement(sql);
        String result = generator.generate(createTable);

        // 根据达梦官方文档，普通标识符（包括混合大小写）不需要双引号
        // 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/practice-single-table.html#2-9-使用列别名
        // 官方示例：SELECT employee_id AS "员工编号", employee_name AS "员工姓名" FROM dmhr.employee;
        // 可见普通标识符 employee_id, employee_name, dmhr.employee 都没有双引号
        assertTrue(result.contains("CREATE TABLE myTable") || result.contains("CREATE TABLE \"myTable\""));
        assertTrue(result.contains("userId INT") || result.contains("\"userId\" INT"));
        assertTrue(result.contains("firstName VARCHAR") || result.contains("\"firstName\" VARCHAR"));
        assertTrue(result.contains("lastName VARCHAR") || result.contains("\"lastName\" VARCHAR"));
    }

    @Test
    @DisplayName("测试混合情况 - 普通标识符和保留关键字")
    public void testMixedScenario() {
        String sql = "CREATE TABLE normal_table (" +
                    "`index` INT, " +
                    "regular_column VARCHAR(100), " +
                    "`order` VARCHAR(50), " +
                    "another_normal_col INT" +
                    ");";
        CreateTable createTable = (CreateTable) MySqlHelper.parseStatement(sql);
        String result = generator.generate(createTable);
        
        // 普通标识符无双引号
        assertTrue(result.contains("CREATE TABLE normal_table"));
        assertTrue(result.contains("regular_column VARCHAR"));
        assertTrue(result.contains("another_normal_col INT"));
        
        // 保留关键字有双引号
        assertTrue(result.contains("\"index\" INT"));
        assertTrue(result.contains("\"order\" VARCHAR"));
        
        // 确保普通标识符没有不必要的双引号
        assertFalse(result.contains("\"normal_table\""));
        assertFalse(result.contains("\"regular_column\""));
        assertFalse(result.contains("\"another_normal_col\""));
    }

    @Test
    @DisplayName("测试SELECT语句中的标识符引用")
    public void testSelectStatementIdentifiers() {
        String sql = "SELECT id, name, `order`, `table` FROM users WHERE `index` = 1";
        QueryStmt select = (QueryStmt) MySqlHelper.parseStatement(sql);
        String result = generator.generate(select);
        
        // 普通标识符无双引号
        assertTrue(result.contains("id"));
        assertTrue(result.contains("name"));
        assertTrue(result.contains("FROM users"));
        
        // 保留关键字有双引号
        assertTrue(result.contains("\"order\""));
        assertTrue(result.contains("\"table\""));
        assertTrue(result.contains("\"index\""));
        
        // 确保普通标识符没有不必要的双引号
        assertFalse(result.contains("\"id\""));
        assertFalse(result.contains("\"name\""));
        assertFalse(result.contains("\"users\""));
    }

    @Test
    @DisplayName("测试DUAL表 - 不需要双引号")
    public void testDualTable() {
        String sql = "SELECT 1 FROM DUAL";
        QueryStmt select = (QueryStmt) MySqlHelper.parseStatement(sql);
        String result = generator.generate(select);
        
        // DUAL是大写标识符，不需要双引号
        assertTrue(result.contains("FROM DUAL"));
        assertFalse(result.contains("\"DUAL\""));
    }

    @Test
    @DisplayName("测试大小写变体")
    public void testCaseVariants() {
        // 测试不同大小写的相同标识符
        String[] testCases = {
            "CREATE TABLE users (id INT);",           // 全小写，无需引号
            "CREATE TABLE USERS (ID INT);",           // 全大写，无需引号
            "CREATE TABLE Users (Id INT);",           // 混合大小写，根据达梦官方文档无需引号
            "CREATE TABLE `order` (id INT);",         // 保留关键字，需要引号
            "CREATE TABLE `ORDER` (id INT);"          // 大写保留关键字，需要引号
        };

        String[] expectedPatterns = {
            "CREATE TABLE users",      // 无引号
            "CREATE TABLE USERS",      // 无引号
            "CREATE TABLE Users",      // 根据达梦官方文档，普通标识符无需引号
            "CREATE TABLE \"order\"",  // 有引号（保留字）
            "CREATE TABLE \"ORDER\""   // 有引号（保留字）
        };
        
        for (int i = 0; i < testCases.length; i++) {
            Statement statement = MySqlHelper.parseStatement(testCases[i]);
            assertTrue(statement instanceof CreateTable, "解析结果应该是CreateTable类型");
            CreateTable createTable = (CreateTable) statement;
            String result = generator.generate(statement);
            assertTrue(result.contains(expectedPatterns[i]), 
                "Test case " + i + " failed. Expected: " + expectedPatterns[i] + ", Got: " + result);
        }
    }
}
