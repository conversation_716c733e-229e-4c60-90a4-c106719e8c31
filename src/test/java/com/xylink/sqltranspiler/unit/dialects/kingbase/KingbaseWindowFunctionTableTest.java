package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金仓数据库窗口函数表结构测试
 * 
 * 根据金仓官方文档，测试MySQL窗口函数相关表结构到金仓的转换
 * 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * 
 * 测试覆盖：
 * - 窗口函数支持的表结构
 * - 分析函数的数据类型要求
 * - 排序和分组相关的字段定义
 * - 聚合窗口函数的数据存储
 */
@DisplayName("金仓数据库窗口函数表结构测试")
public class KingbaseWindowFunctionTableTest {

    private KingbaseGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new KingbaseGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本窗口函数支持的表结构")
    void testBasicWindowFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE window_function_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "department_id INT NOT NULL, " +
                "employee_id INT NOT NULL, " +
                "salary DECIMAL(10,2) NOT NULL, " +
                "bonus DECIMAL(8,2), " +
                "hire_date DATE NOT NULL, " +
                "performance_score DECIMAL(5,2), " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "INDEX idx_dept_salary (department_id, salary), " +
                "INDEX idx_hire_date (hire_date)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("基本窗口函数支持的表结构转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证窗口函数支持的表结构
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"window_function_data\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"department_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"employee_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"salary\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"bonus\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"hire_date\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"performance_score\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"created_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("DECIMAL(10,2)"), "应保留薪资DECIMAL类型");
        assertTrue(kingbaseSql.contains("DECIMAL(8,2)"), "应保留奖金DECIMAL类型");
        assertTrue(kingbaseSql.contains("DECIMAL(5,2)"), "应保留评分DECIMAL类型");
        assertTrue(kingbaseSql.contains("DATE"), "应保留DATE类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("CURRENT_TIMESTAMP"), "应保留时间函数");
        
        // 验证窗口函数相关索引处理
        assertTrue(kingbaseSql.contains("idx_dept_salary") || kingbaseSql.contains("department_id"), 
                  "应处理部门薪资索引");
        assertTrue(kingbaseSql.contains("idx_hire_date") || kingbaseSql.contains("hire_date"), 
                  "应处理入职日期索引");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试排名函数支持的表结构")
    void testRankingFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE ranking_function_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "category_id INT NOT NULL, " +
                "subcategory_id INT, " +
                "product_name VARCHAR(200) NOT NULL, " +
                "sales_amount DECIMAL(15,2) NOT NULL, " +
                "sales_quantity INT NOT NULL, " +
                "sales_date DATE NOT NULL, " +
                "region_id INT, " +
                "rank_value INT, " +
                "dense_rank_value INT, " +
                "row_number_value INT, " +
                "ntile_value INT, " +
                "INDEX idx_category_sales (category_id, sales_amount DESC), " +
                "INDEX idx_date_region (sales_date, region_id)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("排名函数支持的表结构转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证排名函数支持的表结构
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"ranking_function_data\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"category_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"subcategory_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"product_name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"sales_amount\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"sales_quantity\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"sales_date\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"region_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"rank_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"dense_rank_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"row_number_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"ntile_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("VARCHAR(200)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("DECIMAL(15,2)"), "应保留销售金额DECIMAL类型");
        assertTrue(kingbaseSql.contains("DATE"), "应保留DATE类型");
        
        // 验证排名函数相关索引处理
        assertTrue(kingbaseSql.contains("idx_category_sales") || kingbaseSql.contains("category_id"), 
                  "应处理分类销售索引");
        assertTrue(kingbaseSql.contains("idx_date_region") || kingbaseSql.contains("sales_date"), 
                  "应处理日期区域索引");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试聚合窗口函数支持的表结构")
    void testAggregateWindowFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE aggregate_window_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "group_id INT NOT NULL, " +
                "sequence_number INT NOT NULL, " +
                "value DECIMAL(12,4) NOT NULL, " +
                "running_sum DECIMAL(20,4), " +
                "running_avg DECIMAL(15,6), " +
                "running_count INT, " +
                "running_min DECIMAL(12,4), " +
                "running_max DECIMAL(12,4), " +
                "moving_avg_3 DECIMAL(15,6), " +
                "moving_sum_5 DECIMAL(20,4), " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "INDEX idx_group_seq (group_id, sequence_number)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("聚合窗口函数支持的表结构转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证聚合窗口函数支持的表结构
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"aggregate_window_data\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"group_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"sequence_number\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"running_sum\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"running_avg\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"running_count\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"running_min\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"running_max\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"moving_avg_3\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"moving_sum_5\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"created_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("DECIMAL(12,4)"), "应保留基础值DECIMAL类型");
        assertTrue(kingbaseSql.contains("DECIMAL(20,4)"), "应保留累计和DECIMAL类型");
        assertTrue(kingbaseSql.contains("DECIMAL(15,6)"), "应保留平均值DECIMAL类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("CURRENT_TIMESTAMP"), "应保留时间函数");
        
        // 验证聚合窗口函数相关索引处理
        assertTrue(kingbaseSql.contains("idx_group_seq") || kingbaseSql.contains("group_id"), 
                  "应处理分组序列索引");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试偏移函数支持的表结构")
    void testOffsetFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE offset_function_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "partition_key VARCHAR(50) NOT NULL, " +
                "order_key INT NOT NULL, " +
                "current_value DECIMAL(10,2), " +
                "lag_1_value DECIMAL(10,2), " +
                "lag_2_value DECIMAL(10,2), " +
                "lead_1_value DECIMAL(10,2), " +
                "lead_2_value DECIMAL(10,2), " +
                "first_value DECIMAL(10,2), " +
                "last_value DECIMAL(10,2), " +
                "nth_value DECIMAL(10,2), " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "INDEX idx_partition_order (partition_key, order_key)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("偏移函数支持的表结构转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证偏移函数支持的表结构
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"offset_function_data\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"partition_key\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"order_key\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"current_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"lag_1_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"lag_2_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"lead_1_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"lead_2_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"first_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"last_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"nth_value\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"created_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("VARCHAR(50)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("CURRENT_TIMESTAMP"), "应保留时间函数");
        
        // 验证偏移函数相关索引处理
        assertTrue(kingbaseSql.contains("idx_partition_order") || kingbaseSql.contains("partition_key"), 
                  "应处理分区排序索引");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试金仓PostgreSQL兼容的窗口函数特性")
    void testKingbasePostgreSQLWindowFunctionFeatures() {
        String mysqlSql = "CREATE TABLE kingbase_window_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "json_data JSON, " +
                "text_data TEXT, " +
                "blob_data LONGBLOB, " +
                "numeric_data DECIMAL(18,8), " +
                "boolean_data BOOLEAN DEFAULT FALSE, " +
                "array_data TEXT, " +
                "partition_column VARCHAR(100), " +
                "order_column DECIMAL(15,4), " +
                "window_result TEXT, " +
                "analytical_result DECIMAL(20,10), " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓PostgreSQL兼容窗口函数特性:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证金仓PostgreSQL兼容窗口函数特性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"kingbase_window_features\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        
        // 验证PostgreSQL兼容类型在窗口函数中的支持
        assertTrue(kingbaseSql.contains("JSON"), "应支持JSON类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        assertTrue(kingbaseSql.contains("LONGBLOB") || kingbaseSql.contains("BYTEA"), 
                  "应保留或转换大二进制类型");
        assertTrue(kingbaseSql.contains("DECIMAL(18,8)"), "应保留高精度DECIMAL类型");
        assertTrue(kingbaseSql.contains("DECIMAL(15,4)"), "应保留排序列DECIMAL类型");
        assertTrue(kingbaseSql.contains("DECIMAL(20,10)"), "应保留分析结果DECIMAL类型");
        assertTrue(kingbaseSql.contains("BOOLEAN"), "应保留BOOLEAN类型");
        
        // 验证窗口函数相关字段
        assertTrue(kingbaseSql.contains("\"json_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"text_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"blob_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"numeric_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"boolean_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"array_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"partition_column\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"order_column\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"window_result\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"analytical_result\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"created_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留分区列VARCHAR类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("FALSE"), "应保留布尔默认值");
        assertTrue(kingbaseSql.contains("CURRENT_TIMESTAMP"), "应保留时间函数");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }
}
