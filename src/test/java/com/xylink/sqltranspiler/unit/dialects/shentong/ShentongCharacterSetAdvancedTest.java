package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库字符集高级特性测试
 * 基于神通数据库官方文档 shentong.md 第2.1节字符集规范
 * 
 * 根据官方文档第61行：神通数据库的字符集包括英文大小写字母以及ASCII码为128-255的字符
 * 
 * 测试覆盖：
 * 1. ASCII码128-255字符处理
 * 2. 扩展字符集支持验证
 * 3. 字符编码转换测试
 * 4. 多字节字符处理
 */
public class ShentongCharacterSetAdvancedTest extends BaseShentongConversionTest {

    /**
     * 测试ASCII码128-255字符处理
     * 根据官方文档第61行：神通数据库的字符集包括英文大小写字母以及ASCII码为128-255的字符
     */
    @Test
    @DisplayName("验证ASCII码128-255字符处理")
    public void testAscii128To255CharacterHandling() throws Exception {
        // 构造包含ASCII码128-255字符的SQL
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("CREATE TABLE extended_chars_test (\n");
        sqlBuilder.append("    id INT PRIMARY KEY,\n");
        
        // 添加包含扩展ASCII字符的列名和数据
        // ASCII 128-255范围内的一些字符
        String extendedChars = "àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ";
        sqlBuilder.append("    col_").append(extendedChars.substring(0, 5)).append(" VARCHAR(100),\n");
        sqlBuilder.append("    description VARCHAR(200)\n");
        sqlBuilder.append(");\n");
        
        sqlBuilder.append("INSERT INTO extended_chars_test VALUES (1, '").append(extendedChars).append("', 'Extended ASCII test');");
        
        String mysqlSql = sqlBuilder.toString();
        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证扩展ASCII字符被正确处理
        assertTrue(shentongSql.contains("CREATE TABLE"), "应包含CREATE TABLE语句");
        assertTrue(shentongSql.contains("INSERT INTO"), "应包含INSERT INTO语句");
        assertTrue(shentongSql.contains(extendedChars), "应保留扩展ASCII字符");
        
        // 验证字符集声明
        assertTrue(shentongSql.contains("CHARACTER SET UTF8") || 
                   shentongSql.contains("CHARSET UTF8"), "应包含UTF8字符集声明");
    }

    /**
     * 测试多字节字符处理
     * 验证神通数据库对Unicode字符的支持
     */
    @Test
    @DisplayName("验证多字节字符处理")
    public void testMultiByteCharacterHandling() throws Exception {
        String mysqlSql = """
            CREATE TABLE unicode_test (
                id INT PRIMARY KEY,
                chinese_name VARCHAR(50) COMMENT '中文姓名',
                japanese_name VARCHAR(50) COMMENT '日本語名前',
                korean_name VARCHAR(50) COMMENT '한국어 이름',
                emoji_field VARCHAR(100) COMMENT '😀😃😄😁😆'
            ) DEFAULT CHARSET=utf8mb4;
            
            INSERT INTO unicode_test VALUES 
            (1, '张三', '田中太郎', '김철수', '😀😃😄'),
            (2, '李四', '佐藤花子', '박영희', '🎉🎊🎈');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证多字节字符被正确保留
        assertTrue(shentongSql.contains("张三"), "应保留中文字符");
        assertTrue(shentongSql.contains("田中太郎"), "应保留日文字符");
        assertTrue(shentongSql.contains("김철수"), "应保留韩文字符");
        assertTrue(shentongSql.contains("😀😃😄"), "应保留emoji字符");
        
        // 验证字符集转换
        assertTrue(shentongSql.contains("CHARACTER SET UTF8"), "应转换为神通的UTF8字符集");
        assertFalse(shentongSql.contains("utf8mb4"), "不应包含MySQL特有的utf8mb4");
    }

    /**
     * 测试字符集转换规则
     * 验证MySQL字符集到神通字符集的正确转换
     */
    @Test
    @DisplayName("验证字符集转换规则")
    public void testCharacterSetConversionRules() throws Exception {
        String[] mysqlCharsets = {
            "DEFAULT CHARSET=utf8",
            "DEFAULT CHARSET=utf8mb4",
            "CHARACTER SET latin1",
            "CHARACTER SET ascii",
            "CHARSET=gbk"
        };
        
        for (String charset : mysqlCharsets) {
            String mysqlSql = "CREATE TABLE charset_test (id INT) " + charset + ";";
            String shentongSql = convertMySqlToShentong(mysqlSql);
            
            // 验证基本转换成功
            assertBasicConversionRequirements(shentongSql);
            
            // 验证字符集转换
            assertTrue(shentongSql.contains("CHARACTER SET UTF8"), 
                       "所有MySQL字符集都应转换为神通的UTF8: " + charset);
            assertFalse(shentongSql.contains("DEFAULT CHARSET"), 
                        "不应包含MySQL的DEFAULT CHARSET语法: " + charset);
        }
    }

    /**
     * 测试字符长度计算
     * 验证神通数据库的字符长度计算方式
     */
    @Test
    @DisplayName("验证字符长度计算")
    public void testCharacterLengthCalculation() throws Exception {
        String mysqlSql = """
            CREATE TABLE length_test (
                ascii_field VARCHAR(10),
                utf8_field VARCHAR(10),
                mixed_field VARCHAR(20)
            );
            
            INSERT INTO length_test VALUES 
            ('abcdefghij', '中文测试12', 'Mixed中文😀');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证VARCHAR长度保持不变（神通按字符计算）
        assertTrue(shentongSql.contains("VARCHAR(10)"), "应保留VARCHAR(10)长度");
        assertTrue(shentongSql.contains("VARCHAR(20)"), "应保留VARCHAR(20)长度");
        
        // 验证数据插入正确
        assertTrue(shentongSql.contains("中文测试12"), "应保留中文数据");
        assertTrue(shentongSql.contains("Mixed中文😀"), "应保留混合字符数据");
    }

    /**
     * 测试特殊字符转义
     * 验证SQL中特殊字符的正确转义处理
     */
    @Test
    @DisplayName("验证特殊字符转义")
    public void testSpecialCharacterEscaping() throws Exception {
        String mysqlSql = """
            CREATE TABLE escape_test (
                id INT PRIMARY KEY,
                single_quote VARCHAR(100),
                double_quote VARCHAR(100),
                backslash VARCHAR(100),
                newline VARCHAR(100)
            );
            
            INSERT INTO escape_test VALUES 
            (1, 'It''s a test', 'He said "Hello"', 'Path\\to\\file', 'Line1\\nLine2');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证特殊字符转义被正确处理
        assertTrue(shentongSql.contains("It''s a test"), "应保留单引号转义");
        assertTrue(shentongSql.contains("He said \"Hello\""), "应保留双引号");
        assertTrue(shentongSql.contains("Path\\\\to\\\\file") || 
                   shentongSql.contains("Path\\to\\file"), "应正确处理反斜杠");
        assertTrue(shentongSql.contains("Line1\\nLine2") || 
                   shentongSql.contains("Line1\nLine2"), "应正确处理换行符");
    }

    /**
     * 测试字符集兼容性
     * 验证神通数据库与MySQL字符集的兼容性
     */
    @Test
    @DisplayName("验证字符集兼容性")
    public void testCharacterSetCompatibility() throws Exception {
        String mysqlSql = """
            CREATE TABLE compatibility_test (
                id INT PRIMARY KEY,
                data VARCHAR(100)
            ) DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci;
            
            INSERT INTO compatibility_test VALUES 
            (1, 'ASCII text'),
            (2, 'Extended: àáâãäå'),
            (3, 'Unicode: 中文日本語한국어'),
            (4, 'Symbols: ©®™€£¥');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证所有字符类型都被正确处理
        assertTrue(shentongSql.contains("ASCII text"), "应支持ASCII字符");
        assertTrue(shentongSql.contains("àáâãäå"), "应支持扩展ASCII字符");
        assertTrue(shentongSql.contains("中文日本語한국어"), "应支持Unicode字符");
        assertTrue(shentongSql.contains("©®™€£¥"), "应支持特殊符号");
        
        // 验证字符集和排序规则转换
        assertTrue(shentongSql.contains("CHARACTER SET UTF8"), "应转换为神通UTF8字符集");
        assertFalse(shentongSql.contains("COLLATE"), "神通不需要显式COLLATE子句");
    }
}
