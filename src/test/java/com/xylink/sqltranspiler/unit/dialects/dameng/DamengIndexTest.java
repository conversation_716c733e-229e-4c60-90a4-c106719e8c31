package com.xylink.sqltranspiler.unit.dialects.dameng;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateIndex;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 达梦数据库索引功能测试
 * 基于达梦数据库官方文档的测试驱动开发
 * 参考：https://eco.dameng.com/document/dm/zh-cn/pm/
 * 
 * 验证MySQL CREATE INDEX语句到达梦数据库的转换功能
 */
public class DamengIndexTest {
    
    private static final Logger log = LoggerFactory.getLogger(DamengIndexTest.class);
    
    private DamengGenerator generator;
    
    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
        generator.clearState(); // 确保每个测试都有干净的状态
    }
    
    private CreateIndex parseCreateIndex(String sql) {
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "解析结果不应为null");
        assertTrue(statement instanceof CreateIndex, "应该解析为CreateIndex类型");
        return (CreateIndex) statement;
    }
    
    @Test
    @DisplayName("测试简单索引创建")
    void testSimpleIndex() {
        String sql = "CREATE INDEX idx_libra_conference_number_first_dept_id ON ainemo.libra_conference_number (first_dept_id)";
        
        CreateIndex createIndex = parseCreateIndex(sql);
        
        // 验证解析结果
        assertEquals("idx_libra_conference_number_first_dept_id", createIndex.getIndexName());
        assertEquals("libra_conference_number", createIndex.getTableId().getTableName());
        assertEquals("ainemo", createIndex.getTableId().getSchemaName());
        assertEquals(CreateIndex.IndexType.NORMAL, createIndex.getIndexType());
        assertNotNull(createIndex.getColumns());
        assertEquals(1, createIndex.getColumns().size());
        assertEquals("first_dept_id", createIndex.getColumns().get(0).getColumnName());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(createIndex);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦 SQL: {}", damengSql);
        
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE INDEX"), "应该包含CREATE INDEX，实际结果: " + damengSql);
        // 达梦数据库会按照标准生成索引名：idx_tablename_columnname
        assertTrue(damengSql.contains("idx_libra_conference_number_first_dept_id") ||
                   damengSql.toUpperCase().contains("IDX_LIBRA_CONFERENCE_NUMBER_FIRST_DEPT_ID"),
                   "应该包含标准索引名，实际结果: " + damengSql);
        assertTrue((damengSql.toUpperCase().contains("AINEMO") || damengSql.contains("ainemo")) &&
                   (damengSql.toUpperCase().contains("LIBRA_CONFERENCE_NUMBER") || damengSql.contains("libra_conference_number")),
                   "应该包含schema和表名，实际结果: " + damengSql);
        assertTrue(damengSql.toUpperCase().contains("FIRST_DEPT_ID") || damengSql.contains("first_dept_id"),
                   "应该包含列名，实际结果: " + damengSql);
        assertTrue(damengSql.endsWith(";"), "应该以分号结尾，实际结果: " + damengSql);
    }
    
    @Test
    @DisplayName("测试UNIQUE索引创建")
    void testUniqueIndex() {
        String sql = "CREATE UNIQUE INDEX uk_email ON users(email)";
        
        CreateIndex createIndex = parseCreateIndex(sql);
        String damengSql = generator.generate(createIndex);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦 SQL: {}", damengSql);
        
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE UNIQUE INDEX"), "应该包含CREATE UNIQUE INDEX，实际结果: " + damengSql);
        // 达梦数据库会按照标准生成索引名，可能不是原始的uk_email
        assertTrue(damengSql.contains("users") || damengSql.contains("USERS"), "应该包含表名users，实际结果: " + damengSql);
        assertTrue(damengSql.contains("email") || damengSql.contains("EMAIL"), "应该包含列名email，实际结果: " + damengSql);
        assertTrue(damengSql.endsWith(";"), "应该以分号结尾，实际结果: " + damengSql);
    }
    
    @Test
    @DisplayName("测试多列索引创建")
    void testMultiColumnIndex() {
        String sql = "CREATE INDEX idx_libra_cross_cloud_room_num_mapping_callee_room_number_enterprise_id_cloud_id ON ainemo.libra_cross_cloud_room_num_mapping (callee_room_number, enterprise_id, cloud_id)";
        
        CreateIndex createIndex = parseCreateIndex(sql);
        
        // 验证解析结果
        assertEquals("idx_libra_cross_cloud_room_num_mapping_callee_room_number_enterprise_id_cloud_id", createIndex.getIndexName());
        assertEquals("libra_cross_cloud_room_num_mapping", createIndex.getTableId().getTableName());
        assertEquals("ainemo", createIndex.getTableId().getSchemaName());
        assertEquals(CreateIndex.IndexType.NORMAL, createIndex.getIndexType());
        assertNotNull(createIndex.getColumns());
        assertEquals(3, createIndex.getColumns().size());
        assertEquals("callee_room_number", createIndex.getColumns().get(0).getColumnName());
        assertEquals("enterprise_id", createIndex.getColumns().get(1).getColumnName());
        assertEquals("cloud_id", createIndex.getColumns().get(2).getColumnName());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(createIndex);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦 SQL: {}", damengSql);
        
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE INDEX"), "应该包含CREATE INDEX，实际结果: " + damengSql);
        // 达梦数据库会按照标准生成索引名：idx_tablename_column1_column2_column3
        assertTrue(damengSql.contains("idx_libra_cross_cloud_room_num_mapping_callee_room_number_enterprise_id_cloud_id") ||
                   damengSql.toUpperCase().contains("IDX_LIBRA_CROSS_CLOUD_ROOM_NUM_MAPPING_CALLEE_ROOM_NUMBER_ENTERPRISE_ID_CLOUD_ID"),
                   "应该包含标准多列索引名，实际结果: " + damengSql);
        assertTrue((damengSql.toUpperCase().contains("AINEMO") || damengSql.contains("ainemo")) &&
                   (damengSql.toUpperCase().contains("LIBRA_CROSS_CLOUD_ROOM_NUM_MAPPING") || damengSql.contains("libra_cross_cloud_room_num_mapping")),
                   "应该包含schema和表名，实际结果: " + damengSql);
        assertTrue((damengSql.toUpperCase().contains("CALLEE_ROOM_NUMBER") || damengSql.contains("callee_room_number")) &&
                   (damengSql.toUpperCase().contains("ENTERPRISE_ID") || damengSql.contains("enterprise_id")) &&
                   (damengSql.toUpperCase().contains("CLOUD_ID") || damengSql.contains("cloud_id")),
                   "应该包含所有列名，实际结果: " + damengSql);
        assertTrue(damengSql.endsWith(";"), "应该以分号结尾，实际结果: " + damengSql);
    }
    
    @Test
    @DisplayName("测试带schema的索引创建")
    void testIndexWithSchema() {
        String sql = "CREATE INDEX idx_order_status ON sales.orders(status)";
        
        CreateIndex createIndex = parseCreateIndex(sql);
        String damengSql = generator.generate(createIndex);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦 SQL: {}", damengSql);
        
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE INDEX"), "应该包含CREATE INDEX，实际结果: " + damengSql);
        // 达梦数据库会按照标准生成索引名：idx_tablename_columnname
        assertTrue(damengSql.contains("idx_orders_status") || damengSql.contains("IDX_ORDERS_STATUS"),
                   "应该包含标准索引名，实际结果: " + damengSql);
        // 验证schema保留
        assertTrue(damengSql.contains("sales") || damengSql.contains("SALES"),
                   "应该包含schema名，实际结果: " + damengSql);
        assertTrue(damengSql.contains("orders") || damengSql.contains("ORDERS"),
                   "应该包含表名，实际结果: " + damengSql);
        assertTrue(damengSql.contains("status") || damengSql.contains("STATUS"),
                   "应该包含列名，实际结果: " + damengSql);
        assertTrue(damengSql.endsWith(";"), "应该以分号结尾，实际结果: " + damengSql);
    }
    
    @Test
    @DisplayName("测试无schema的索引创建")
    void testIndexWithoutSchema() {
        String sql = "CREATE INDEX idx_name ON users(name)";
        
        CreateIndex createIndex = parseCreateIndex(sql);
        String damengSql = generator.generate(createIndex);
        
        log.info("MySQL SQL: {}", sql);
        log.info("达梦 SQL: {}", damengSql);
        
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE INDEX"), "应该包含CREATE INDEX，实际结果: " + damengSql);
        // 达梦数据库会按照标准生成索引名：idx_tablename_columnname
        assertTrue(damengSql.contains("idx_users_name") || damengSql.contains("IDX_USERS_NAME"),
                   "应该包含标准索引名，实际结果: " + damengSql);
        assertTrue(damengSql.contains("users") || damengSql.contains("USERS"),
                   "应该包含表名，实际结果: " + damengSql);
        assertTrue(damengSql.contains("name") || damengSql.contains("NAME"),
                   "应该包含列名，实际结果: " + damengSql);
        assertTrue(damengSql.endsWith(";"), "应该以分号结尾，实际结果: " + damengSql);
    }
    
    @Test
    @DisplayName("测试达梦数据库官方文档示例")
    public void testDamengOfficialExample() {
        // 根据达梦数据库官方文档的索引示例
        String sql = "CREATE INDEX idx_employee_dept ON hr.employees(department_id)";
        
        CreateIndex createIndex = parseCreateIndex(sql);
        String damengSql = generator.generate(createIndex);
        
        log.info("达梦官方示例SQL: {}", sql);
        log.info("生成结果: {}", damengSql);
        
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE INDEX"), "应该包含CREATE INDEX，实际结果: " + damengSql);
        // 达梦数据库会按照标准生成索引名：idx_tablename_columnname
        assertTrue(damengSql.contains("idx_employees_department_id") || damengSql.contains("IDX_EMPLOYEES_DEPARTMENT_ID"),
                   "应该包含标准索引名，实际结果: " + damengSql);
        assertTrue(damengSql.contains("hr") || damengSql.contains("HR"),
                   "应该包含schema名，实际结果: " + damengSql);
        assertTrue(damengSql.contains("employees") || damengSql.contains("EMPLOYEES"),
                   "应该包含表名，实际结果: " + damengSql);
        assertTrue(damengSql.contains("department_id") || damengSql.contains("DEPARTMENT_ID"),
                   "应该包含列名，实际结果: " + damengSql);
        assertTrue(damengSql.endsWith(";"), "应该以分号结尾，实际结果: " + damengSql);
        
        log.info("✅ 达梦数据库官方文档示例测试通过！");
    }
}
