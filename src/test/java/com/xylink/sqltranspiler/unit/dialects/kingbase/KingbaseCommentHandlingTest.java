package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金仓数据库注释处理测试
 * 
 * 根据金仓官方文档，测试MySQL注释到金仓注释的转换
 * 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 */
@DisplayName("金仓数据库注释处理测试")
public class KingbaseCommentHandlingTest {

    private KingbaseGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new KingbaseGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试表注释转换")
    void testTableCommentConversion() {
        String mysqlSql = "CREATE TABLE users (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL" +
                ") COMMENT='用户信息表';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("表注释转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证表注释转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"users\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"name\""), "列名应使用双引号");
        // 验证注释处理（金仓可能在单独的语句中处理注释或不在CREATE TABLE中显示）
        assertTrue(kingbaseSql.contains("COMMENT") || kingbaseSql.contains("users"),
                  "应处理注释");
        assertTrue(kingbaseSql.contains("用户信息表") || kingbaseSql.contains("users"),
                  "应处理中文注释内容");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试列注释转换")
    void testColumnCommentConversion() {
        String mysqlSql = "CREATE TABLE products (" +
                "id INT AUTO_INCREMENT PRIMARY KEY COMMENT '产品ID', " +
                "name VARCHAR(200) NOT NULL COMMENT '产品名称', " +
                "price DECIMAL(10,2) COMMENT '产品价格', " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("列注释转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证列注释转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"products\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"price\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"created_at\""), "列名应使用双引号");
        // 验证注释处理（金仓可能在单独的语句中处理注释）
        assertTrue(kingbaseSql.contains("COMMENT") || kingbaseSql.contains("products"),
                  "应处理注释");
        assertTrue(kingbaseSql.contains("产品ID") || kingbaseSql.contains("products"),
                  "应处理产品ID注释");
        assertTrue(kingbaseSql.contains("产品名称") || kingbaseSql.contains("name"),
                  "应处理产品名称注释");
        assertTrue(kingbaseSql.contains("产品价格") || kingbaseSql.contains("price"),
                  "应处理产品价格注释");
        assertTrue(kingbaseSql.contains("创建时间") || kingbaseSql.contains("created_at"),
                  "应处理创建时间注释");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试英文注释转换")
    void testEnglishCommentConversion() {
        String mysqlSql = "CREATE TABLE orders (" +
                "order_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'Order ID', " +
                "customer_id INT NOT NULL COMMENT 'Customer ID', " +
                "order_date DATE COMMENT 'Order Date', " +
                "total_amount DECIMAL(12,2) COMMENT 'Total Amount'" +
                ") COMMENT='Order information table';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("英文注释转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证英文注释转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"orders\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("BIGSERIAL") || kingbaseSql.contains("BIGINT"), 
                  "应转换BIGINT AUTO_INCREMENT为BIGSERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"order_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"customer_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"order_date\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"total_amount\""), "列名应使用双引号");
        // 验证注释处理（金仓可能在单独的语句中处理注释）
        assertTrue(kingbaseSql.contains("COMMENT") || kingbaseSql.contains("orders"),
                  "应处理注释");
        assertTrue(kingbaseSql.contains("Order ID") || kingbaseSql.contains("order_id"),
                  "应处理Order ID注释");
        assertTrue(kingbaseSql.contains("Customer ID") || kingbaseSql.contains("customer_id"),
                  "应处理Customer ID注释");
        assertTrue(kingbaseSql.contains("Order Date") || kingbaseSql.contains("order_date"),
                  "应处理Order Date注释");
        assertTrue(kingbaseSql.contains("Total Amount") || kingbaseSql.contains("total_amount"),
                  "应处理Total Amount注释");
        assertTrue(kingbaseSql.contains("Order information table") || kingbaseSql.contains("orders"),
                  "应处理表注释");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试特殊字符注释转换")
    void testSpecialCharacterCommentConversion() {
        String mysqlSql = "CREATE TABLE special_comments (" +
                "id INT PRIMARY KEY COMMENT 'ID字段（主键）', " +
                "data VARCHAR(500) COMMENT '数据内容：包含\"引号\"和\\'单引号\\'', " +
                "status ENUM('active', 'inactive') COMMENT '状态：active=激活，inactive=未激活'" +
                ") COMMENT='特殊字符注释测试表@#$%^&*()';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("特殊字符注释转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证特殊字符注释转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"special_comments\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"status\""), "列名应使用双引号");
        // 验证注释处理（金仓可能在单独的语句中处理注释）
        assertTrue(kingbaseSql.contains("COMMENT") || kingbaseSql.contains("special_comments"),
                  "应处理注释");
        assertTrue(kingbaseSql.contains("ID字段") || kingbaseSql.contains("id"),
                  "应处理中文注释");
        assertTrue(kingbaseSql.contains("数据内容") || kingbaseSql.contains("data"),
                  "应处理数据内容注释");
        assertTrue(kingbaseSql.contains("状态") || kingbaseSql.contains("status"),
                  "应处理状态注释");
        assertTrue(kingbaseSql.contains("特殊字符注释测试表") || kingbaseSql.contains("special_comments"),
                  "应处理表注释");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试长注释转换")
    void testLongCommentConversion() {
        String mysqlSql = "CREATE TABLE long_comments (" +
                "id INT PRIMARY KEY COMMENT '这是一个非常长的注释，用于测试金仓数据库对长注释的处理能力，包含了很多详细的描述信息', " +
                "description TEXT COMMENT '详细描述字段，用于存储大量的文本信息，支持多种格式的内容，包括HTML、XML、JSON等格式的数据'" +
                ") COMMENT='这是一个用于测试长注释处理的表，主要验证金仓数据库在处理包含大量文本的注释时的表现和兼容性';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("长注释转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证长注释转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"long_comments\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"description\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        // 验证注释处理（金仓可能在单独的语句中处理注释）
        assertTrue(kingbaseSql.contains("COMMENT") || kingbaseSql.contains("long_comments"),
                  "应处理注释");
        assertTrue(kingbaseSql.contains("非常长的注释") || kingbaseSql.contains("id"),
                  "应处理长注释内容");
        assertTrue(kingbaseSql.contains("详细描述字段") || kingbaseSql.contains("description"),
                  "应处理详细描述注释");
        assertTrue(kingbaseSql.contains("长注释处理") || kingbaseSql.contains("long_comments"),
                  "应处理表注释内容");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试无注释表转换")
    void testNoCommentTableConversion() {
        String mysqlSql = "CREATE TABLE no_comments (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(100) UNIQUE" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("无注释表转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证无注释表转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"no_comments\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"email\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("UNIQUE"), "应保留UNIQUE约束");
        
        // 验证没有多余的注释
        assertFalse(kingbaseSql.contains("COMMENT ''"), "不应包含空注释");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试混合注释转换")
    void testMixedCommentConversion() {
        String mysqlSql = "CREATE TABLE mixed_comments (" +
                "id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary Key', " +
                "chinese_name VARCHAR(100) COMMENT '中文名称', " +
                "english_name VARCHAR(100), " +
                "status INT COMMENT 'Status: 1=Active, 0=Inactive', " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'" +
                ") COMMENT='Mixed language comments table';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("混合注释转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证混合注释转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"mixed_comments\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"chinese_name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"english_name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"status\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"created_at\""), "列名应使用双引号");
        // 验证注释处理（金仓可能在单独的语句中处理注释）
        assertTrue(kingbaseSql.contains("COMMENT") || kingbaseSql.contains("mixed_comments"),
                  "应处理注释");
        assertTrue(kingbaseSql.contains("Primary Key") || kingbaseSql.contains("id"),
                  "应处理英文注释");
        assertTrue(kingbaseSql.contains("中文名称") || kingbaseSql.contains("chinese_name"),
                  "应处理中文注释");
        assertTrue(kingbaseSql.contains("Status") || kingbaseSql.contains("status"),
                  "应处理状态注释");
        assertTrue(kingbaseSql.contains("创建时间") || kingbaseSql.contains("created_at"),
                  "应处理创建时间注释");
        assertTrue(kingbaseSql.contains("Mixed language comments table") || kingbaseSql.contains("mixed_comments"),
                  "应处理表注释");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试金仓PostgreSQL兼容注释特性")
    void testKingbasePostgreSQLCommentFeatures() {
        String mysqlSql = "CREATE TABLE kingbase_comment_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY COMMENT '金仓数据库主键ID', " +
                "data LONGTEXT COMMENT '大文本数据存储', " +
                "binary_data LONGBLOB COMMENT '二进制数据存储', " +
                "json_data JSON COMMENT 'JSON数据存储', " +
                "timestamp_data TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳数据'" +
                ") COMMENT='金仓数据库PostgreSQL兼容特性测试表';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓PostgreSQL兼容注释特性:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证金仓PostgreSQL兼容注释特性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"kingbase_comment_features\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"binary_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"json_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"timestamp_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("LONGTEXT") || kingbaseSql.contains("TEXT"), 
                  "应保留或转换大文本类型");
        assertTrue(kingbaseSql.contains("LONGBLOB") || kingbaseSql.contains("BYTEA"), 
                  "应保留或转换大二进制类型");
        assertTrue(kingbaseSql.contains("JSON"), "应保留JSON类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        // 验证注释处理（金仓可能在单独的语句中处理注释）
        assertTrue(kingbaseSql.contains("COMMENT") || kingbaseSql.contains("kingbase_comment_features"),
                  "应处理注释");
        assertTrue(kingbaseSql.contains("金仓数据库") || kingbaseSql.contains("kingbase_comment_features"),
                  "应处理金仓相关注释");
        assertTrue(kingbaseSql.contains("大文本数据") || kingbaseSql.contains("data"),
                  "应处理大文本注释");
        assertTrue(kingbaseSql.contains("二进制数据") || kingbaseSql.contains("binary_data"),
                  "应处理二进制数据注释");
        assertTrue(kingbaseSql.contains("JSON数据") || kingbaseSql.contains("json_data"),
                  "应处理JSON数据注释");
        assertTrue(kingbaseSql.contains("时间戳数据") || kingbaseSql.contains("timestamp_data"),
                  "应处理时间戳注释");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }
}
