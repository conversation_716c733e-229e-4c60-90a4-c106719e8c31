package com.xylink.sqltranspiler.unit.dialects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.dialects.DamengDialect;
import com.xylink.sqltranspiler.core.dialects.SqlDialect;

/**
 * SQL方言抽象测试
 * 验证借鉴Apache Calcite设计思想的方言抽象是否正常工作
 */
public class SqlDialectTest {
    
    @Test
    public void testDamengDialectBasicFunctionality() {
        SqlDialect dialect = new DamengDialect();
        
        // 测试基本信息
        assertEquals("DM", dialect.getName());
        assertEquals("DM Database", dialect.getDatabaseProduct());
        
        // 测试标识符引用
        assertEquals("normal_table", dialect.quoteIdentifier("normal_table"));
        assertEquals("\"SELECT\"", dialect.quoteIdentifier("SELECT")); // 保留字需要引用
        
        // 测试字符串引用
        assertEquals("'test'", dialect.quoteLiteral("test"));
        assertEquals("NULL", dialect.quoteLiteral(null));
        
        // 测试数据类型映射
        assertEquals("CLOB", dialect.mapDataType("TEXT", null, null, null));
        assertEquals("VARCHAR(255)", dialect.mapDataType("VARCHAR", 255, null, null));
        assertEquals("DECIMAL(10,2)", dialect.mapDataType("DECIMAL", null, 10, 2));
        
        // 测试分页支持
        assertFalse(dialect.supportsLimit()); // 达梦不直接支持LIMIT
        assertFalse(dialect.supportsOffset()); // 达梦不直接支持OFFSET
        assertEquals("WHERE ROWNUM <= 10", dialect.formatPagination(10, null));
        assertEquals("WHERE ROWNUM > 5 AND ROWNUM <= 15", dialect.formatPagination(10, 5));
        
        // 测试自增支持
        assertTrue(dialect.supportsAutoIncrement());
        assertEquals("IDENTITY(1,1)", dialect.getAutoIncrementSyntax());
        
        // 测试函数映射
        assertEquals("SYSDATE", dialect.mapFunction("NOW"));
        assertEquals("CONCAT(a, b)", dialect.mapFunction("CONCAT", "a", "b"));
        assertEquals("LENGTH(str)", dialect.mapFunction("LENGTH", "str"));
        
        // 测试约束支持
        assertTrue(dialect.supportsForeignKey());
        assertTrue(dialect.supportsCheckConstraint());
        assertEquals("PRIMARY KEY (id)", dialect.formatPrimaryKey("id"));
        assertEquals("UNIQUE (email, phone)", dialect.formatUniqueConstraint("email", "phone"));
        
        // 测试默认方法
        assertEquals("SYSDATE", dialect.getCurrentTimestampFunction());
        assertEquals("TRUNC(SYSDATE)", dialect.getCurrentDateFunction());
        assertEquals("||", dialect.getConcatOperator());
        assertFalse(dialect.isCaseSensitive());
    }
    
    @Test
    public void testDataTypeMappingEdgeCases() {
        SqlDialect dialect = new DamengDialect();
        
        // 测试null输入
        assertEquals("VARCHAR(255)", dialect.mapDataType(null, null, null, null));
        
        // 测试未知类型
        assertEquals("UNKNOWN_TYPE", dialect.mapDataType("UNKNOWN_TYPE", null, null, null));
        
        // 测试大小写不敏感
        assertEquals("CLOB", dialect.mapDataType("text", null, null, null));
        assertEquals("CLOB", dialect.mapDataType("TEXT", null, null, null));
        assertEquals("CLOB", dialect.mapDataType("Text", null, null, null));
    }
    
    @Test
    public void testFunctionMappingEdgeCases() {
        SqlDialect dialect = new DamengDialect();
        
        // 测试null输入
        assertEquals("", dialect.mapFunction(null));
        
        // 测试无参数函数
        assertEquals("SYSDATE", dialect.mapFunction("NOW"));
        
        // 测试多参数函数
        assertEquals("CONCAT(a, b, c)", dialect.mapFunction("CONCAT", "a", "b", "c"));
        
        // 测试未映射的函数
        assertEquals("CUSTOM_FUNC(arg1)", dialect.mapFunction("CUSTOM_FUNC", "arg1"));
        
        // 测试函数支持检查
        assertTrue(dialect.supportsFunction("NOW"));
        assertTrue(dialect.supportsFunction("CONCAT"));
        assertFalse(dialect.supportsFunction("UNKNOWN_FUNC"));
    }
    
    @Test
    public void testIdentifierQuotingRules() {
        SqlDialect dialect = new DamengDialect();
        
        // 普通标识符不需要引用
        assertFalse(dialect.requiresQuoting("normal_table"));
        assertFalse(dialect.requiresQuoting("user_id"));
        assertFalse(dialect.requiresQuoting("table123"));
        
        // 保留字需要引用
        assertTrue(dialect.requiresQuoting("SELECT"));
        assertTrue(dialect.requiresQuoting("FROM"));
        assertTrue(dialect.requiresQuoting("WHERE"));
        
        // 特殊字符需要引用
        assertTrue(dialect.requiresQuoting("table-name"));
        assertTrue(dialect.requiresQuoting("table name"));
        assertTrue(dialect.requiresQuoting("table.name"));
        
        // 以数字开头需要引用
        assertTrue(dialect.requiresQuoting("123table"));
        
        // 空字符串和null不需要引用
        assertFalse(dialect.requiresQuoting(""));
        assertFalse(dialect.requiresQuoting(null));
    }
    
    @Test
    public void testPaginationFormatting() {
        SqlDialect dialect = new DamengDialect();
        
        // 只有LIMIT
        assertEquals("WHERE ROWNUM <= 10", dialect.formatPagination(10, null));
        assertEquals("WHERE ROWNUM <= 100", dialect.formatPagination(100, null));
        
        // LIMIT + OFFSET
        assertEquals("WHERE ROWNUM > 10 AND ROWNUM <= 20", dialect.formatPagination(10, 10));
        assertEquals("WHERE ROWNUM > 50 AND ROWNUM <= 100", dialect.formatPagination(50, 50));
        
        // 边界情况
        assertEquals("", dialect.formatPagination(null, null));
        assertEquals("", dialect.formatPagination(null, 10));
        assertEquals("WHERE ROWNUM <= 0", dialect.formatPagination(0, null));
        assertEquals("WHERE ROWNUM <= 0", dialect.formatPagination(0, 0)); // offset=0被当作无偏移量
    }
    
    @Test
    public void testConstraintFormatting() {
        SqlDialect dialect = new DamengDialect();
        
        // 主键约束
        assertEquals("PRIMARY KEY (id)", dialect.formatPrimaryKey("id"));
        assertEquals("PRIMARY KEY (user_id, tenant_id)", dialect.formatPrimaryKey("user_id", "tenant_id"));
        assertEquals("", dialect.formatPrimaryKey()); // 空参数
        
        // 唯一约束
        assertEquals("UNIQUE (email)", dialect.formatUniqueConstraint("email"));
        assertEquals("UNIQUE (email, phone)", dialect.formatUniqueConstraint("email", "phone"));
        assertEquals("", dialect.formatUniqueConstraint()); // 空参数
    }
}
