package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 达梦数据库注释处理测试
 * 
 * 根据达梦官方文档，测试MySQL注释到达梦注释的转换
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 */
@DisplayName("达梦数据库注释处理测试")
public class DamengCommentHandlingTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试表注释转换")
    void testTableCommentConversion() {
        String mysqlSql = "CREATE TABLE users (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL" +
                ") COMMENT='用户信息表';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("表注释转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证表注释转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("users"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("COMMENT"), "应保留注释");
        assertTrue(damengSql.contains("用户信息表"), "应保留中文注释内容");
    }

    @Test
    @DisplayName("测试列注释转换")
    void testColumnCommentConversion() {
        String mysqlSql = "CREATE TABLE products (" +
                "id INT AUTO_INCREMENT PRIMARY KEY COMMENT '产品ID', " +
                "name VARCHAR(200) NOT NULL COMMENT '产品名称', " +
                "price DECIMAL(10,2) COMMENT '产品价格', " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("列注释转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证列注释转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("products"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("COMMENT"), "应保留注释");
        assertTrue(damengSql.contains("产品ID"), "应保留产品ID注释");
        assertTrue(damengSql.contains("产品名称"), "应保留产品名称注释");
        assertTrue(damengSql.contains("产品价格"), "应保留产品价格注释");
        assertTrue(damengSql.contains("创建时间"), "应保留创建时间注释");
    }

    @Test
    @DisplayName("测试英文注释转换")
    void testEnglishCommentConversion() {
        String mysqlSql = "CREATE TABLE orders (" +
                "order_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'Order ID', " +
                "customer_id INT NOT NULL COMMENT 'Customer ID', " +
                "order_date DATE COMMENT 'Order Date', " +
                "total_amount DECIMAL(12,2) COMMENT 'Total Amount'" +
                ") COMMENT='Order information table';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("英文注释转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证英文注释转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("orders"), "应保留表名");
        assertTrue(damengSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("COMMENT"), "应保留注释");
        assertTrue(damengSql.contains("Order ID"), "应保留Order ID注释");
        assertTrue(damengSql.contains("Customer ID"), "应保留Customer ID注释");
        assertTrue(damengSql.contains("Order Date"), "应保留Order Date注释");
        assertTrue(damengSql.contains("Total Amount"), "应保留Total Amount注释");
        assertTrue(damengSql.contains("Order information table"), "应保留表注释");
    }

    @Test
    @DisplayName("测试特殊字符注释转换")
    void testSpecialCharacterCommentConversion() {
        String mysqlSql = "CREATE TABLE special_comments (" +
                "id INT PRIMARY KEY COMMENT 'ID字段（主键）', " +
                "data VARCHAR(500) COMMENT '数据内容：包含\"引号\"和\\'单引号\\'', " +
                "status ENUM('active', 'inactive') COMMENT '状态：active=激活，inactive=未激活'" +
                ") COMMENT='特殊字符注释测试表@#$%^&*()';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("特殊字符注释转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证特殊字符注释转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("special_comments"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("COMMENT"), "应保留注释");
        assertTrue(damengSql.contains("ID字段"), "应保留中文注释");
        assertTrue(damengSql.contains("数据内容"), "应保留数据内容注释");
        assertTrue(damengSql.contains("状态"), "应保留状态注释");
        assertTrue(damengSql.contains("特殊字符注释测试表"), "应保留表注释");
    }

    @Test
    @DisplayName("测试长注释转换")
    void testLongCommentConversion() {
        String mysqlSql = "CREATE TABLE long_comments (" +
                "id INT PRIMARY KEY COMMENT '这是一个非常长的注释，用于测试达梦数据库对长注释的处理能力，包含了很多详细的描述信息', " +
                "description TEXT COMMENT '详细描述字段，用于存储大量的文本信息，支持多种格式的内容，包括HTML、XML、JSON等格式的数据'" +
                ") COMMENT='这是一个用于测试长注释处理的表，主要验证达梦数据库在处理包含大量文本的注释时的表现和兼容性';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("长注释转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证长注释转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("long_comments"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        assertTrue(damengSql.contains("COMMENT"), "应保留注释");
        assertTrue(damengSql.contains("非常长的注释"), "应保留长注释内容");
        assertTrue(damengSql.contains("详细描述字段"), "应保留详细描述注释");
        assertTrue(damengSql.contains("长注释处理"), "应保留表注释内容");
    }

    @Test
    @DisplayName("测试无注释表转换")
    void testNoCommentTableConversion() {
        String mysqlSql = "CREATE TABLE no_comments (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(100) UNIQUE" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("无注释表转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证无注释表转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("no_comments"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("UNIQUE"), "应保留UNIQUE约束");
        
        // 验证没有多余的注释
        assertFalse(damengSql.contains("COMMENT ''"), "不应包含空注释");
    }

    @Test
    @DisplayName("测试混合注释转换")
    void testMixedCommentConversion() {
        String mysqlSql = "CREATE TABLE mixed_comments (" +
                "id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary Key', " +
                "chinese_name VARCHAR(100) COMMENT '中文名称', " +
                "english_name VARCHAR(100), " +
                "status INT COMMENT 'Status: 1=Active, 0=Inactive', " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'" +
                ") COMMENT='Mixed language comments table';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("混合注释转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证混合注释转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("mixed_comments"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("COMMENT"), "应保留注释");
        assertTrue(damengSql.contains("Primary Key"), "应保留英文注释");
        assertTrue(damengSql.contains("中文名称"), "应保留中文注释");
        assertTrue(damengSql.contains("Status"), "应保留状态注释");
        assertTrue(damengSql.contains("创建时间"), "应保留创建时间注释");
        assertTrue(damengSql.contains("Mixed language comments table"), "应保留表注释");
    }

    @Test
    @DisplayName("测试达梦特有注释特性")
    void testDamengSpecificCommentFeatures() {
        String mysqlSql = "CREATE TABLE dameng_comment_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY COMMENT '达梦数据库主键ID', " +
                "data LONGTEXT COMMENT '大文本数据存储', " +
                "binary_data LONGBLOB COMMENT '二进制数据存储', " +
                "numeric_data DECIMAL(20,6) COMMENT '高精度数值数据', " +
                "timestamp_data TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳数据'" +
                ") COMMENT='达梦数据库特有特性测试表';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦特有注释特性:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证达梦特有注释特性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("dameng_comment_features"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("LONGTEXT"), 
                  "应转换或保留大文本类型");
        assertTrue(damengSql.contains("BLOB") || damengSql.contains("LONGBLOB"), 
                  "应转换或保留大二进制类型");
        assertTrue(damengSql.contains("DECIMAL(20,6)"), "应保留高精度数值类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("COMMENT"), "应保留注释");
        assertTrue(damengSql.contains("达梦数据库"), "应保留达梦相关注释");
        assertTrue(damengSql.contains("大文本数据"), "应保留大文本注释");
        assertTrue(damengSql.contains("二进制数据"), "应保留二进制数据注释");
        assertTrue(damengSql.contains("高精度数值"), "应保留高精度数值注释");
        assertTrue(damengSql.contains("时间戳数据"), "应保留时间戳注释");
    }
}
