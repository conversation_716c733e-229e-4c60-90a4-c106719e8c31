package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库数据类型转换测试
 * 基于神通数据库官方文档 shentong.md 第2.9节数据类型规范
 */
public class ShentongDataTypeTest extends BaseShentongConversionTest {

    /**
     * 测试字符串类型转换
     * 根据文档2.9.1节：CHARACTER(n), VARCHAR(n), TEXT, "CHAR", NAME
     */
    @Test
    public void testStringDataTypes() throws Exception {
        // 使用正确的MySQL语法（根据 .augment/rules/rule-db.md 要求）
        // MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/string-type-syntax.html
        String mysqlSql = """
            CREATE TABLE string_types_test (
                fixed_char CHARACTER(10),
                var_char VARCHAR(255),
                char_alias CHAR(5),
                varchar_alias CHARACTER VARYING(100),
                nvarchar_col VARCHAR(50),
                text_col TEXT
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证官方文档合规性
        assertOfficialDocumentCompliance(shentongSql);

        // 验证字符串类型支持（基于官方文档第2.9.1节）
        assertTrue(shentongSql.contains("CHARACTER(10)"), "应支持CHARACTER(n)类型");
        assertTrue(shentongSql.contains("VARCHAR(255)"), "应支持VARCHAR(n)类型");
        assertTrue(shentongSql.contains("CHAR(5)"), "应支持CHAR(n)别名");
        assertTrue(shentongSql.contains("CHARACTER VARYING(100)") ||
                   shentongSql.contains("VARCHAR(100)"), "应支持CHARACTER VARYING(n)类型");
        assertTrue(shentongSql.contains("VARCHAR(50)"), "NVARCHAR2应映射为VARCHAR");
        assertTrue(shentongSql.contains("TEXT"), "应支持TEXT类型");
    }

    /**
     * 测试位串类型转换
     * 根据文档2.9.2节：BIT(n), BIT VARYING(n), VARBIT(n)
     */
    @Test
    public void testBitStringDataTypes() throws Exception {
        String mysqlSql = """
            CREATE TABLE bit_types_test (
                fixed_bit BIT(8)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证官方文档合规性
        assertOfficialDocumentCompliance(shentongSql);

        // 验证位串类型支持（基于官方文档第2.9.2节）
        assertTrue(shentongSql.contains("BIT(8)"), "应支持BIT(n)类型");
    }

    /**
     * 测试数值类型转换
     * 根据文档2.9.3节：TINYINT, SMALLINT, INT, BIGINT, DECIMAL, NUMERIC, REAL, DOUBLE PRECISION, FLOAT
     * 使用正确的MySQL语法（根据 .augment/rules/rule-db.md 要求）
     */
    @Test
    public void testNumericDataTypes() throws Exception {
        // 使用正确的MySQL语法，SERIAL和BIGSERIAL是PostgreSQL语法
        // MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/numeric-types.html
        String mysqlSql = """
            CREATE TABLE numeric_types_test (
                tiny_int TINYINT,
                small_int SMALLINT,
                int_col INT,
                integer_col INTEGER,
                big_int BIGINT,
                decimal_col DECIMAL(10,2),
                numeric_col NUMERIC(15,5),
                real_col REAL,
                double_col DOUBLE PRECISION,
                float_col FLOAT(24),
                float_high FLOAT(53),
                auto_int_col INT AUTO_INCREMENT,
                auto_bigint_col BIGINT AUTO_INCREMENT
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证官方文档合规性
        assertOfficialDocumentCompliance(shentongSql);

        // 验证数值类型支持（基于官方文档第2.9.3节）
        assertTrue(shentongSql.contains("TINYINT"), "应支持TINYINT类型");
        assertTrue(shentongSql.contains("SMALLINT"), "应支持SMALLINT类型");
        assertTrue(shentongSql.contains("INT") || shentongSql.contains("INTEGER"), "应支持INT/INTEGER类型");
        assertTrue(shentongSql.contains("BIGINT"), "应支持BIGINT类型");
        assertTrue(shentongSql.contains("DECIMAL(10,2)"), "应支持DECIMAL(p,s)类型");
        assertTrue(shentongSql.contains("NUMERIC(15,5)"), "应支持NUMERIC(p,s)类型");
        assertTrue(shentongSql.contains("REAL"), "应支持REAL类型");
        assertTrue(shentongSql.contains("DOUBLE PRECISION"), "应支持DOUBLE PRECISION类型");
        assertTrue(shentongSql.contains("FLOAT") || shentongSql.contains("REAL"), "应支持FLOAT或REAL类型");
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应支持AUTO_INCREMENT类型（MySQL标准语法）");
    }

    /**
     * 测试自增列类型转换
     * 根据文档2.9.4节：AUTO_INCREMENT支持
     */
    @Test
    public void testAutoIncrementDataTypes() throws Exception {
        String mysqlSql = """
            CREATE TABLE auto_increment_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                bigint_auto BIGINT AUTO_INCREMENT UNIQUE,
                float_auto FLOAT AUTO_INCREMENT
            ) AUTO_INCREMENT = 100;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证自增列类型支持
        assertTrue(shentongSql.contains("AUTO_INCREMENT") || shentongSql.contains("SERIAL"),
                   "应支持AUTO_INCREMENT属性或SERIAL类型");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持自增列的PRIMARY KEY约束");
        assertTrue(shentongSql.contains("UNIQUE"), "应支持自增列的UNIQUE约束");
        // 根据神通官方文档：神通完全支持AUTO_INCREMENT语法
        assertTrue((shentongSql.contains("AUTO_INCREMENT") || shentongSql.contains("SERIAL")) &&
                   shentongSql.contains("FLOAT"), "应支持AUTO_INCREMENT或SERIAL、BIGSERIAL、FLOAT的自增列");
    }

    /**
     * 测试二进制字符串类型转换
     * 根据文档2.9.5节：BINARY(n), VARBINARY(n), RAW(n)
     */
    @Test
    public void testBinaryStringDataTypes() throws Exception {
        String mysqlSql = """
            CREATE TABLE binary_types_test (
                fixed_binary BINARY(16),
                var_binary VARBINARY(255)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证二进制字符串类型支持
        assertTrue(shentongSql.contains("BINARY(16)"), "应支持BINARY(n)类型并保持精度");
        assertTrue(shentongSql.contains("VARBINARY(255)"), "应支持VARBINARY(n)类型并保持精度");
    }

    /**
     * 测试日期时间类型转换
     * 根据文档2.9.6节：DATE, TIME, TIMESTAMP, TIME WITH TIME ZONE
     */
    @Test
    public void testDateTimeDataTypes() throws Exception {
        String mysqlSql = """
            CREATE TABLE datetime_types_test (
                date_col DATE,
                time_col TIME,
                time_precision TIME(6),
                timestamp_col TIMESTAMP,
                datetime_col DATETIME
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证日期时间类型支持
        assertTrue(shentongSql.contains("DATE"), "应支持DATE类型");
        assertTrue(shentongSql.contains("TIME"), "应支持TIME类型");
        assertTrue(shentongSql.contains("TIME(6)"), "应支持TIME(p)精度");
        assertTrue(shentongSql.contains("TIMESTAMP"), "应支持TIMESTAMP类型");
        // DATETIME在神通中转换为TIMESTAMP
        assertTrue(shentongSql.contains("TIMESTAMP"), "DATETIME应转换为TIMESTAMP类型");
    }

    /**
     * 测试大对象类型转换
     * 根据文档：BLOB, CLOB等大对象类型
     */
    @Test
    public void testLargeObjectDataTypes() throws Exception {
        String mysqlSql = """
            CREATE TABLE lob_types_test (
                blob_col BLOB,
                text_lob TEXT,
                longtext_col LONGTEXT,
                mediumtext_col MEDIUMTEXT
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证大对象类型支持
        assertTrue(shentongSql.contains("BLOB"), "应支持BLOB类型");
        assertTrue(shentongSql.contains("TEXT"), "应支持TEXT类型");
        // LONGTEXT和MEDIUMTEXT可能转换为TEXT
        assertTrue(shentongSql.contains("TEXT") || shentongSql.contains("LONGTEXT"), 
                   "应支持长文本类型转换");
    }

    /**
     * 测试数据类型精度和范围
     * 根据文档：验证各类型的精度限制
     */
    @Test
    public void testDataTypePrecisionAndRange() throws Exception {
        String mysqlSql = """
            CREATE TABLE precision_test (
                char_max CHARACTER(8000),
                varchar_max VARCHAR(8000),
                decimal_max DECIMAL(1000,500),
                numeric_max NUMERIC(1000,500),
                binary_max BINARY(8000),
                varbinary_max VARBINARY(8000)
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证精度支持
        assertTrue(shentongSql.contains("CHARACTER(8000)"), "应支持CHARACTER最大长度8000");
        assertTrue(shentongSql.contains("VARCHAR(8000)"), "应支持VARCHAR最大长度8000");
        assertTrue(shentongSql.contains("DECIMAL(1000,500)"), "应支持DECIMAL最大精度1000");
        assertTrue(shentongSql.contains("NUMERIC(1000,500)"), "应支持NUMERIC最大精度1000");
        assertTrue(shentongSql.contains("BINARY(8000)"), "应支持BINARY最大长度8000");
        assertTrue(shentongSql.contains("VARBINARY(8000)"), "应支持VARBINARY最大长度8000");
    }

    /**
     * 测试非MySQL语法的错误检测
     * 根据 .augment/rules/rule-db.md 要求，MySQL强制语法校验应该拒绝非MySQL语法
     */
    @Test
    public void testNonMySQLSyntaxDetection() throws Exception {
        // 测试Oracle语法NVARCHAR2应该被拒绝
        String oracleSql = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                nvarchar_col NVARCHAR2(50)
            );
            """;

        // MySQL强制语法校验应该拒绝NVARCHAR2语法，返回空结果
        String result = convertMySqlToShentong(oracleSql);

        // 验证转换结果为空，表示语法验证失败
        assertTrue(result == null || result.trim().isEmpty(),
                  "NVARCHAR2语法应该被MySQL强制语法校验拒绝，返回空结果");
    }

    /**
     * 测试PostgreSQL SERIAL语法的错误检测
     * 根据 .augment/rules/rule-db.md 要求，MySQL强制语法校验应该拒绝PostgreSQL语法
     */
    @Test
    public void testPostgreSQLSerialSyntaxDetection() throws Exception {
        // 测试PostgreSQL语法SERIAL应该被拒绝
        String postgresqlSql = """
            CREATE TABLE test_table (
                id SERIAL PRIMARY KEY,
                bigid BIGSERIAL
            );
            """;

        // MySQL强制语法校验应该拒绝SERIAL语法，返回空结果
        String result = convertMySqlToShentong(postgresqlSql);

        // 验证转换结果为空，表示语法验证失败
        assertTrue(result == null || result.trim().isEmpty(),
                  "SERIAL和BIGSERIAL语法应该被MySQL强制语法校验拒绝，返回空结果");
    }
}
