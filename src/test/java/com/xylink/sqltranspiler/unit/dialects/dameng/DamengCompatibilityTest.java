package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 达梦数据库兼容性测试
 * 
 * 根据达梦官方文档，测试MySQL语法到达梦的兼容性转换
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 */
@DisplayName("达梦数据库兼容性测试")
public class DamengCompatibilityTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试MySQL数据类型兼容性")
    void testMySQLDataTypeCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_types (" +
                "tinyint_col TINYINT, " +
                "smallint_col SMALLINT, " +
                "mediumint_col MEDIUMINT, " +
                "int_col INT, " +
                "bigint_col BIGINT, " +
                "float_col FLOAT, " +
                "double_col DOUBLE, " +
                "decimal_col DECIMAL(10,2), " +
                "varchar_col VARCHAR(255), " +
                "text_col TEXT, " +
                "date_col DATE, " +
                "datetime_col DATETIME, " +
                "timestamp_col TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("MySQL数据类型兼容性转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证MySQL数据类型兼容性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("mysql_types"), "应保留表名");
        assertTrue(damengSql.contains("TINYINT"), "应保留TINYINT类型");
        assertTrue(damengSql.contains("SMALLINT"), "应保留SMALLINT类型");
        assertTrue(damengSql.contains("INT"), "应保留INT类型");
        assertTrue(damengSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(damengSql.contains("FLOAT"), "应保留FLOAT类型");
        assertTrue(damengSql.contains("DOUBLE"), "应保留DOUBLE类型");
        assertTrue(damengSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        assertTrue(damengSql.contains("VARCHAR(255)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        assertTrue(damengSql.contains("DATE"), "应保留DATE类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应转换DATETIME为TIMESTAMP或保留DATETIME");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
    }

    @Test
    @DisplayName("测试MySQL约束兼容性")
    void testMySQLConstraintCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_constraints (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "email VARCHAR(100) NOT NULL UNIQUE, " +
                "age INT CHECK (age >= 0), " +
                "status ENUM('active', 'inactive') DEFAULT 'active', " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("MySQL约束兼容性转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证MySQL约束兼容性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("mysql_constraints"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键约束");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(damengSql.contains("CHECK") || damengSql.contains("age"), 
                  "应处理CHECK约束");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
    }

    @Test
    @DisplayName("测试MySQL函数兼容性")
    void testMySQLFunctionCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_functions (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "uuid_col VARCHAR(36) DEFAULT (UUID()), " +
                "random_col DOUBLE DEFAULT (RAND())" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("MySQL函数兼容性转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证MySQL函数兼容性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("mysql_functions"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证时间函数转换
        assertTrue(damengSql.contains("CURRENT_TIMESTAMP") || damengSql.contains("SYSDATE"), 
                  "应转换时间函数");
        assertTrue(damengSql.contains("VARCHAR(36)"), "应保留UUID列类型");
        assertTrue(damengSql.contains("DOUBLE"), "应保留DOUBLE类型");
    }

    @Test
    @DisplayName("测试MySQL存储引擎兼容性")
    void testMySQLStorageEngineCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_engines (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "data VARCHAR(100)" +
                ") ENGINE=InnoDB " +
                "AUTO_INCREMENT=1000 " +
                "DEFAULT CHARSET=utf8mb4 " +
                "COLLATE=utf8mb4_unicode_ci " +
                "COMMENT='MySQL存储引擎测试表';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("MySQL存储引擎兼容性转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证MySQL存储引擎兼容性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("mysql_engines"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留数据类型");
        assertTrue(damengSql.contains("COMMENT"), "应保留注释");
        assertTrue(damengSql.contains("MySQL存储引擎测试表"), "应保留注释内容");
        
        // 验证MySQL特有选项的处理（达梦可能忽略或转换）
        assertFalse(damengSql.contains("ENGINE=InnoDB"), "不应包含MySQL特有的ENGINE选项");
        assertFalse(damengSql.contains("utf8mb4"), "不应包含MySQL特有的字符集");
    }

    @Test
    @DisplayName("测试MySQL索引兼容性")
    void testMySQLIndexCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_indexes (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(100) UNIQUE, " +
                "content TEXT, " +
                "INDEX idx_name (name), " +
                "INDEX idx_name_email (name, email), " +
                "FULLTEXT INDEX ft_content (content)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("MySQL索引兼容性转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证MySQL索引兼容性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("mysql_indexes"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        
        // 验证索引处理（达梦可能在单独的语句中处理索引）
        assertTrue(damengSql.contains("idx_name") || damengSql.contains("name"), 
                  "应处理name索引");
        assertTrue(damengSql.contains("idx_name_email") || damengSql.contains("email"), 
                  "应处理复合索引");
    }

    @Test
    @DisplayName("测试MySQL分区表兼容性")
    void testMySQLPartitionCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_partitions (" +
                "id INT AUTO_INCREMENT, " +
                "order_date DATE NOT NULL, " +
                "amount DECIMAL(10,2), " +
                "PRIMARY KEY (id, order_date)" +
                ") PARTITION BY RANGE (YEAR(order_date)) (" +
                "PARTITION p2020 VALUES LESS THAN (2021), " +
                "PARTITION p2021 VALUES LESS THAN (2022), " +
                "PARTITION p2022 VALUES LESS THAN (2023)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("MySQL分区表兼容性转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证MySQL分区表兼容性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("mysql_partitions"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("DATE"), "应保留DATE类型");
        assertTrue(damengSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        assertTrue(damengSql.contains("order_date"), "应保留分区键");
        
        // 验证分区处理（达梦支持分区表）
        assertTrue(damengSql.contains("PARTITION") || damengSql.contains("mysql_partitions"), 
                  "应处理分区定义");
    }

    @Test
    @DisplayName("测试MySQL字符集兼容性")
    void testMySQLCharsetCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_charset (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "utf8_col VARCHAR(100) CHARACTER SET utf8, " +
                "utf8mb4_col VARCHAR(100) CHARACTER SET utf8mb4, " +
                "latin1_col VARCHAR(100) CHARACTER SET latin1, " +
                "binary_col VARCHAR(100) CHARACTER SET binary" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("MySQL字符集兼容性转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证MySQL字符集兼容性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("mysql_charset"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("utf8_col"), "应保留列名");
        assertTrue(damengSql.contains("utf8mb4_col"), "应保留列名");
        assertTrue(damengSql.contains("latin1_col"), "应保留列名");
        assertTrue(damengSql.contains("binary_col"), "应保留列名");
        
        // 验证字符集处理（达梦可能移除MySQL特有的字符集声明）
        assertFalse(damengSql.contains("CHARACTER SET utf8mb4"), 
                   "不应包含MySQL特有的字符集声明");
        assertFalse(damengSql.contains("ENGINE=InnoDB"), 
                   "不应包含MySQL特有的存储引擎");
    }

    @Test
    @DisplayName("测试达梦特有兼容性特性")
    void testDamengSpecificCompatibilityFeatures() {
        String mysqlSql = "CREATE TABLE dameng_compatibility (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "oracle_number NUMBER(10,2), " +
                "oracle_varchar2 VARCHAR2(100), " +
                "oracle_date DATE, " +
                "oracle_timestamp TIMESTAMP, " +
                "mysql_text TEXT, " +
                "mysql_blob BLOB" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦特有兼容性特性:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证达梦特有兼容性特性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("dameng_compatibility"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        
        // 验证Oracle兼容类型处理
        assertTrue(damengSql.contains("oracle_number"), "应保留oracle_number列名");
        assertTrue(damengSql.contains("oracle_varchar2"), "应保留oracle_varchar2列名");
        assertTrue(damengSql.contains("oracle_date"), "应保留oracle_date列名");
        assertTrue(damengSql.contains("oracle_timestamp"), "应保留oracle_timestamp列名");
        
        // 验证MySQL类型转换
        assertTrue(damengSql.contains("mysql_text"), "应保留mysql_text列名");
        assertTrue(damengSql.contains("mysql_blob"), "应保留mysql_blob列名");
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        assertTrue(damengSql.contains("BLOB"), "应保留BLOB类型");
    }

    @Test
    @DisplayName("测试复杂兼容性场景")
    void testComplexCompatibilityScenario() {
        String mysqlSql = "CREATE TABLE complex_compatibility (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "user_data JSON, " +
                "file_content LONGBLOB, " +
                "search_text FULLTEXT, " +
                "geo_point POINT, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "status ENUM('draft', 'published', 'archived') DEFAULT 'draft'" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("复杂兼容性场景转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证复杂兼容性场景
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("complex_compatibility"), "应保留表名");
        assertTrue(damengSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("user_data"), "应保留user_data列名");
        assertTrue(damengSql.contains("file_content"), "应保留file_content列名");
        assertTrue(damengSql.contains("search_text"), "应保留search_text列名");
        assertTrue(damengSql.contains("created_at"), "应保留created_at列名");
        assertTrue(damengSql.contains("updated_at"), "应保留updated_at列名");
        assertTrue(damengSql.contains("status"), "应保留status列名");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证复杂类型处理
        assertTrue(damengSql.contains("JSON") || damengSql.contains("CLOB"), 
                  "应处理JSON类型");
        assertTrue(damengSql.contains("BLOB"), "应转换或保留BLOB类型");
        
        // 验证MySQL特有选项的移除
        assertFalse(damengSql.contains("ENGINE=InnoDB"), 
                   "不应包含MySQL特有的存储引擎");
        assertFalse(damengSql.contains("utf8mb4_unicode_ci"), 
                   "不应包含MySQL特有的排序规则");
    }
}
