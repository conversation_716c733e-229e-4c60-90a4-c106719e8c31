package com.xylink.sqltranspiler.unit.dialects.dameng;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseConversionTest;

/**
 * 达梦字符集和编码问题测试
 * 
 * 基于达梦官方文档中的字符集、编码和长度相关问题
 * 验证MySQL到DM的字符集转换和长度处理
 */
@DisplayName("达梦字符集和编码问题测试")
public class DamengCharsetAndEncodingTest extends BaseConversionTest {

    @Test
    @DisplayName("UTF8MB4字符集不支持问题")
    void testUtf8mb4CharsetNotSupported() throws Exception {
        // DM不支持UTF8MB4字符集，需要转换为UTF8
        String mysqlSql = """
            CREATE TABLE utf8mb4_test (
                id INT NOT NULL AUTO_INCREMENT,
                emoji_content VARCHAR(255),
                chinese_text TEXT,
                mixed_content LONGTEXT,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            """;
        
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证字符集转换 - 达梦不支持表级字符集
        assertFalse(damengSql.contains("CHARACTER SET"),
                   "达梦不支持表级字符集，应移除CHARACTER SET");
        assertFalse(damengSql.contains("CHARSET=utf8mb4") || damengSql.contains("CHARACTER SET utf8mb4"),
                    "不应包含不支持的utf8mb4字符集定义");
        assertFalse(damengSql.contains("COLLATE"),
                    "COLLATE排序规则应被移除");
        assertFalse(damengSql.contains("unicode_ci"),
                    "排序规则应被移除");
        assertFalse(damengSql.contains("ENGINE="),
                    "ENGINE选项应被移除");
    }

    @Test
    @DisplayName("CHAR长度扩大3倍问题")
    void testCharLengthTripleExpansion() throws Exception {
        // 老版本DTS工具为保证数据成功迁移，会为不同字符集的目标库中char字段自动扩大3倍
        String mysqlSql = """
            CREATE TABLE char_expansion_test (
                id INT NOT NULL AUTO_INCREMENT,
                short_code CHAR(5),
                medium_code CHAR(10),
                long_code CHAR(20),
                description VARCHAR(100),
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证CHAR类型转换（不应该自动扩大3倍）
        assertTrue((damengSql.toUpperCase().contains("SHORT_CODE") || damengSql.contains("short_code")) &&
                   damengSql.contains("CHAR(5)"),
                   "CHAR(5)应保持原长度");
        assertTrue((damengSql.toUpperCase().contains("MEDIUM_CODE") || damengSql.contains("medium_code")) &&
                   damengSql.contains("CHAR(10)"),
                   "CHAR(10)应保持原长度");
        assertTrue((damengSql.toUpperCase().contains("LONG_CODE") || damengSql.contains("long_code")) &&
                   damengSql.contains("CHAR(20)"),
                   "CHAR(20)应保持原长度");
        
        // 验证字符集转换 - 达梦不支持表级字符集
        assertFalse(damengSql.contains("CHARACTER SET"),
                   "达梦不支持表级字符集，应移除CHARACTER SET");
        assertFalse(damengSql.contains("ENGINE="),
                   "ENGINE选项应被移除");
    }

    @Test
    @DisplayName("VARCHAR字符vs字节长度问题")
    void testVarcharCharacterVsByteLength() throws Exception {
        // MySQL中VARCHAR(1)可以存一个汉字，DM默认以字节为单位
        // UTF-8字符集，varchar(3)才可以存一个汉字
        String mysqlSql = """
            CREATE TABLE varchar_length_test (
                id INT NOT NULL AUTO_INCREMENT,
                chinese_char VARCHAR(1),
                chinese_name VARCHAR(10),
                english_name VARCHAR(50),
                mixed_content VARCHAR(255),
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证VARCHAR长度保持（现在严格保持原始长度不变）
        // 注意：根据修正后的要求，只有实际数据超出时才调整，否则保持原始长度
        assertTrue((damengSql.toUpperCase().contains("CHINESE_CHAR") || damengSql.contains("chinese_char")) &&
                   damengSql.contains("VARCHAR(1)"),
                   "VARCHAR(1)应保持原始长度");
        // chinese_name现在保持原始长度，不进行预测性调整
        assertTrue((damengSql.toUpperCase().contains("CHINESE_NAME") || damengSql.contains("chinese_name")) &&
                   damengSql.contains("VARCHAR(10)"),
                   "VARCHAR(10)应保持原始长度，不进行预测性调整");
        assertTrue((damengSql.toUpperCase().contains("ENGLISH_NAME") || damengSql.contains("english_name")) &&
                   damengSql.contains("VARCHAR(50)"),
                   "VARCHAR(50)应保持原始长度");
        
        // 注意：在实际使用中，可能需要将VARCHAR(N)转换为VARCHAR(N CHAR)
        // 以确保中文字符的正确存储
    }

    @Test
    @DisplayName("列长度超出定义问题")
    void testColumnLengthExceedsDefinition() throws Exception {
        // MySQL迁移到DM报错：列[NAMES]长度超出定义
        // 原因是中文字符在不同字符集下的字节长度不同
        String mysqlSql = """
            CREATE TABLE length_exceed_test (
                id INT NOT NULL AUTO_INCREMENT,
                names VARCHAR(10),
                chinese_names VARCHAR(20),
                descriptions TEXT,
                short_desc VARCHAR(50),
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证字段长度转换（现在严格保持原始长度不变）
        assertTrue((damengSql.toUpperCase().contains("NAMES") || damengSql.contains("names")) &&
                   damengSql.contains("VARCHAR(10)"),
                   "names字段应保持原始长度VARCHAR(10)");
        assertTrue((damengSql.toUpperCase().contains("CHINESE_NAMES") || damengSql.contains("chinese_names")) &&
                   damengSql.contains("VARCHAR(20)"),
                   "中文名称字段应保持原始长度VARCHAR(20)");
        assertTrue((damengSql.toUpperCase().contains("DESCRIPTIONS") || damengSql.contains("descriptions")) &&
                   damengSql.contains("CLOB"),
                   "TEXT应转换为CLOB");
        
        // 验证字符集转换 - 达梦不支持表级字符集
        assertFalse(damengSql.contains("CHARACTER SET"),
                   "达梦不支持表级字符集，应移除CHARACTER SET");
        assertFalse(damengSql.contains("ENGINE="),
                   "ENGINE选项应被移除");
    }

    @Test
    @DisplayName("VARCHAR内容不完整且结尾乱码问题")
    void testVarcharContentIncompleteWithGarbledEnd() throws Exception {
        // MySQL迁移到DM8，VARCHAR类型字段内容不完整，且结尾有乱码
        // 原因是字符串截断造成的乱码或数据不完整
        String mysqlSql = """
            CREATE TABLE content_integrity_test (
                id INT NOT NULL AUTO_INCREMENT,
                title VARCHAR(100),
                content VARCHAR(500),
                summary VARCHAR(200),
                tags VARCHAR(255),
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证VARCHAR字段转换
        assertTrue((damengSql.toUpperCase().contains("TITLE") || damengSql.contains("title")) &&
                   damengSql.contains("VARCHAR(100)"),
                   "标题字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("CONTENT") || damengSql.contains("content")) &&
                   damengSql.contains("VARCHAR(500)"),
                   "内容字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("SUMMARY") || damengSql.contains("summary")) &&
                   damengSql.contains("VARCHAR(200)"),
                   "摘要字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("TAGS") || damengSql.contains("tags")) &&
                   damengSql.contains("VARCHAR(255)"),
                   "标签字段应正确转换");
    }

    @Test
    @DisplayName("字符集兼容模式设置验证")
    void testCharsetCompatibilityMode() throws Exception {
        // 验证兼容MySQL的字符集设置
        String mysqlSql = """
            CREATE TABLE compatibility_test (
                id INT NOT NULL AUTO_INCREMENT,
                data VARCHAR(100) CHARACTER SET utf8,
                content TEXT CHARACTER SET utf8mb4,
                binary_data VARBINARY(255),
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证字符集转换 - 达梦不支持表级字符集
        assertFalse(damengSql.contains("CHARACTER SET"),
                   "达梦不支持表级字符集，应移除CHARACTER SET");
        assertFalse(damengSql.contains("utf8mb4"),
                    "不应包含utf8mb4");
        assertFalse(damengSql.contains("ENGINE="),
                   "ENGINE选项应被移除");
        
        // 验证二进制数据类型
        assertTrue((damengSql.toUpperCase().contains("BINARY_DATA") || damengSql.contains("binary_data")) &&
                   damengSql.contains("VARBINARY(255)"),
                   "VARBINARY类型应保持不变");
    }

    @Test
    @DisplayName("多字节字符处理测试")
    void testMultiByteCharacterHandling() throws Exception {
        // 测试多字节字符的处理，包括中文、日文、韩文等
        String mysqlSql = """
            CREATE TABLE multibyte_test (
                id INT NOT NULL AUTO_INCREMENT,
                chinese_text VARCHAR(50),
                japanese_text VARCHAR(50),
                korean_text VARCHAR(50),
                emoji_text VARCHAR(100),
                mixed_text TEXT,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
            """;
        
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证多字节字符字段转换
        assertTrue((damengSql.toUpperCase().contains("CHINESE_TEXT") || damengSql.contains("chinese_text")) &&
                   damengSql.contains("VARCHAR(50)"),
                   "中文文本字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("JAPANESE_TEXT") || damengSql.contains("japanese_text")) &&
                   damengSql.contains("VARCHAR(50)"),
                   "日文文本字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("KOREAN_TEXT") || damengSql.contains("korean_text")) &&
                   damengSql.contains("VARCHAR(50)"),
                   "韩文文本字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("EMOJI_TEXT") || damengSql.contains("emoji_text")) &&
                   damengSql.contains("VARCHAR(100)"),
                   "表情文本字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("MIXED_TEXT") || damengSql.contains("mixed_text")) &&
                   damengSql.contains("CLOB"),
                   "混合文本应转换为CLOB");
        
        // 验证排序规则被移除
        assertFalse(damengSql.contains("COLLATE"), 
                    "排序规则应被移除");
        assertFalse(damengSql.contains("general_ci"), 
                    "排序规则应被移除");
    }

    @Test
    @DisplayName("字符长度单位转换测试")
    void testCharacterLengthUnitConversion() throws Exception {
        // 测试字符长度单位的转换（字符 vs 字节）
        String mysqlSql = """
            CREATE TABLE length_unit_test (
                id INT NOT NULL AUTO_INCREMENT,
                char_field CHAR(10),
                varchar_field VARCHAR(50),
                text_field TEXT,
                binary_field BINARY(16),
                varbinary_field VARBINARY(100),
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证字符类型转换
        assertTrue((damengSql.toUpperCase().contains("CHAR_FIELD") || damengSql.contains("char_field")) &&
                   damengSql.contains("CHAR(10)"),
                   "CHAR字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("VARCHAR_FIELD") || damengSql.contains("varchar_field")) &&
                   damengSql.contains("VARCHAR(50)"),
                   "VARCHAR字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("TEXT_FIELD") || damengSql.contains("text_field")) &&
                   damengSql.contains("CLOB"),
                   "TEXT字段应转换为CLOB");

        // 验证二进制类型转换
        assertTrue((damengSql.toUpperCase().contains("BINARY_FIELD") || damengSql.contains("binary_field")) &&
                   damengSql.contains("BINARY(16)"),
                   "BINARY字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("VARBINARY_FIELD") || damengSql.contains("varbinary_field")) &&
                   damengSql.contains("VARBINARY(100)"),
                   "VARBINARY字段应正确转换");
    }

    @Test
    @DisplayName("字符集转换综合测试")
    void testComprehensiveCharsetConversion() throws Exception {
        // 综合测试各种字符集相关的转换
        String mysqlSql = """
            CREATE TABLE comprehensive_charset_test (
                id INT NOT NULL AUTO_INCREMENT,
                ascii_data VARCHAR(100) CHARACTER SET ascii,
                latin1_data VARCHAR(100) CHARACTER SET latin1,
                utf8_data VARCHAR(100) CHARACTER SET utf8,
                utf8mb4_data VARCHAR(100) CHARACTER SET utf8mb4,
                binary_data BLOB,
                text_data TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
            """;
        
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证字符集转换 - 达梦不支持表级字符集
        assertFalse(damengSql.contains("CHARACTER SET"),
                   "达梦不支持表级字符集，应移除所有CHARACTER SET");

        // 验证不包含MySQL特有的字符集定义
        assertFalse(damengSql.contains("CHARSET ascii"),
                    "不应包含ascii字符集定义");
        assertFalse(damengSql.contains("CHARSET latin1"),
                    "不应包含latin1字符集定义");
        assertFalse(damengSql.contains("CHARSET utf8mb4"),
                    "不应包含utf8mb4字符集定义");
        assertFalse(damengSql.contains("ENGINE="),
                   "ENGINE选项应被移除");
        
        // 验证排序规则被移除
        assertFalse(damengSql.contains("COLLATE"), 
                    "所有排序规则应被移除");
        assertFalse(damengSql.contains("unicode_ci"), 
                    "排序规则应被移除");
        assertFalse(damengSql.contains("general_ci"), 
                    "排序规则应被移除");
        
        // 验证数据类型转换
        assertTrue((damengSql.toUpperCase().contains("TEXT_DATA") || damengSql.contains("text_data")) &&
                   damengSql.contains("CLOB"),
                   "TEXT应转换为CLOB");
        assertTrue((damengSql.toUpperCase().contains("BINARY_DATA") || damengSql.contains("binary_data")) &&
                   damengSql.contains("BLOB"),
                   "BLOB应保持不变");
    }

    @Test
    @DisplayName("字符编码乱码预防测试")
    void testCharacterEncodingCorruptionPrevention() throws Exception {
        // 测试预防字符编码乱码的表结构设计
        String mysqlSql = """
            CREATE TABLE encoding_safe_test (
                id INT NOT NULL AUTO_INCREMENT,
                safe_text VARCHAR(200),
                unsafe_text VARCHAR(50),
                long_content LONGTEXT,
                medium_content MEDIUMTEXT,
                short_content TINYTEXT,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        assertBasicConversionRequirements(damengSql);
        
        // 验证文本类型转换
        assertTrue((damengSql.toUpperCase().contains("SAFE_TEXT") || damengSql.contains("safe_text")) &&
                   damengSql.contains("VARCHAR(200)"),
                   "安全文本字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("UNSAFE_TEXT") || damengSql.contains("unsafe_text")) &&
                   damengSql.contains("VARCHAR(50)"),
                   "可能不安全的文本字段应正确转换");
        assertTrue((damengSql.toUpperCase().contains("LONG_CONTENT") || damengSql.contains("long_content")) &&
                   damengSql.contains("CLOB"),
                   "LONGTEXT应转换为CLOB");
        assertTrue((damengSql.toUpperCase().contains("MEDIUM_CONTENT") || damengSql.contains("medium_content")) &&
                   damengSql.contains("CLOB"),
                   "MEDIUMTEXT应转换为CLOB");
        assertTrue((damengSql.toUpperCase().contains("SHORT_CONTENT") || damengSql.contains("short_content")) &&
                   damengSql.contains("VARCHAR"),
                   "TINYTEXT应转换为VARCHAR");
    }
}
