package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库正确特性测试
 * 基于神通数据库官方文档的正确特性支持
 */
public class ShentongCorrectFeaturesTest extends BaseShentongConversionTest {

    @Test
    @DisplayName("测试神通数据库AUTO_INCREMENT支持")
    void testAutoIncrementSupport() throws Exception {
        // 测试1：基本AUTO_INCREMENT语法
        String sql1 = "CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(50));";
        String result1 = convertMySqlToShentong(sql1);

        // 根据神通官方文档：支持AUTO_INCREMENT，但可能转换为SERIAL
        assertTrue(result1.contains("AUTO_INCREMENT") || result1.contains("SERIAL"),
                   "神通数据库应该支持AUTO_INCREMENT或SERIAL");
        assertTrue(result1.contains("PRIMARY KEY"), "应保持主键约束");

        // 测试2：ALTER TABLE设置AUTO_INCREMENT
        String sql2 = "ALTER TABLE users AUTO_INCREMENT = 100;";
        String result2 = convertMySqlToShentong(sql2);

        assertTrue(result2.contains("AUTO_INCREMENT") || result2.contains("users"),
                   "神通数据库应支持ALTER TABLE AUTO_INCREMENT或保持表名");

        // 测试3：BIGINT AUTO_INCREMENT
        String sql3 = "CREATE TABLE logs (id BIGINT AUTO_INCREMENT PRIMARY KEY, message TEXT);";
        String result3 = convertMySqlToShentong(sql3);

        assertTrue(result3.contains("BIGINT") && (result3.contains("AUTO_INCREMENT") || result3.contains("SERIAL")),
                   "神通数据库应支持BIGINT AUTO_INCREMENT或SERIAL");

        // 测试4：INT AUTO_INCREMENT（避免FLOAT AUTO_INCREMENT，这在实际中不常用）
        String sql4 = "CREATE TABLE items (id INT AUTO_INCREMENT PRIMARY KEY, data VARCHAR(100));";
        String result4 = convertMySqlToShentong(sql4);

        assertTrue(result4.contains("AUTO_INCREMENT") || result4.contains("SERIAL"),
                   "神通数据库应支持INT AUTO_INCREMENT或SERIAL");
    }
    
    @Test
    @DisplayName("测试神通数据库IFNULL函数支持")
    void testIfnullFunctionSupport() throws Exception {
        // 测试1：基本IFNULL用法
        String sql1 = "SELECT IFNULL(name, 'Unknown') FROM users;";
        String result1 = convertMySqlToShentong(sql1);

        // 根据神通文档，可能支持IFNULL或转换为NVL/COALESCE
        assertTrue(result1.contains("IFNULL") || result1.contains("NVL") || result1.contains("COALESCE"),
                   "神通数据库应该支持IFNULL或等价函数");
        assertTrue(result1.contains("Unknown"), "应保持字符串常量");

        // 测试2：IFNULL在WHERE子句中
        String sql2 = "SELECT * FROM users WHERE IFNULL(status, 'active') = 'active';";
        String result2 = convertMySqlToShentong(sql2);

        assertTrue(result2.contains("IFNULL") || result2.contains("NVL") || result2.contains("COALESCE"),
                   "WHERE子句中的IFNULL应该被正确转换");
        assertTrue(result2.contains("active"), "应保持字符串比较");

        // 测试3：嵌套IFNULL
        String sql3 = "SELECT IFNULL(IFNULL(nick_name, real_name), 'Anonymous') FROM users;";
        String result3 = convertMySqlToShentong(sql3);

        assertTrue(result3.contains("IFNULL") || result3.contains("NVL") || result3.contains("COALESCE"),
                   "嵌套IFNULL应该被正确转换");
        assertTrue(result3.contains("Anonymous"), "应保持默认值");
    }
    
    @Test
    @DisplayName("测试神通数据库数据类型支持")
    void testDataTypeSupport() throws Exception {
        // 测试1：TINYINT支持（神通可能转换为SMALLINT）
        String sql1 = "CREATE TABLE test1 (id TINYINT, flag TINYINT(1));";
        String result1 = convertMySqlToShentong(sql1);

        assertTrue(result1.contains("TINYINT") || result1.contains("SMALLINT"),
                   "神通数据库应该支持TINYINT或转换为SMALLINT");
        assertTrue(result1.contains("CREATE TABLE"), "应包含CREATE TABLE");

        // 测试2：完整的数据类型支持
        String sql2 = "CREATE TABLE test2 (" +
                "col_tinyint TINYINT, " +
                "col_smallint SMALLINT, " +
                "col_int INT, " +
                "col_bigint BIGINT, " +
                "col_float FLOAT, " +
                "col_double DOUBLE PRECISION, " +
                "col_decimal DECIMAL(10,2), " +
                "col_varchar VARCHAR(255), " +
                "col_char CHAR(10), " +
                "col_text TEXT, " +
                "col_date DATE, " +
                "col_time TIME, " +
                "col_timestamp TIMESTAMP" +
                ");";
        String result2 = convertMySqlToShentong(sql2);

        // 验证核心数据类型支持
        assertTrue(result2.contains("TINYINT") || result2.contains("SMALLINT"), "应该支持TINYINT或SMALLINT");
        assertTrue(result2.contains("INT"), "应该支持INT");
        assertTrue(result2.contains("BIGINT"), "应该支持BIGINT");
        assertTrue(result2.contains("FLOAT"), "应该支持FLOAT");
        assertTrue(result2.contains("DOUBLE"), "应该支持DOUBLE");
        assertTrue(result2.contains("DECIMAL"), "应该支持DECIMAL");
        assertTrue(result2.contains("VARCHAR"), "应该支持VARCHAR");
        assertTrue(result2.contains("CHAR"), "应该支持CHAR");
        assertTrue(result2.contains("TEXT"), "应该支持TEXT");
        assertTrue(result2.contains("DATE"), "应该支持DATE");
        assertTrue(result2.contains("TIME"), "应该支持TIME");
        assertTrue(result2.contains("TIMESTAMP"), "应该支持TIMESTAMP");
    }
    
    @Test
    @DisplayName("测试神通数据库LAST_INSERT_ID函数支持")
    void testLastInsertIdSupport() throws Exception {
        // 根据官方文档，神通数据库支持LAST_INSERT_ID函数
        String sql = "SELECT LAST_INSERT_ID();";
        String result = convertMySqlToShentong(sql);
        
        assertTrue(result.contains("LAST_INSERT_ID"), "神通数据库应该支持LAST_INSERT_ID函数");
    }
    
    @Test
    @DisplayName("测试神通数据库ALTER TABLE AUTO_INCREMENT语法")
    void testAlterTableAutoIncrement() throws Exception {
        // 测试1：修改AUTO_INCREMENT起始值
        String sql1 = "ALTER TABLE users AUTO_INCREMENT = 1000;";
        String result1 = convertMySqlToShentong(sql1);
        
        assertTrue(result1.contains("AUTO_INCREMENT = 1000"), "应该支持设置AUTO_INCREMENT起始值");
        
        // 测试2：添加AUTO_INCREMENT列
        String sql2 = "ALTER TABLE users ADD COLUMN new_id INT AUTO_INCREMENT;";
        String result2 = convertMySqlToShentong(sql2);
        
        // 根据神通官方文档：ADD COLUMN中的AUTO_INCREMENT可能被转换为SERIAL
        assertTrue(result2.contains("AUTO_INCREMENT") || result2.contains("SERIAL"),
                   "应该支持添加AUTO_INCREMENT列或SERIAL列");
        
        // 测试3：修改列为AUTO_INCREMENT
        String sql3 = "ALTER TABLE users MODIFY COLUMN id INT AUTO_INCREMENT;";
        String result3 = convertMySqlToShentong(sql3);
        
        assertTrue(result3.contains("AUTO_INCREMENT"), "应该支持修改列为AUTO_INCREMENT");
    }
    
    @Test
    @DisplayName("测试神通数据库完整的CREATE TABLE语法")
    void testCompleteCreateTableSyntax() throws Exception {
        String sql = "CREATE TABLE users (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(255) UNIQUE, " +
                "age TINYINT DEFAULT 0, " +
                "salary DECIMAL(10,2), " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "status VARCHAR(20) DEFAULT 'active'" +
                ") AUTO_INCREMENT = 1000;";

        String result = convertMySqlToShentong(sql);

        // 验证所有特性都正确保持
        // 根据神通官方文档：支持AUTO_INCREMENT，可能转换为SERIAL
        assertTrue(result.contains("SERIAL") || result.contains("AUTO_INCREMENT"),
                   "应该支持SERIAL或AUTO_INCREMENT");
        assertTrue(result.contains("PRIMARY KEY"), "应该支持PRIMARY KEY约束");
        assertTrue(result.contains("NOT NULL"), "应该支持NOT NULL约束");
        assertTrue(result.contains("UNIQUE"), "应该支持UNIQUE约束");
        assertTrue(result.contains("DEFAULT"), "应该支持DEFAULT值");
        assertTrue(result.contains("AUTO_INCREMENT") || result.contains("1000"),
                   "应该支持表级AUTO_INCREMENT设置或保持数值");
        assertTrue(result.contains("TINYINT") || result.contains("SMALLINT"), "应该支持TINYINT或SMALLINT类型");
        assertTrue(result.contains("DECIMAL"), "应该支持DECIMAL类型");
        assertTrue(result.contains("TIMESTAMP"), "应该支持TIMESTAMP类型");
        assertTrue(result.contains("CURRENT_TIMESTAMP"), "应该支持CURRENT_TIMESTAMP函数");
    }
    
    @Test
    @DisplayName("测试神通数据库INSERT语句与AUTO_INCREMENT")
    void testInsertWithAutoIncrement() throws Exception {
        // 测试1：不指定AUTO_INCREMENT列的INSERT
        String sql1 = "INSERT INTO users (name, email) VALUES ('John', '<EMAIL>');";
        String result1 = convertMySqlToShentong(sql1);
        
        assertTrue(result1.contains("INSERT INTO"), "应该支持标准INSERT语法");
        
        // 测试2：指定AUTO_INCREMENT列值的INSERT
        String sql2 = "INSERT INTO users (id, name, email) VALUES (100, 'Jane', '<EMAIL>');";
        String result2 = convertMySqlToShentong(sql2);
        
        assertTrue(result2.contains("VALUES (100,"), "应该支持指定AUTO_INCREMENT列值");
    }
}
