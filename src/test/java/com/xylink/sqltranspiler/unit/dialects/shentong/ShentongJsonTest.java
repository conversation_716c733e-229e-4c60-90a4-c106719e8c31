package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库JSON支持测试
 * 
 * 基于官方文档测试JSON支持：
 * - MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/json.html
 * - 神通数据库官方文档：shentong.md 中关于大对象类型的支持
 * 
 * 测试覆盖的JSON功能：
 * 1. JSON数据类型转换 - JSON → TEXT/CLOB
 * 2. JSON函数转换 - JSON_EXTRACT、JSON_SET、JSON_VALID等
 * 3. JSON操作转换 - 插入、查询、更新JSON数据
 * 4. JSON路径表达式 - $.path语法支持
 * 5. JSON聚合函数 - JSON_ARRAYAGG、JSON_OBJECTAGG等
 * 6. JSON比较和排序 - JSON值的比较操作
 * 
 * <AUTHOR>
 */
@DisplayName("神通数据库JSON支持测试")
public class ShentongJsonTest extends BaseShentongConversionTest {
    
    private static final Logger log = LoggerFactory.getLogger(ShentongJsonTest.class);

    /**
     * 测试JSON数据类型转换
     * 
     * 根据MySQL官方文档：JSON数据类型用于存储JSON文档
     * 根据神通官方文档：神通数据库支持CLOB和TEXT类型存储大文本数据
     */
    @Test
    @DisplayName("JSON数据类型转换测试")
    public void testJsonDataTypeConversion() throws Exception {
        log.info("=== JSON数据类型转换测试 ===");
        
        String mysqlSql = """
            CREATE TABLE json_test (
                id INT NOT NULL AUTO_INCREMENT,
                user_profile JSON,
                settings JSON NOT NULL,
                metadata JSON DEFAULT '{}',
                config JSON COMMENT 'Configuration data',
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "JSON数据类型SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证JSON数据类型转换结果
        if (shentongSql.contains("JSON")) {
            // 如果神通支持JSON数据类型
            assertTrue(shentongSql.contains("user_profile JSON"), "应保持JSON数据类型");
            assertTrue(shentongSql.contains("settings JSON NOT NULL"), "应保持JSON NOT NULL约束");
            assertTrue(shentongSql.contains("metadata JSON DEFAULT '{}'"), "应保持JSON默认值");
            
            log.info("✅ 神通数据库支持JSON数据类型，转换成功");
        } else {
            // 如果神通不支持JSON，应转换为TEXT或CLOB
            assertTrue(shentongSql.contains("TEXT") || shentongSql.contains("CLOB"), 
                      "JSON应转换为TEXT或CLOB类型");
            assertTrue(shentongSql.contains("user_profile"), "应保持列名");
            assertTrue(shentongSql.contains("settings"), "应保持列名");
            assertTrue(shentongSql.contains("metadata"), "应保持列名");
            assertTrue(shentongSql.contains("NOT NULL"), "应保持NOT NULL约束");
            
            log.info("⚠️ 神通数据库不支持JSON数据类型，已转换为TEXT/CLOB");
        }
        
        // 验证基本SQL结构
        assertTrue(shentongSql.contains("CREATE TABLE"), "应包含CREATE TABLE");
        assertTrue(shentongSql.contains("json_test"), "应包含表名");
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应包含AUTO_INCREMENT");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应包含主键定义");
        assertTrue(shentongSql.endsWith(";"), "应以分号结尾");
    }

    /**
     * 测试JSON函数转换
     * 
     * 根据MySQL官方文档：JSON_EXTRACT、JSON_SET、JSON_VALID等函数
     */
    @Test
    @DisplayName("JSON函数转换测试")
    public void testJsonFunctionConversion() throws Exception {
        log.info("=== JSON函数转换测试 ===");
        
        String mysqlSql = """
            SELECT 
                id,
                JSON_EXTRACT(user_profile, '$.name') as user_name,
                JSON_EXTRACT(user_profile, '$.email') as user_email,
                JSON_UNQUOTE(JSON_EXTRACT(settings, '$.theme')) as theme,
                JSON_VALID(metadata) as is_valid_json,
                JSON_LENGTH(config) as config_length,
                JSON_TYPE(user_profile) as profile_type
            FROM json_test 
            WHERE JSON_CONTAINS(user_profile, '{"active": true}')
              AND JSON_SEARCH(settings, 'one', 'dark') IS NOT NULL;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "JSON函数SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证JSON函数转换结果
        if (shentongSql.contains("JSON_EXTRACT")) {
            // 如果神通支持JSON函数
            assertTrue(shentongSql.contains("JSON_EXTRACT"), "应保持JSON_EXTRACT函数");
            assertTrue(shentongSql.contains("JSON_VALID"), "应保持JSON_VALID函数");
            assertTrue(shentongSql.contains("JSON_LENGTH"), "应保持JSON_LENGTH函数");
            assertTrue(shentongSql.contains("JSON_CONTAINS"), "应保持JSON_CONTAINS函数");
            
            log.info("✅ 神通数据库支持JSON函数，转换成功");
        } else {
            // 如果神通不支持JSON函数，应该能正常解析但可能转换为其他形式
            assertTrue(shentongSql.contains("SELECT"), "应包含SELECT语句");
            assertTrue(shentongSql.contains("FROM json_test"), "应包含FROM子句");
            assertTrue(shentongSql.contains("WHERE"), "应包含WHERE子句");
            
            log.info("⚠️ 神通数据库不完全支持JSON函数，已进行适配转换");
        }
        
        // 验证基本SQL结构
        assertTrue(shentongSql.contains("SELECT"), "应包含SELECT");
        assertTrue(shentongSql.contains("FROM"), "应包含FROM");
        assertTrue(shentongSql.contains("WHERE"), "应包含WHERE");
        assertTrue(shentongSql.endsWith(";"), "应以分号结尾");
    }

    /**
     * 测试JSON插入和更新操作
     * 
     * 根据MySQL官方文档：JSON值的插入和更新操作
     */
    @Test
    @DisplayName("JSON插入和更新操作测试")
    public void testJsonInsertAndUpdate() throws Exception {
        log.info("=== JSON插入和更新操作测试 ===");
        
        String mysqlSql = """
            INSERT INTO json_test (user_profile, settings, metadata, config) VALUES 
            (
                '{"name": "John Doe", "email": "<EMAIL>", "age": 30, "active": true}',
                '{"theme": "dark", "language": "en", "notifications": {"email": true, "sms": false}}',
                '{"created_at": "2023-01-01", "version": "1.0"}',
                '{"database": {"host": "localhost", "port": 3306}, "cache": {"enabled": true}}'
            );
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "JSON插入SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证JSON插入转换结果
        assertTrue(shentongSql.contains("INSERT INTO"), "应包含INSERT INTO");
        assertTrue(shentongSql.contains("json_test"), "应包含表名");
        assertTrue(shentongSql.contains("user_profile"), "应包含列名");
        assertTrue(shentongSql.contains("settings"), "应包含列名");
        assertTrue(shentongSql.contains("metadata"), "应包含列名");
        assertTrue(shentongSql.contains("config"), "应包含列名");
        assertTrue(shentongSql.contains("VALUES"), "应包含VALUES");
        
        // JSON字符串应该被保持
        assertTrue(shentongSql.contains("John Doe"), "应保持JSON内容");
        assertTrue(shentongSql.contains("<EMAIL>"), "应保持JSON内容");
        assertTrue(shentongSql.contains("dark"), "应保持JSON内容");
        assertTrue(shentongSql.contains("localhost"), "应保持JSON内容");
        
        assertTrue(shentongSql.endsWith(";"), "应以分号结尾");
        
        log.info("✅ JSON插入操作转换测试通过");
    }

    /**
     * 测试JSON更新操作
     * 
     * 根据MySQL官方文档：JSON_SET、JSON_REPLACE、JSON_REMOVE等函数
     */
    @Test
    @DisplayName("JSON更新操作测试")
    public void testJsonUpdateOperations() throws Exception {
        log.info("=== JSON更新操作测试 ===");
        
        String mysqlSql = """
            UPDATE json_test 
            SET 
                user_profile = JSON_SET(user_profile, '$.age', 31, '$.last_login', NOW()),
                settings = JSON_REPLACE(settings, '$.theme', 'light'),
                metadata = JSON_REMOVE(metadata, '$.temp_data'),
                config = JSON_INSERT(config, '$.new_feature', true)
            WHERE id = 1;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "JSON更新SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证JSON更新转换结果
        assertTrue(shentongSql.contains("UPDATE"), "应包含UPDATE");
        assertTrue(shentongSql.contains("json_test"), "应包含表名");
        assertTrue(shentongSql.contains("SET"), "应包含SET");
        assertTrue(shentongSql.contains("WHERE"), "应包含WHERE");
        assertTrue(shentongSql.contains("id = 1"), "应包含WHERE条件");
        
        if (shentongSql.contains("JSON_SET")) {
            // 如果神通支持JSON修改函数
            assertTrue(shentongSql.contains("JSON_SET"), "应保持JSON_SET函数");
            assertTrue(shentongSql.contains("JSON_REPLACE"), "应保持JSON_REPLACE函数");
            assertTrue(shentongSql.contains("JSON_REMOVE"), "应保持JSON_REMOVE函数");
            assertTrue(shentongSql.contains("JSON_INSERT"), "应保持JSON_INSERT函数");
            
            log.info("✅ 神通数据库支持JSON修改函数，转换成功");
        } else {
            // 如果神通不支持JSON修改函数，应该能正常解析
            assertTrue(shentongSql.contains("user_profile"), "应包含列名");
            assertTrue(shentongSql.contains("settings"), "应包含列名");
            assertTrue(shentongSql.contains("metadata"), "应包含列名");
            assertTrue(shentongSql.contains("config"), "应包含列名");
            
            log.info("⚠️ 神通数据库不支持JSON修改函数，已进行适配转换");
        }
        
        assertTrue(shentongSql.endsWith(";"), "应以分号结尾");
        
        log.info("✅ JSON更新操作转换测试通过");
    }

    /**
     * 测试JSON聚合函数
     * 
     * 根据MySQL官方文档：JSON_ARRAYAGG、JSON_OBJECTAGG等聚合函数
     */
    @Test
    @DisplayName("JSON聚合函数测试")
    public void testJsonAggregateFunctions() throws Exception {
        log.info("=== JSON聚合函数测试 ===");
        
        String mysqlSql = """
            SELECT 
                JSON_ARRAYAGG(user_name) as user_names,
                JSON_OBJECTAGG(id, user_name) as user_map,
                JSON_ARRAYAGG(JSON_OBJECT('id', id, 'name', user_name)) as user_objects
            FROM (
                SELECT 
                    id,
                    JSON_UNQUOTE(JSON_EXTRACT(user_profile, '$.name')) as user_name
                FROM json_test
                WHERE JSON_VALID(user_profile) = 1
            ) t
            GROUP BY 1;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "JSON聚合函数SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证JSON聚合函数转换结果
        assertTrue(shentongSql.contains("SELECT"), "应包含SELECT");
        assertTrue(shentongSql.contains("FROM"), "应包含FROM");
        assertTrue(shentongSql.contains("GROUP BY"), "应包含GROUP BY");
        
        if (shentongSql.contains("JSON_ARRAYAGG")) {
            // 如果神通支持JSON聚合函数
            assertTrue(shentongSql.contains("JSON_ARRAYAGG"), "应保持JSON_ARRAYAGG函数");
            assertTrue(shentongSql.contains("JSON_OBJECTAGG"), "应保持JSON_OBJECTAGG函数");
            assertTrue(shentongSql.contains("JSON_OBJECT"), "应保持JSON_OBJECT函数");
            
            log.info("✅ 神通数据库支持JSON聚合函数，转换成功");
        } else {
            // 如果神通不支持JSON聚合函数，应该能正常解析
            assertTrue(shentongSql.contains("user_names"), "应包含别名");
            assertTrue(shentongSql.contains("user_map"), "应包含别名");
            assertTrue(shentongSql.contains("user_objects"), "应包含别名");
            
            log.info("⚠️ 神通数据库不支持JSON聚合函数，已进行适配转换");
        }
        
        assertTrue(shentongSql.endsWith(";"), "应以分号结尾");
        
        log.info("✅ JSON聚合函数转换测试通过");
    }

    /**
     * 测试JSON路径表达式
     * 
     * 根据MySQL官方文档：$.path语法用于访问JSON文档中的特定元素
     */
    @Test
    @DisplayName("JSON路径表达式测试")
    public void testJsonPathExpressions() throws Exception {
        log.info("=== JSON路径表达式测试 ===");
        
        String mysqlSql = """
            SELECT 
                id,
                user_profile->'$.name' as name_with_quotes,
                user_profile->>'$.name' as name_without_quotes,
                user_profile->'$.address.city' as city,
                user_profile->'$.hobbies[0]' as first_hobby,
                user_profile->'$.hobbies[*]' as all_hobbies,
                settings->'$.notifications.email' as email_notifications
            FROM json_test
            WHERE user_profile->'$.active' = true
              AND settings->'$.theme' = 'dark';
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "JSON路径表达式SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证JSON路径表达式转换结果
        assertTrue(shentongSql.contains("SELECT"), "应包含SELECT");
        assertTrue(shentongSql.contains("FROM json_test"), "应包含FROM子句");
        assertTrue(shentongSql.contains("WHERE"), "应包含WHERE子句");
        
        if (shentongSql.contains("->")) {
            // 如果神通支持JSON路径操作符
            assertTrue(shentongSql.contains("user_profile->"), "应保持JSON路径操作符");
            assertTrue(shentongSql.contains("settings->"), "应保持JSON路径操作符");
            assertTrue(shentongSql.contains("'$.name'"), "应保持JSON路径表达式");
            assertTrue(shentongSql.contains("'$.address.city'"), "应保持嵌套路径");
            assertTrue(shentongSql.contains("'$.hobbies[0]'"), "应保持数组索引");
            
            log.info("✅ 神通数据库支持JSON路径操作符，转换成功");
        } else {
            // 如果神通不支持JSON路径操作符，应该转换为函数调用
            assertTrue(shentongSql.contains("name_with_quotes"), "应包含别名");
            assertTrue(shentongSql.contains("name_without_quotes"), "应包含别名");
            assertTrue(shentongSql.contains("city"), "应包含别名");
            assertTrue(shentongSql.contains("first_hobby"), "应包含别名");
            
            log.info("⚠️ 神通数据库不支持JSON路径操作符，已转换为函数调用");
        }
        
        assertTrue(shentongSql.endsWith(";"), "应以分号结尾");
        
        log.info("✅ JSON路径表达式转换测试通过");
    }

    /**
     * 测试JSON错误处理
     * 验证不支持的JSON语法的处理
     */
    @Test
    @DisplayName("JSON错误处理测试")
    public void testJsonErrorHandling() throws Exception {
        log.info("=== JSON错误处理测试 ===");
        
        // 测试基本的JSON查询，确保不会出现转换错误
        String mysqlSql = """
            SELECT 
                id,
                user_profile,
                CASE 
                    WHEN JSON_VALID(user_profile) = 1 THEN 'Valid JSON'
                    ELSE 'Invalid JSON'
                END as json_status
            FROM json_test
            WHERE user_profile IS NOT NULL
            ORDER BY id;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "JSON错误处理SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        assertFalse(shentongSql.contains("ERROR"), "转换结果不应包含错误信息");
        assertFalse(shentongSql.contains("FAILED"), "转换结果不应包含失败信息");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证基本SQL结构
        assertTrue(shentongSql.contains("SELECT"), "应包含SELECT");
        assertTrue(shentongSql.contains("CASE"), "应包含CASE表达式");
        assertTrue(shentongSql.contains("FROM json_test"), "应包含FROM子句");
        assertTrue(shentongSql.contains("WHERE"), "应包含WHERE子句");
        assertTrue(shentongSql.contains("ORDER BY"), "应包含ORDER BY子句");
        assertTrue(shentongSql.endsWith(";"), "应以分号结尾");
        
        log.info("✅ JSON错误处理测试通过");
    }

    /**
     * 测试JSON数组操作
     *
     * 根据MySQL官方文档：JSON数组的创建、访问和修改操作
     */
    @Test
    @DisplayName("JSON数组操作测试")
    public void testJsonArrayOperations() throws Exception {
        log.info("=== JSON数组操作测试 ===");

        String mysqlSql = """
            SELECT
                id,
                JSON_EXTRACT(user_profile, '$.hobbies') as hobbies,
                JSON_LENGTH(user_profile->'$.hobbies') as hobby_count,
                JSON_CONTAINS(user_profile->'$.hobbies', '"swimming"') as has_swimming,
                JSON_VALID(user_profile) as is_valid
            FROM json_test
            WHERE JSON_LENGTH(user_profile->'$.hobbies') > 0;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "JSON数组操作SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证JSON数组操作转换结果
        assertTrue(shentongSql.contains("SELECT"), "应包含SELECT");
        assertTrue(shentongSql.contains("FROM json_test"), "应包含FROM子句");
        assertTrue(shentongSql.contains("WHERE"), "应包含WHERE子句");

        if (shentongSql.contains("JSON_EXTRACT")) {
            // 如果神通支持JSON函数
            assertTrue(shentongSql.contains("JSON_EXTRACT"), "应保持JSON_EXTRACT函数");
            assertTrue(shentongSql.contains("JSON_LENGTH"), "应保持JSON_LENGTH函数");
            assertTrue(shentongSql.contains("JSON_CONTAINS"), "应保持JSON_CONTAINS函数");
            assertTrue(shentongSql.contains("JSON_VALID"), "应保持JSON_VALID函数");

            log.info("✅ 神通数据库支持JSON数组操作，转换成功");
        } else {
            // 如果神通不支持JSON函数，应该能正常解析
            assertTrue(shentongSql.contains("hobbies"), "应包含别名");
            assertTrue(shentongSql.contains("hobby_count"), "应包含别名");
            assertTrue(shentongSql.contains("has_swimming"), "应包含别名");

            log.info("⚠️ 神通数据库不完全支持JSON数组函数，已进行适配转换");
        }

        // 调试：打印字符串末尾字符
        log.info("转换结果末尾字符: [{}]", shentongSql.substring(Math.max(0, shentongSql.length() - 10)));
        assertTrue(shentongSql.trim().endsWith(";"), "应以分号结尾");

        log.info("✅ JSON数组操作转换测试通过");
    }

    /**
     * 测试JSON对象操作
     *
     * 根据MySQL官方文档：JSON对象的创建、合并和键值操作
     */
    @Test
    @DisplayName("JSON对象操作测试")
    public void testJsonObjectOperations() throws Exception {
        log.info("=== JSON对象操作测试 ===");

        String mysqlSql = """
            SELECT
                id,
                JSON_OBJECT('name', 'John', 'age', 30, 'active', true) as new_user,
                JSON_MERGE_PATCH(user_profile, '{"last_updated": "2023-12-01"}') as merged_profile,
                JSON_MERGE_PRESERVE(settings, '{"backup": {"enabled": true}}') as merged_settings,
                JSON_KEYS(user_profile) as profile_keys,
                JSON_DEPTH(config) as config_depth,
                JSON_STORAGE_SIZE(user_profile) as profile_size,
                JSON_PRETTY(user_profile) as formatted_profile
            FROM json_test
            WHERE JSON_TYPE(user_profile) = 'OBJECT';
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "JSON对象操作SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证JSON对象操作转换结果
        assertTrue(shentongSql.contains("SELECT"), "应包含SELECT");
        assertTrue(shentongSql.contains("FROM json_test"), "应包含FROM子句");
        assertTrue(shentongSql.contains("WHERE"), "应包含WHERE子句");

        if (shentongSql.contains("JSON_OBJECT")) {
            // 如果神通支持JSON对象函数
            assertTrue(shentongSql.contains("JSON_OBJECT"), "应保持JSON_OBJECT函数");
            assertTrue(shentongSql.contains("JSON_MERGE_PATCH"), "应保持JSON_MERGE_PATCH函数");
            assertTrue(shentongSql.contains("JSON_MERGE_PRESERVE"), "应保持JSON_MERGE_PRESERVE函数");
            assertTrue(shentongSql.contains("JSON_KEYS"), "应保持JSON_KEYS函数");
            assertTrue(shentongSql.contains("JSON_DEPTH"), "应保持JSON_DEPTH函数");
            assertTrue(shentongSql.contains("JSON_TYPE"), "应保持JSON_TYPE函数");

            log.info("✅ 神通数据库支持JSON对象操作，转换成功");
        } else {
            // 如果神通不支持JSON对象函数，应该能正常解析
            assertTrue(shentongSql.contains("new_user"), "应包含别名");
            assertTrue(shentongSql.contains("merged_profile"), "应包含别名");
            assertTrue(shentongSql.contains("profile_keys"), "应包含别名");

            log.info("⚠️ 神通数据库不完全支持JSON对象函数，已进行适配转换");
        }

        assertTrue(shentongSql.endsWith(";"), "应以分号结尾");

        log.info("✅ JSON对象操作转换测试通过");
    }

    /**
     * 测试JSON与其他SQL特性的组合
     * 验证JSON与窗口函数、CTE、子查询等的兼容性
     */
    @Test
    @DisplayName("JSON与其他SQL特性组合测试")
    public void testJsonWithOtherSqlFeatures() throws Exception {
        log.info("=== JSON与其他SQL特性组合测试 ===");

        String mysqlSql = """
            WITH user_stats AS (
                SELECT
                    id,
                    JSON_UNQUOTE(JSON_EXTRACT(user_profile, '$.name')) as user_name,
                    JSON_UNQUOTE(JSON_EXTRACT(user_profile, '$.department')) as department,
                    CAST(JSON_EXTRACT(user_profile, '$.salary') AS DECIMAL(10,2)) as salary
                FROM json_test
                WHERE JSON_VALID(user_profile) = 1
                  AND JSON_EXTRACT(user_profile, '$.active') = true
            )
            SELECT
                department,
                user_name,
                salary,
                ROW_NUMBER() OVER (PARTITION BY department ORDER BY salary DESC) as salary_rank,
                LAG(salary) OVER (PARTITION BY department ORDER BY salary DESC) as prev_salary,
                AVG(salary) OVER (PARTITION BY department) as dept_avg_salary
            FROM user_stats
            ORDER BY department, salary_rank;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "JSON与其他SQL特性组合SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证JSON与其他SQL特性组合转换结果
        assertTrue(shentongSql.contains("WITH user_stats AS"), "应包含CTE");
        assertTrue(shentongSql.contains("ROW_NUMBER()"), "应包含窗口函数");
        assertTrue(shentongSql.contains("LAG("), "应包含LAG窗口函数");
        assertTrue(shentongSql.contains("PARTITION BY"), "应包含分区子句");
        assertTrue(shentongSql.contains("ORDER BY"), "应包含排序子句");

        if (shentongSql.contains("JSON_EXTRACT")) {
            // 如果神通支持JSON函数与其他特性的组合
            assertTrue(shentongSql.contains("JSON_EXTRACT"), "应保持JSON_EXTRACT函数");
            assertTrue(shentongSql.contains("JSON_UNQUOTE"), "应保持JSON_UNQUOTE函数");
            assertTrue(shentongSql.contains("JSON_VALID"), "应保持JSON_VALID函数");

            log.info("✅ 神通数据库支持JSON与其他SQL特性的完美组合，转换成功");
        } else {
            // 如果神通不支持JSON函数，应该能正常解析其他特性
            assertTrue(shentongSql.contains("user_name"), "应包含别名");
            assertTrue(shentongSql.contains("department"), "应包含别名");
            assertTrue(shentongSql.contains("salary"), "应包含别名");

            log.info("⚠️ JSON函数已适配转换，其他SQL特性保持完整");
        }

        assertTrue(shentongSql.endsWith(";"), "应以分号结尾");

        log.info("✅ JSON与其他SQL特性组合转换测试通过");
    }
}
