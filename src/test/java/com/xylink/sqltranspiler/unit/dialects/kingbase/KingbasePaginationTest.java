package com.xylink.sqltranspiler.unit.dialects.kingbase;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;

/**
 * 金仓数据库分页查询支持测试
 * 基于金仓官方文档PostgreSQL兼容性的测试驱动开发
 * 参考金仓数据库官方文档：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 */
public class KingbasePaginationTest {

    private KingbaseGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new KingbaseGenerator();
    }

    @Test
    @DisplayName("测试LIMIT语句保持不变 - 金仓PostgreSQL兼容")
    public void testLimitPreserved() {
        String sql = "SELECT * FROM test_table ORDER BY id LIMIT 5";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        // 考虑格式化后的多行结构
        assertTrue(result.contains("SELECT *"));
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("test_table"));  // 金仓保持普通标识符不变
        assertTrue(result.contains("ORDER BY id"));  // 金仓保持普通标识符不变
        assertTrue(result.contains("LIMIT 5"));  // 金仓原生支持LIMIT
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试LIMIT OFFSET语句保持不变 - 金仓PostgreSQL兼容")
    public void testLimitOffsetPreserved() {
        String sql = "SELECT * FROM test_table ORDER BY id LIMIT 3 OFFSET 2";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        // 考虑格式化后的多行结构
        assertTrue(result.contains("SELECT *"));
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("test_table"));  // 金仓保持普通标识符不变
        assertTrue(result.contains("ORDER BY id"));  // 金仓保持普通标识符不变
        assertTrue(result.contains("LIMIT 3"));  // 金仓原生支持LIMIT
        assertTrue(result.contains("OFFSET 2"));  // 金仓原生支持OFFSET
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试复杂查询的分页保持 - 金仓PostgreSQL兼容")
    public void testComplexQueryPagination() {
        String sql = "SELECT `id`, `name`, `email` FROM `users` WHERE `status` = 'active' ORDER BY `created_date` DESC LIMIT 10 OFFSET 20";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);
        
        assertTrue(result.contains("SELECT"));
        assertTrue(result.contains("\"id\", \"name\", \"email\""));  // 反引号转双引号
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("\"users\""));
        assertTrue(result.contains("WHERE \"status\" = 'active'"));
        assertTrue(result.contains("ORDER BY \"created_date\" DESC"));
        assertTrue(result.contains("LIMIT 10"));  // 金仓原生支持LIMIT
        assertTrue(result.contains("OFFSET 20"));  // 金仓原生支持OFFSET
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试金仓数据库官方文档示例")
    public void testKingbaseOfficialExample() {
        // 根据金仓官方文档的PostgreSQL兼容分页示例
        String sql = "SELECT * FROM employees ORDER BY hire_date LIMIT 10 OFFSET 5";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        // 考虑格式化后的多行结构
        assertTrue(result.contains("SELECT *"));
        assertTrue(result.contains("FROM"));
        // 金仓数据库基于PostgreSQL，对于没有反引号的标识符，保持原样
        assertTrue(result.contains("employees"));  // 普通标识符保持原样
        assertTrue(result.contains("ORDER BY hire_date"));
        assertTrue(result.contains("LIMIT 10"));  // 金仓原生支持LIMIT
        assertTrue(result.contains("OFFSET 5"));  // 金仓原生支持OFFSET
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试JOIN查询的分页 - 金仓PostgreSQL兼容")
    public void testJoinQueryPagination() {
        String sql = "SELECT u.`name`, p.`title` FROM `users` u JOIN `posts` p ON u.`id` = p.`user_id` ORDER BY p.`created_date` LIMIT 5 OFFSET 10";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);
        
        assertTrue(result.contains("SELECT"));
        assertTrue(result.contains("u.\"name\", p.\"title\""));  // 反引号转双引号
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("\"users\" u"));
        assertTrue(result.contains("JOIN \"posts\" p"));
        assertTrue(result.contains("ON u.\"id\" = p.\"user_id\""));
        assertTrue(result.contains("ORDER BY p.\"created_date\""));
        assertTrue(result.contains("LIMIT 5"));  // 金仓原生支持LIMIT
        assertTrue(result.contains("OFFSET 10"));  // 金仓原生支持OFFSET
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试聚合查询的分页 - 金仓PostgreSQL兼容")
    public void testAggregateQueryPagination() {
        String sql = "SELECT `department`, COUNT(*) as `count` FROM `employees` GROUP BY `department` HAVING COUNT(*) > 5 ORDER BY `count` DESC LIMIT 3";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);
        
        assertTrue(result.contains("SELECT"));
        assertTrue(result.contains("\"department\", COUNT(*) as \"count\""));  // 反引号转双引号
        assertTrue(result.contains("FROM \"employees\""));
        assertTrue(result.contains("GROUP BY \"department\""));
        assertTrue(result.contains("HAVING COUNT(*) > 5"));
        assertTrue(result.contains("ORDER BY \"count\" DESC"));
        assertTrue(result.contains("LIMIT 3"));  // 金仓原生支持
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试没有分页的查询保持不变")
    public void testQueryWithoutPaginationUnchanged() {
        String sql = "SELECT * FROM test_table ORDER BY id";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        // 金仓数据库基于PostgreSQL，对于没有反引号的标识符，保持原样
        // 考虑格式化后的多行结构
        assertTrue(result.contains("SELECT *"));
        assertTrue(result.contains("FROM test_table"));
        assertTrue(result.contains("ORDER BY id"));
        assertFalse(result.contains("LIMIT"));
        assertFalse(result.contains("OFFSET"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试PostgreSQL风格的分页语法")
    public void testPostgreSQLStylePagination() {
        // 金仓数据库完全支持PostgreSQL的分页语法
        String sql = "SELECT * FROM products WHERE price > 100 ORDER BY price LIMIT 20 OFFSET 40";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);

        // 金仓数据库基于PostgreSQL，对于没有反引号的标识符，保持原样
        // 考虑格式化后的多行结构
        assertTrue(result.contains("SELECT *"));
        assertTrue(result.contains("FROM products"));
        assertTrue(result.contains("WHERE price > 100"));
        assertTrue(result.contains("ORDER BY price"));
        assertTrue(result.contains("LIMIT 20"));  // 完全兼容PostgreSQL LIMIT
        assertTrue(result.contains("OFFSET 40"));  // 完全兼容PostgreSQL OFFSET
        assertTrue(result.endsWith(";"));
    }
}
