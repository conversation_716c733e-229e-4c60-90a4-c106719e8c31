package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 神通数据库标识符和关键字处理测试
 * 基于神通官方文档第2.2节的测试驱动开发
 * 参考神通数据库官方文档：标识符命名规则、分隔标识符、大小写处理等
 */
public class ShentongIdentifierTest {

    private ShentongGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new ShentongGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试反引号转换为双引号 - 神通官方文档标准")
    public void testBacktickToDoubleQuoteConversion() {
        String sql = "CREATE TABLE `user_table` (" +
                    "`user_id` INT PRIMARY KEY, " +
                    "`user_name` VARCHAR(100), " +
                    "`create_time` TIMESTAMP" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"user_table\""));
        assertTrue(result.contains("\"user_id\""));
        assertTrue(result.contains("\"user_name\""));
        assertTrue(result.contains("\"create_time\""));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试分隔标识符处理 - 双引号保持")
    public void testDelimitedIdentifierHandling() {
        // 使用反引号，因为MySQL解析器需要反引号，然后转换为双引号
        String sql = "CREATE TABLE `User Table` (" +
                    "`User ID` INT, " +
                    "`User Name` VARCHAR(100)" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"User Table\""));
        assertTrue(result.contains("\"User ID\""));
        assertTrue(result.contains("\"User Name\""));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试关键字作为标识符 - 需要双引号")
    public void testKeywordAsIdentifier() {
        String sql = "CREATE TABLE `select` (" +
                    "`from` INT, " +
                    "`where` VARCHAR(50), " +
                    "`order` TIMESTAMP" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"select\""));
        assertTrue(result.contains("\"from\""));
        assertTrue(result.contains("\"where\""));
        assertTrue(result.contains("\"order\""));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试特殊字符标识符处理")
    public void testSpecialCharacterIdentifiers() {
        String sql = "CREATE TABLE `user@table` (" +
                    "`user#id` INT, " +
                    "`user$name` VARCHAR(100), " +
                    "`user-email` VARCHAR(200)" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"user@table\""));
        assertTrue(result.contains("\"user#id\""));
        assertTrue(result.contains("\"user$name\""));
        assertTrue(result.contains("\"user-email\""));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试双引号转义处理")
    public void testDoubleQuoteEscaping() {
        // 这个测试验证内部双引号的正确转义
        String sql = "CREATE TABLE test_table (" +
                    "column1 VARCHAR(100) COMMENT 'Contains \"quotes\"'" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"test_table\""));
        assertTrue(result.contains("\"column1\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试大小写处理 - 神通数据库规则")
    public void testCaseHandling() {
        // 根据神通官方文档，普通标识符大小写无关，分隔标识符大小写相关
        String sql = "CREATE TABLE MixedCase (" +
                    "ColumnName INT, " +
                    "`CaseSensitive` VARCHAR(100)" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"MixedCase\""));
        assertTrue(result.contains("\"ColumnName\""));
        assertTrue(result.contains("\"CaseSensitive\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试查询语句中的标识符转换")
    public void testIdentifierConversionInQuery() {
        String sql = "SELECT `user_id`, `user_name` FROM `user_table` WHERE `status` = 'active'";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        String result = generator.generate(queryStmt);
        
        assertTrue(result.contains("SELECT"));
        assertTrue(result.contains("\"user_id\""));
        assertTrue(result.contains("\"user_name\""));
        assertTrue(result.contains("FROM"));
        assertTrue(result.contains("\"user_table\""));
        assertTrue(result.contains("WHERE"));
        assertTrue(result.contains("\"status\""));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试复杂标识符场景")
    public void testComplexIdentifierScenarios() {
        String sql = "CREATE TABLE `order_details` (" +
                    "`order_id` INT, " +
                    "`product_name` VARCHAR(200), " +
                    "`unit_price` DECIMAL(10,2), " +
                    "`order_date` DATE, " +
                    "INDEX `idx_order_date` (`order_date`), " +
                    "FOREIGN KEY (`order_id`) REFERENCES `orders`(`id`)" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"order_details\""));
        assertTrue(result.contains("\"order_id\""));
        assertTrue(result.contains("\"product_name\""));
        assertTrue(result.contains("\"unit_price\""));
        assertTrue(result.contains("\"order_date\""));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试神通数据库系统保留前缀")
    public void testShentongSystemPrefixes() {
        // 根据神通官方文档，系统表以SYS_开头，系统视图以V_SYS_开头
        // 用户定义的对象不应该以这些前缀开头
        String sql = "CREATE TABLE `user_sys_table` (" +
                    "`sys_column` INT, " +
                    "`v_sys_column` VARCHAR(100)" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"user_sys_table\""));
        assertTrue(result.contains("\"sys_column\""));
        assertTrue(result.contains("\"v_sys_column\""));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试神通数据库官方文档示例")
    public void testShentongOfficialExample() {
        // 根据神通官方文档的标识符示例
        String sql = "CREATE TABLE `employee_info` (" +
                    "`emp_id` BIGINT PRIMARY KEY, " +
                    "`emp_name` VARCHAR(127), " +  // 神通标识符最大长度127
                    "`department` VARCHAR(100), " +
                    "`hire_date` DATE, " +
                    "`salary` DECIMAL(10,2)" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"employee_info\""));
        assertTrue(result.contains("\"emp_id\""));
        assertTrue(result.contains("\"emp_name\""));
        assertTrue(result.contains("\"department\""));
        assertTrue(result.contains("\"hire_date\""));
        assertTrue(result.contains("\"salary\""));
        assertTrue(result.contains("PRIMARY KEY"));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试标识符长度限制处理")
    public void testIdentifierLengthHandling() {
        // 神通数据库标识符长度限制为127字符
        String longIdentifier = "a".repeat(120); // 120字符的标识符
        String sql = "CREATE TABLE `" + longIdentifier + "` (" +
                    "`id` INT PRIMARY KEY" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"" + longIdentifier + "\""));
        assertTrue(result.contains("\"id\""));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试混合引用方式处理")
    public void testMixedQuotingStyles() {
        // 测试同时包含反引号和双引号的情况
        String sql = "CREATE TABLE `mixed_table` (" +
                    "`backtick_column` INT, " +
                    "normal_column VARCHAR(100)" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"mixed_table\""));
        assertTrue(result.contains("\"backtick_column\""));
        assertTrue(result.contains("\"normal_column\""));
        assertFalse(result.contains("`")); // 确保反引号被转换
        assertTrue(result.endsWith(";"));
    }
}
