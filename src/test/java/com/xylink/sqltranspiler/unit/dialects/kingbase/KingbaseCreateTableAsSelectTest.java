package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.create.CreateTableAsSelect;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金仓数据库CREATE TABLE AS SELECT功能测试
 * 基于金仓官方文档的测试驱动开发
 * 参考：https://help.kingbase.com.cn/v8/development/sql-plsql/sql/SQL_Statements_3.html#create-table-as
 */
public class KingbaseCreateTableAsSelectTest {

    private KingbaseGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new KingbaseGenerator();
    }

    @Test
    @DisplayName("测试基本CREATE TABLE AS SELECT语句 - PostgreSQL兼容")
    public void testBasicCreateTableAsSelect() {
        TableId tableId = new TableId("employee_backup");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT * FROM employees");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"employee_backup\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT * FROM employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试CREATE TABLE IF NOT EXISTS AS SELECT语句 - PostgreSQL兼容")
    public void testCreateTableIfNotExistsAsSelect() {
        TableId tableId = new TableId("safe_backup");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT id, name FROM employees WHERE active = true");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, true, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("IF NOT EXISTS"));
        assertTrue(result.contains("\"safe_backup\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT id, name FROM employees WHERE active = true"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带列定义的CREATE TABLE AS SELECT语句")
    public void testCreateTableAsSelectWithColumns() {
        TableId tableId = new TableId("employee_summary");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT id, name, salary FROM employees");
        
        List<ColumnRel> columnRels = Arrays.asList(
            new ColumnRel("emp_id", null, null),
            new ColumnRel("emp_name", null, null),
            new ColumnRel("emp_salary", null, null)
        );
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        ctas.setColumnRels(columnRels);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"employee_summary\""));
        assertTrue(result.contains("(\"emp_id\", \"emp_name\", \"emp_salary\")"));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT id, name, salary FROM employees"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试空查询的CREATE TABLE AS SELECT语句 - PostgreSQL兼容")
    public void testCreateTableAsSelectEmptyQuery() {
        TableId tableId = new TableId("empty_table");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(""); // 空查询
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"empty_table\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT 1 WHERE FALSE")); // PostgreSQL兼容的空表创建
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试金仓数据库PostgreSQL兼容的CTAS语法")
    public void testKingbasePostgreSQLCompatibility() {
        // 根据金仓官方文档，金仓数据库完全兼容PostgreSQL的CREATE TABLE AS SELECT语法
        TableId tableId = new TableId("postgresql_style_table");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT generate_series(1, 100) as id, 'test' || generate_series(1, 100) as name");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"postgresql_style_table\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT generate_series"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试金仓数据库MySQL兼容特性")
    public void testKingbaseMySQLCompatibility() {
        // 金仓数据库支持MySQL兼容的CREATE TABLE AS SELECT语法
        TableId tableId = new TableId("mysql_compat_table");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT id, CONCAT(first_name, ' ', last_name) as full_name FROM users");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"mysql_compat_table\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT id, CONCAT"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带反引号的CREATE TABLE AS SELECT语句")
    public void testCreateTableAsSelectWithBackticks() {
        TableId tableId = new TableId("user_backup");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT `id`, `name`, `email` FROM `users` WHERE `status` = 'active'");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"user_backup\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT \"id\", \"name\", \"email\" FROM \"users\""));
        assertFalse(result.contains("`")); // 确保反引号被转换为双引号
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试金仓数据库官方文档示例")
    public void testKingbaseOfficialExample() {
        // 根据金仓官方文档的CREATE TABLE AS SELECT示例
        TableId tableId = new TableId("sales_report");
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql("SELECT region, SUM(sales_amount) as total_sales, COUNT(*) as order_count " +
                         "FROM orders WHERE order_date >= CURRENT_DATE - INTERVAL '30 days' " +
                         "GROUP BY region ORDER BY total_sales DESC");
        
        CreateTableAsSelect ctas = new CreateTableAsSelect(tableId, queryStmt, null, false, null);
        String result = generator.generate(ctas);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"sales_report\""));
        assertTrue(result.contains("AS"));
        assertTrue(result.contains("SELECT region, SUM(sales_amount)"));
        assertTrue(result.contains("GROUP BY"));
        assertTrue(result.contains("ORDER BY"));
        assertTrue(result.endsWith(";"));
        
        // 验证生成的SQL格式正确，符合PostgreSQL标准
        assertTrue(result.startsWith("CREATE TABLE"));
        assertTrue(result.contains(" AS "));
    }
}
