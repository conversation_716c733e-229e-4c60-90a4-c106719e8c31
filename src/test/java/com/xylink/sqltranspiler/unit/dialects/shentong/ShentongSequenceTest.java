package com.xylink.sqltranspiler.unit.dialects.shentong;

import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.alter.AlterSequence;
import com.xylink.sqltranspiler.core.ast.create.CreateSequence;
import com.xylink.sqltranspiler.core.ast.drop.DropSequence;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 神通数据库序列功能测试
 * 基于神通官方文档的测试驱动开发
 * 参考神通数据库官方文档：神通数据库支持序列对象
 */
public class ShentongSequenceTest {

    private ShentongGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new ShentongGenerator();
    }

    @Test
    @DisplayName("测试基本CREATE SEQUENCE语句")
    public void testBasicCreateSequence() {
        TableId sequenceId = new TableId("test_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId);
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("\"test_seq\""));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试带数据类型的CREATE SEQUENCE语句 - PostgreSQL兼容")
    public void testCreateSequenceWithDataType() {
        TableId sequenceId = new TableId("typed_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId, 1L, 1L, 1L, 999999L, 20L, false, false, "INTEGER");
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("\"typed_seq\""));
        assertTrue(result.contains("AS INTEGER"));
        assertTrue(result.contains("START WITH 1"));
        assertTrue(result.contains("INCREMENT BY 1"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试ALTER SEQUENCE语句")
    public void testAlterSequence() {
        TableId sequenceId = new TableId("test_seq");
        AlterSequence alterSequence = new AlterSequence(sequenceId, 100L, 2L, 1L, 9999L, 50L, false, "table.column");
        String result = generator.generate(alterSequence);
        
        assertTrue(result.contains("ALTER SEQUENCE"));
        assertTrue(result.contains("\"test_seq\""));
        assertTrue(result.contains("RESTART WITH 100"));
        assertTrue(result.contains("INCREMENT BY 2"));
        assertTrue(result.contains("NO CYCLE"));
        assertTrue(result.contains("OWNED BY table.column"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP SEQUENCE RESTRICT语句")
    public void testDropSequenceRestrict() {
        TableId sequenceId = new TableId("restrict_seq");
        List<TableId> sequenceIds = Arrays.asList(sequenceId);
        DropSequence dropSequence = new DropSequence(sequenceIds, true, false, true);
        String result = generator.generate(dropSequence);
        
        assertTrue(result.contains("DROP SEQUENCE"));
        assertTrue(result.contains("IF EXISTS"));
        assertTrue(result.contains("\"restrict_seq\""));
        assertTrue(result.contains("RESTRICT"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试神通数据库PLOSCAR语言兼容的序列")
    public void testShentongPLOSCARCompatibility() {
        // 神通数据库支持PLOSCAR语言和PostgreSQL兼容语法
        TableId sequenceId = new TableId("ploscar_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId, 1L, 1L);
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("\"ploscar_seq\""));
        assertTrue(result.contains("START WITH 1"));
        assertTrue(result.contains("INCREMENT BY 1"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试神通数据库PostgreSQL兼容特性")
    public void testShentongPostgreSQLCompatibility() {
        // 神通数据库支持PostgreSQL兼容的序列功能
        TableId sequenceId = new TableId("postgresql_seq");
        CreateSequence createSequence = new CreateSequence(sequenceId, 1000L, 10L, 1L, 999999L, 100L, true, false, "BIGINT");
        String result = generator.generate(createSequence);
        
        assertTrue(result.contains("CREATE SEQUENCE"));
        assertTrue(result.contains("\"postgresql_seq\""));
        assertTrue(result.contains("AS BIGINT"));
        assertTrue(result.contains("START WITH 1000"));
        assertTrue(result.contains("INCREMENT BY 10"));
        assertTrue(result.contains("CYCLE"));
        assertTrue(result.endsWith(";"));
    }
}
