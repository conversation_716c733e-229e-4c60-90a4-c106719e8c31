package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 达梦数据库CTE（公共表表达式）测试
 * 
 * 根据达梦官方文档，测试MySQL CTE相关功能到达梦的转换
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * 
 * 测试覆盖：
 * - WITH子句的支持
 * - 递归CTE的转换
 * - 复杂查询中的CTE使用
 * - CTE在CREATE TABLE AS SELECT中的应用
 */
@DisplayName("达梦数据库CTE测试")
public class DamengCteTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本CTE支持的表结构")
    void testBasicCteTableStructure() {
        String mysqlSql = "CREATE TABLE cte_test_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "parent_id INT, " +
                "name VARCHAR(100) NOT NULL, " +
                "level INT DEFAULT 0, " +
                "path VARCHAR(500), " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (parent_id) REFERENCES cte_test_data(id)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("基本CTE支持的表结构转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证CTE支持的表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("cte_test_data"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("parent_id"), "应保留parent_id列");
        assertTrue(damengSql.contains("name"), "应保留name列");
        assertTrue(damengSql.contains("level"), "应保留level列");
        assertTrue(damengSql.contains("path"), "应保留path列");
        assertTrue(damengSql.contains("created_at"), "应保留created_at列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("VARCHAR(500)"), "应保留路径字段类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
        
        // 验证自引用外键处理
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("parent_id"), "应处理自引用外键");
    }

    @Test
    @DisplayName("测试递归查询支持的表结构")
    void testRecursiveQueryTableStructure() {
        String mysqlSql = "CREATE TABLE hierarchy_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "parent_id INT, " +
                "name VARCHAR(100) NOT NULL, " +
                "depth INT DEFAULT 0, " +
                "sort_order INT DEFAULT 0, " +
                "is_active BOOLEAN DEFAULT TRUE, " +
                "metadata JSON, " +
                "INDEX idx_parent (parent_id), " +
                "INDEX idx_depth (depth), " +
                "FOREIGN KEY (parent_id) REFERENCES hierarchy_data(id) ON DELETE CASCADE" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("递归查询支持的表结构转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证递归查询支持的表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("hierarchy_data"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("parent_id"), "应保留parent_id列");
        assertTrue(damengSql.contains("name"), "应保留name列");
        assertTrue(damengSql.contains("depth"), "应保留depth列");
        assertTrue(damengSql.contains("sort_order"), "应保留sort_order列");
        assertTrue(damengSql.contains("is_active"), "应保留is_active列");
        assertTrue(damengSql.contains("metadata"), "应保留metadata列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("JSON"), "应保留JSON类型");
        
        // 验证索引处理
        assertTrue(damengSql.contains("idx_parent") || damengSql.contains("parent_id"), 
                  "应处理parent索引");
        assertTrue(damengSql.contains("idx_depth") || damengSql.contains("depth"), 
                  "应处理depth索引");
        
        // 验证级联外键处理
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("CASCADE") || damengSql.contains("parent_id"), 
                  "应处理级联外键");
    }

    @Test
    @DisplayName("测试WITH子句相关的表结构")
    void testWithClauseTableStructure() {
        String mysqlSql = "CREATE TABLE with_clause_test (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "category_id INT NOT NULL, " +
                "subcategory_id INT, " +
                "name VARCHAR(200) NOT NULL, " +
                "value DECIMAL(15,4), " +
                "calculation_result DECIMAL(20,6), " +
                "aggregated_data TEXT, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (category_id) REFERENCES categories(id), " +
                "FOREIGN KEY (subcategory_id) REFERENCES subcategories(id) ON DELETE SET NULL" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("WITH子句相关的表结构转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证WITH子句相关的表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("with_clause_test"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("category_id"), "应保留category_id列");
        assertTrue(damengSql.contains("subcategory_id"), "应保留subcategory_id列");
        assertTrue(damengSql.contains("name"), "应保留name列");
        assertTrue(damengSql.contains("value"), "应保留value列");
        assertTrue(damengSql.contains("calculation_result"), "应保留calculation_result列");
        assertTrue(damengSql.contains("aggregated_data"), "应保留aggregated_data列");
        assertTrue(damengSql.contains("created_at"), "应保留created_at列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("VARCHAR(200)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("DECIMAL(15,4)"), "应保留高精度DECIMAL类型");
        assertTrue(damengSql.contains("DECIMAL(20,6)"), "应保留计算结果DECIMAL类型");
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
        
        // 验证外键处理
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("category_id"), "应处理外键约束");
    }

    @Test
    @DisplayName("测试复杂CTE场景的表结构")
    void testComplexCteScenarioTableStructure() {
        String mysqlSql = "CREATE TABLE complex_cte_scenario (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "organization_id INT NOT NULL, " +
                "department_id INT, " +
                "employee_id INT, " +
                "manager_id INT, " +
                "hierarchy_level INT DEFAULT 0, " +
                "hierarchy_path VARCHAR(1000), " +
                "total_subordinates INT DEFAULT 0, " +
                "salary_budget DECIMAL(18,2), " +
                "performance_score DECIMAL(5,2), " +
                "last_calculated TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "INDEX idx_org_dept (organization_id, department_id), " +
                "INDEX idx_manager (manager_id), " +
                "INDEX idx_hierarchy (hierarchy_level), " +
                "FOREIGN KEY (organization_id) REFERENCES organizations(id), " +
                "FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL, " +
                "FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (manager_id) REFERENCES employees(id) ON DELETE SET NULL" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("复杂CTE场景的表结构转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证复杂CTE场景的表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("complex_cte_scenario"), "应保留表名");
        assertTrue(damengSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("organization_id"), "应保留organization_id列");
        assertTrue(damengSql.contains("department_id"), "应保留department_id列");
        assertTrue(damengSql.contains("employee_id"), "应保留employee_id列");
        assertTrue(damengSql.contains("manager_id"), "应保留manager_id列");
        assertTrue(damengSql.contains("hierarchy_level"), "应保留hierarchy_level列");
        assertTrue(damengSql.contains("hierarchy_path"), "应保留hierarchy_path列");
        assertTrue(damengSql.contains("total_subordinates"), "应保留total_subordinates列");
        assertTrue(damengSql.contains("salary_budget"), "应保留salary_budget列");
        assertTrue(damengSql.contains("performance_score"), "应保留performance_score列");
        assertTrue(damengSql.contains("last_calculated"), "应保留last_calculated列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("VARCHAR(1000)"), "应保留长VARCHAR类型");
        assertTrue(damengSql.contains("DECIMAL(18,2)"), "应保留财务DECIMAL类型");
        assertTrue(damengSql.contains("DECIMAL(5,2)"), "应保留评分DECIMAL类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
        
        // 验证复合索引处理
        assertTrue(damengSql.contains("idx_org_dept") || damengSql.contains("organization_id"), 
                  "应处理复合索引");
        assertTrue(damengSql.contains("idx_manager") || damengSql.contains("manager_id"), 
                  "应处理manager索引");
        assertTrue(damengSql.contains("idx_hierarchy") || damengSql.contains("hierarchy_level"), 
                  "应处理hierarchy索引");
        
        // 验证多重外键处理
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("organization_id"), "应处理多重外键约束");
    }

    @Test
    @DisplayName("测试达梦特有的CTE支持特性")
    void testDamengSpecificCteFeatures() {
        String mysqlSql = "CREATE TABLE dameng_cte_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "oracle_number NUMBER(20,4), " +
                "oracle_varchar2 VARCHAR2(300), " +
                "mysql_text TEXT, " +
                "mysql_blob BLOB, " +
                "tree_level INT DEFAULT 0, " +
                "tree_path CLOB, " +
                "aggregated_value NUMBER(25,8), " +
                "calculation_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦特有CTE支持特性:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证达梦特有CTE支持特性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("dameng_cte_features"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        
        // 验证Oracle兼容类型在CTE中的支持
        assertTrue(damengSql.contains("NUMBER(20,4)"), "应支持Oracle NUMBER类型");
        assertTrue(damengSql.contains("VARCHAR2(300)"), "应支持Oracle VARCHAR2类型");
        assertTrue(damengSql.contains("NUMBER(25,8)"), "应支持高精度NUMBER类型");
        
        // 验证MySQL类型转换在CTE中的处理
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        assertTrue(damengSql.contains("BLOB"), "应保留BLOB类型");
        
        // 验证CTE相关字段
        assertTrue(damengSql.contains("tree_level"), "应保留tree_level列");
        assertTrue(damengSql.contains("tree_path"), "应保留tree_path列");
        assertTrue(damengSql.contains("aggregated_value"), "应保留aggregated_value列");
        assertTrue(damengSql.contains("calculation_timestamp"), "应保留calculation_timestamp列");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
    }

    @Test
    @DisplayName("测试CTE窗口函数支持的表结构")
    void testCteWindowFunctionTableStructure() {
        String mysqlSql = "CREATE TABLE cte_window_function_data (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "group_id INT NOT NULL, " +
                "sequence_number INT, " +
                "value DECIMAL(12,4), " +
                "running_total DECIMAL(20,4), " +
                "rank_value INT, " +
                "percentile_value DECIMAL(8,4), " +
                "lag_value DECIMAL(12,4), " +
                "lead_value DECIMAL(12,4), " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "INDEX idx_group_seq (group_id, sequence_number), " +
                "INDEX idx_value (value)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("CTE窗口函数支持的表结构转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证CTE窗口函数支持的表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("cte_window_function_data"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("group_id"), "应保留group_id列");
        assertTrue(damengSql.contains("sequence_number"), "应保留sequence_number列");
        assertTrue(damengSql.contains("value"), "应保留value列");
        assertTrue(damengSql.contains("running_total"), "应保留running_total列");
        assertTrue(damengSql.contains("rank_value"), "应保留rank_value列");
        assertTrue(damengSql.contains("percentile_value"), "应保留percentile_value列");
        assertTrue(damengSql.contains("lag_value"), "应保留lag_value列");
        assertTrue(damengSql.contains("lead_value"), "应保留lead_value列");
        assertTrue(damengSql.contains("created_at"), "应保留created_at列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("DECIMAL(12,4)"), "应保留DECIMAL类型");
        assertTrue(damengSql.contains("DECIMAL(20,4)"), "应保留累计DECIMAL类型");
        assertTrue(damengSql.contains("DECIMAL(8,4)"), "应保留百分比DECIMAL类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
        
        // 验证窗口函数相关索引处理
        assertTrue(damengSql.contains("idx_group_seq") || damengSql.contains("group_id"), 
                  "应处理分组序列索引");
        assertTrue(damengSql.contains("idx_value") || damengSql.contains("value"), 
                  "应处理值索引");
    }
}
