package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * 神通数据库索引转换综合测试
 *
 * 测试CREATE INDEX语句的完整功能，包括：
 * 1. Schema保留问题修复验证
 * 2. 各种索引类型的转换
 * 3. 复杂场景的处理
 *
 * 基于神通数据库官方文档：shentong.md
 *
 * 根据神通数据库官方文档第2.8节对象的命名规则：
 * - 模式对象可以通过SQL语句访问，不同的模式下可以有同名的对象
 * - 访问时可以为它们加上模式前缀来区分，如sch1.tab和sch2.tab
 * - 表、视图、序列和索引在神通数据库中属于同一名称空间
 */
public class ShentongIndexIntegrationTest {

    private static final Logger log = LoggerFactory.getLogger(ShentongIndexIntegrationTest.class);
    
    private Transpiler transpiler;
    
    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }
    
    @Test
    @DisplayName("测试CREATE INDEX语句中schema不应该丢失")
    void testCreateIndexWithSchemaPreservation() {
        // 测试用例：包含schema的CREATE INDEX语句
        String mysqlSql = "create index idx_charge_conf_config_configname on charge.libra_charge_conference_config(config_name);";
        
        log.info("测试MySQL SQL: {}", mysqlSql);
        
        // 转换为神通数据库SQL
        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "shentong");
        
        // 验证转换结果
        assertTrue(result.successCount() > 0, "转换应该成功");
        assertFalse(result.translatedSql() == null || result.translatedSql().trim().isEmpty(), 
                   "转换结果不应为空");
        
        String shentongSql = result.translatedSql();
        log.info("神通数据库SQL: {}", shentongSql);
        
        // 关键验证：schema不应该丢失
        assertTrue(shentongSql.contains("charge"), 
                  "转换后的SQL应该包含schema名称 'charge'，但实际结果: " + shentongSql);
        
        // 验证完整的表引用格式
        assertTrue(shentongSql.contains("\"charge\".\"libra_charge_conference_config\"") || 
                  shentongSql.contains("charge.libra_charge_conference_config"),
                  "转换后的SQL应该包含完整的schema.table引用，但实际结果: " + shentongSql);
        
        // 验证索引名称正确转换
        assertTrue(shentongSql.contains("idx_charge_conf_config_configname"), 
                  "转换后的SQL应该包含索引名称，但实际结果: " + shentongSql);
        
        // 验证列名正确转换
        assertTrue(shentongSql.contains("config_name"), 
                  "转换后的SQL应该包含列名，但实际结果: " + shentongSql);
    }
    
    @Test
    @DisplayName("测试CREATE UNIQUE INDEX语句中schema不应该丢失")
    void testCreateUniqueIndexWithSchemaPreservation() {
        // 测试用例：包含schema的CREATE UNIQUE INDEX语句
        String mysqlSql = "CREATE UNIQUE INDEX uk_user_email ON user_db.users(email);";
        
        log.info("测试MySQL SQL: {}", mysqlSql);
        
        // 转换为神通数据库SQL
        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "shentong");
        
        // 验证转换结果
        assertTrue(result.successCount() > 0, "转换应该成功");
        
        String shentongSql = result.translatedSql();
        log.info("神通数据库SQL: {}", shentongSql);
        
        // 关键验证：schema不应该丢失
        assertTrue(shentongSql.contains("user_db"), 
                  "转换后的SQL应该包含schema名称 'user_db'，但实际结果: " + shentongSql);
        
        // 验证UNIQUE关键字保留
        assertTrue(shentongSql.toUpperCase().contains("UNIQUE"), 
                  "转换后的SQL应该包含UNIQUE关键字，但实际结果: " + shentongSql);
    }
    
    @Test
    @DisplayName("测试无schema的CREATE INDEX语句正常工作")
    void testCreateIndexWithoutSchema() {
        // 测试用例：不包含schema的CREATE INDEX语句
        String mysqlSql = "CREATE INDEX idx_name ON users(name);";
        
        log.info("测试MySQL SQL: {}", mysqlSql);
        
        // 转换为神通数据库SQL
        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "shentong");
        
        // 验证转换结果
        assertTrue(result.successCount() > 0, "转换应该成功");
        
        String shentongSql = result.translatedSql();
        log.info("神通数据库SQL: {}", shentongSql);
        
        // 验证基本转换正确
        assertTrue(shentongSql.toUpperCase().contains("CREATE INDEX"), 
                  "转换后的SQL应该包含CREATE INDEX，但实际结果: " + shentongSql);
        assertTrue(shentongSql.contains("idx_name"), 
                  "转换后的SQL应该包含索引名称，但实际结果: " + shentongSql);
        assertTrue(shentongSql.contains("users"), 
                  "转换后的SQL应该包含表名，但实际结果: " + shentongSql);
        assertTrue(shentongSql.contains("name"), 
                  "转换后的SQL应该包含列名，但实际结果: " + shentongSql);
    }
    
    @Test
    @DisplayName("测试复杂schema名称的CREATE INDEX语句")
    void testCreateIndexWithComplexSchema() {
        // 测试用例：复杂schema名称的CREATE INDEX语句
        String mysqlSql = "CREATE INDEX idx_order_status ON order_management.order_details(status, created_date);";

        log.info("测试MySQL SQL: {}", mysqlSql);

        // 转换为神通数据库SQL
        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "shentong");

        // 验证转换结果
        assertTrue(result.successCount() > 0, "转换应该成功");

        String shentongSql = result.translatedSql();
        log.info("神通数据库SQL: {}", shentongSql);

        // 关键验证：复杂schema名称不应该丢失
        assertTrue(shentongSql.contains("order_management"),
                  "转换后的SQL应该包含schema名称 'order_management'，但实际结果: " + shentongSql);

        // 验证多列索引正确转换
        assertTrue(shentongSql.contains("status") && shentongSql.contains("created_date"),
                  "转换后的SQL应该包含所有列名，但实际结果: " + shentongSql);
    }

    // ========== 原始问题修复验证测试 ==========

    @Test
    @DisplayName("验证原始问题语句的修复")
    void testOriginalProblemStatementFixed() {
        // 原始问题语句
        String originalSql = "create index idx_charge_conf_config_configname on charge.libra_charge_conference_config(config_name);";

        log.info("原始MySQL SQL: {}", originalSql);

        // 转换为神通数据库SQL
        TranspilationResult result = transpiler.transpile(originalSql, "mysql", "shentong");

        // 验证转换成功
        assertTrue(result.successCount() > 0, "转换应该成功");

        String shentongSql = result.translatedSql();
        log.info("修复后的神通数据库SQL: {}", shentongSql);

        // 关键验证：schema不应该丢失
        assertTrue(shentongSql.contains("charge"),
                  "修复后的SQL应该包含schema名称 'charge'");

        // 验证完整的schema.table格式
        assertTrue(shentongSql.contains("\"charge\".\"libra_charge_conference_config\""),
                  "修复后的SQL应该包含完整的schema.table引用格式");

        // 验证其他元素正确保留
        assertTrue(shentongSql.contains("idx_charge_conf_config_configname"),
                  "索引名称应该正确保留");
        assertTrue(shentongSql.contains("config_name"),
                  "列名应该正确保留");
        assertTrue(shentongSql.toUpperCase().contains("CREATE INDEX"),
                  "CREATE INDEX关键字应该正确保留");

        // 验证神通数据库的双引号格式
        assertTrue(shentongSql.contains("\"idx_charge_conf_config_configname\""),
                  "索引名称应该使用双引号格式");
        assertTrue(shentongSql.contains("\"config_name\""),
                  "列名应该使用双引号格式");

        log.info("✅ 原始问题已成功修复！schema不再丢失");
    }

    @Test
    @DisplayName("验证多种schema格式的CREATE INDEX语句")
    void testVariousSchemaFormats() {
        String[] testCases = {
            // 简单schema
            "CREATE INDEX idx_test ON db1.table1(col1);",
            // 复杂schema名称
            "CREATE INDEX idx_complex ON my_database.my_table(column_name);",
            // 带下划线的schema
            "CREATE INDEX idx_underscore ON user_db.user_table(user_id);",
            // 多列索引
            "CREATE INDEX idx_multi ON schema1.table1(col1, col2, col3);",
            // UNIQUE索引
            "CREATE UNIQUE INDEX uk_unique ON test_schema.test_table(unique_col);"
        };

        for (String testSql : testCases) {
            log.info("测试SQL: {}", testSql);

            TranspilationResult result = transpiler.transpile(testSql, "mysql", "shentong");
            assertTrue(result.successCount() > 0, "转换应该成功: " + testSql);

            String convertedSql = result.translatedSql();
            log.info("转换结果: {}", convertedSql);

            // 提取schema名称进行验证
            String schemaName = extractSchemaName(testSql);
            if (schemaName != null) {
                assertTrue(convertedSql.contains(schemaName),
                          "转换后的SQL应该包含schema名称 '" + schemaName + "': " + convertedSql);

                // 验证schema.table格式
                assertTrue(convertedSql.contains("\"" + schemaName + "\"."),
                          "转换后的SQL应该包含正确的schema.table格式: " + convertedSql);
            }
        }

        log.info("✅ 所有schema格式测试通过！");
    }

    /**
     * 从SQL语句中提取schema名称
     */
    private String extractSchemaName(String sql) {
        // 简单的正则匹配提取schema名称
        String upperSql = sql.toUpperCase();
        int onIndex = upperSql.indexOf(" ON ");
        if (onIndex == -1) return null;

        String tablePart = sql.substring(onIndex + 4).trim();
        int dotIndex = tablePart.indexOf('.');
        if (dotIndex == -1) return null;

        String schemaName = tablePart.substring(0, dotIndex).trim();
        // 移除可能的引号
        schemaName = schemaName.replaceAll("[`\"']", "");

        return schemaName;
    }

    // ========== 高级索引功能测试 ==========

    @Test
    @DisplayName("测试复合索引和函数索引")
    void testAdvancedIndexFeatures() {
        String[] advancedCases = {
            // 复合索引
            "CREATE INDEX idx_composite ON sales.orders(customer_id, order_date, status);",
            // 降序索引
            "CREATE INDEX idx_desc ON inventory.products(price DESC);",
            // 混合排序索引
            "CREATE INDEX idx_mixed ON analytics.reports(created_date ASC, priority DESC);",
            // 长索引名测试
            "CREATE INDEX idx_very_long_index_name_for_testing_purposes ON test_schema.test_table(test_column);"
        };

        for (String testSql : advancedCases) {
            log.info("测试高级索引SQL: {}", testSql);

            TranspilationResult result = transpiler.transpile(testSql, "mysql", "shentong");
            assertTrue(result.successCount() > 0, "高级索引转换应该成功: " + testSql);

            String convertedSql = result.translatedSql();
            log.info("高级索引转换结果: {}", convertedSql);

            // 验证基本结构
            assertTrue(convertedSql.toUpperCase().contains("CREATE INDEX"),
                      "应该包含CREATE INDEX: " + convertedSql);

            // 验证schema保留
            String schemaName = extractSchemaName(testSql);
            if (schemaName != null) {
                assertTrue(convertedSql.contains(schemaName),
                          "高级索引应该保留schema: " + convertedSql);
            }
        }

        log.info("✅ 高级索引功能测试通过！");
    }

    // ========== 从ShentongIndexSupportTest合并的测试用例 ==========

    @Test
    @DisplayName("测试UNIQUE INDEX支持")
    void testUniqueIndexSupport() {
        String mysqlSql = """
            CREATE TABLE products (
                id INT PRIMARY KEY,
                code VARCHAR(50),
                name VARCHAR(100) NOT NULL
            );

            CREATE UNIQUE INDEX idx_products_code ON products(code);
            """;

        log.info("测试UNIQUE INDEX SQL: {}", mysqlSql);

        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "shentong");
        assertTrue(result.successCount() > 0, "UNIQUE INDEX转换应该成功");

        String shentongSql = result.translatedSql();
        log.info("UNIQUE INDEX转换结果: {}", shentongSql);

        // 验证UNIQUE INDEX支持
        assertTrue(shentongSql.contains("CREATE UNIQUE INDEX"), "应支持CREATE UNIQUE INDEX");
        assertTrue(shentongSql.contains("idx_products_code"), "应保持唯一索引名称");
        assertTrue(shentongSql.contains("products") || shentongSql.contains("\"products\""),
                   "应支持表名引用");

        log.info("✅ UNIQUE INDEX支持测试通过！");
    }

    @Test
    @DisplayName("测试组合索引支持")
    void testCompositeIndexSupport() {
        String mysqlSql = """
            CREATE TABLE orders (
                id INT PRIMARY KEY,
                customer_id INT NOT NULL,
                order_date DATE NOT NULL,
                status VARCHAR(20) DEFAULT 'pending',
                total_amount DECIMAL(10,2)
            );

            CREATE INDEX idx_orders_customer_date ON orders(customer_id, order_date);
            CREATE INDEX idx_orders_status_amount ON orders(status, total_amount);
            """;

        log.info("测试组合索引SQL: {}", mysqlSql);

        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "shentong");
        assertTrue(result.successCount() > 0, "组合索引转换应该成功");

        String shentongSql = result.translatedSql();
        log.info("组合索引转换结果: {}", shentongSql);

        // 验证组合索引支持
        assertTrue(shentongSql.contains("CREATE INDEX"), "应支持CREATE INDEX");
        assertTrue(shentongSql.contains("idx_orders_customer_date"), "应保持组合索引名称");
        assertTrue(shentongSql.contains("idx_orders_status_amount"), "应保持组合索引名称");
        assertTrue(shentongSql.contains("customer_id") && shentongSql.contains("order_date"),
                   "应支持多列索引");

        log.info("✅ 组合索引支持测试通过！");
    }

    @Test
    @DisplayName("测试DROP INDEX支持")
    void testDropIndexSupport() {
        String mysqlSql = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                data VARCHAR(100)
            );

            CREATE INDEX idx_test_data ON test_table(data);
            DROP INDEX idx_test_data;
            """;

        log.info("测试DROP INDEX SQL: {}", mysqlSql);

        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "shentong");
        assertTrue(result.successCount() > 0, "DROP INDEX转换应该成功");

        String shentongSql = result.translatedSql();
        log.info("DROP INDEX转换结果: {}", shentongSql);

        // 验证DROP INDEX支持
        assertTrue(shentongSql.contains("CREATE INDEX"), "应支持CREATE INDEX");
        assertTrue(shentongSql.contains("DROP INDEX"), "应支持DROP INDEX语句");
        assertTrue(shentongSql.contains("idx_test_data"), "应保持索引名称");

        log.info("✅ DROP INDEX支持测试通过！");
    }

    @Test
    @DisplayName("测试带schema的复杂索引场景")
    void testComplexSchemaIndexScenarios() {
        String[] complexCases = {
            // 跨schema的索引
            "CREATE INDEX idx_cross_schema ON db1.table1(col1); CREATE INDEX idx_same_name ON db2.table1(col1);",
            // 长表名和长列名
            "CREATE INDEX idx_very_long_name ON very_long_schema_name.very_long_table_name(very_long_column_name_for_testing);",
            // 混合大小写schema
            "CREATE INDEX idx_mixed_case ON MySchema.MyTable(MyColumn);",
            // 特殊字符schema（如果支持）
            "CREATE INDEX idx_special ON user_data.user_profile(user_name);"
        };

        for (String testSql : complexCases) {
            log.info("测试复杂schema索引SQL: {}", testSql);

            TranspilationResult result = transpiler.transpile(testSql, "mysql", "shentong");
            assertTrue(result.successCount() > 0, "复杂schema索引转换应该成功: " + testSql);

            String convertedSql = result.translatedSql();
            log.info("复杂schema索引转换结果: {}", convertedSql);

            // 验证基本结构
            assertTrue(convertedSql.toUpperCase().contains("CREATE INDEX"),
                      "应该包含CREATE INDEX: " + convertedSql);

            // 验证schema保留（如果有的话）
            if (testSql.contains(".")) {
                String schemaName = extractSchemaName(testSql);
                if (schemaName != null) {
                    assertTrue(convertedSql.contains(schemaName),
                              "复杂场景应该保留schema: " + schemaName + " in " + convertedSql);
                }
            }
        }

        log.info("✅ 复杂schema索引场景测试通过！");
    }
}
