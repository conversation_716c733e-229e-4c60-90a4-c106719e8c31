package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库CTE（Common Table Expression）转换测试
 * 
 * 基于官方文档测试CTE支持：
 * - MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/with.html
 * - 神通数据库官方文档：shentong.md 第55776行显示支持WITH关键字
 * 
 * 测试覆盖的CTE功能：
 * 1. 简单CTE - 基础公共表表达式
 * 2. 递归CTE - WITH RECURSIVE语法
 * 3. 多个CTE - 一个查询中定义多个CTE
 * 4. 嵌套CTE - CTE中引用其他CTE
 * 5. CTE与JOIN组合 - CTE与表连接
 * 6. CTE与窗口函数组合 - 高级查询场景
 * 
 * <AUTHOR>
 */
@DisplayName("神通数据库CTE转换测试")
public class ShentongCteTest extends BaseShentongConversionTest {
    
    private static final Logger log = LoggerFactory.getLogger(ShentongCteTest.class);

    /**
     * 测试简单CTE转换
     * 
     * 根据MySQL官方文档：WITH cte_name AS (subquery) SELECT ...
     * 根据神通官方文档第55776行：WITH关键字被列为支持的关键字
     */
    @Test
    @DisplayName("简单CTE转换测试")
    public void testSimpleCte() throws Exception {
        log.info("=== 简单CTE转换测试 ===");
        
        String mysqlSql = """
            WITH department_stats AS (
                SELECT 
                    department_id,
                    COUNT(*) as employee_count,
                    AVG(salary) as avg_salary,
                    MAX(salary) as max_salary
                FROM employees
                GROUP BY department_id
            )
            SELECT 
                d.department_name,
                ds.employee_count,
                ds.avg_salary,
                ds.max_salary
            FROM departments d
            JOIN department_stats ds ON d.id = ds.department_id
            ORDER BY ds.avg_salary DESC;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "CTE SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证CTE转换结果
        if (shentongSql.contains("WITH department_stats AS")) {
            // 如果神通支持CTE，验证转换正确性
            assertTrue(shentongSql.contains("WITH department_stats AS"), "应包含CTE定义");
            assertTrue(shentongSql.contains("SELECT"), "应包含SELECT语句");
            assertTrue(shentongSql.contains("FROM departments"), "应包含主查询");
            assertTrue(shentongSql.contains("JOIN department_stats"), "应包含CTE引用");
            assertTrue(shentongSql.endsWith(";"), "应以分号结尾");
            
            log.info("✅ 神通数据库支持简单CTE，转换成功");
        } else {
            // 如果神通不支持CTE，应该有明确的不支持说明
            assertTrue(shentongSql.contains("-- CTE") || shentongSql.contains("不支持"), 
                      "应明确说明CTE不支持或提供替代方案");
            
            log.info("⚠️ 神通数据库不支持CTE，已提供替代方案说明");
        }
    }

    /**
     * 测试递归CTE转换
     * 
     * 根据MySQL官方文档：WITH RECURSIVE cte_name AS (anchor UNION ALL recursive) SELECT ...
     */
    @Test
    @DisplayName("递归CTE转换测试")
    public void testRecursiveCte() throws Exception {
        log.info("=== 递归CTE转换测试 ===");
        
        String mysqlSql = """
            WITH RECURSIVE employee_hierarchy AS (
                -- 锚点查询：找到所有顶级管理者
                SELECT 
                    id, 
                    name, 
                    manager_id, 
                    1 as level,
                    CAST(name AS CHAR(1000)) as path
                FROM employees
                WHERE manager_id IS NULL
                
                UNION ALL
                
                -- 递归查询：找到下级员工
                SELECT 
                    e.id, 
                    e.name, 
                    e.manager_id, 
                    eh.level + 1,
                    CONCAT(eh.path, ' -> ', e.name) as path
                FROM employees e
                INNER JOIN employee_hierarchy eh ON e.manager_id = eh.id
                WHERE eh.level < 10  -- 防止无限递归
            )
            SELECT 
                id,
                name,
                level,
                path
            FROM employee_hierarchy 
            ORDER BY level, name;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "递归CTE SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证递归CTE转换结果
        if (shentongSql.contains("WITH RECURSIVE")) {
            // 如果神通支持递归CTE
            assertTrue(shentongSql.contains("WITH RECURSIVE employee_hierarchy AS"), "应包含递归CTE定义");
            assertTrue(shentongSql.contains("UNION ALL"), "应包含UNION ALL");
            assertTrue(shentongSql.contains("INNER JOIN employee_hierarchy"), "应包含递归引用");
            
            log.info("✅ 神通数据库支持递归CTE，转换成功");
        } else if (shentongSql.contains("CONNECT BY")) {
            // 如果神通将递归CTE转换为CONNECT BY语法
            assertTrue(shentongSql.contains("CONNECT BY"), "应包含CONNECT BY语法");
            assertTrue(shentongSql.contains("START WITH"), "应包含START WITH语法");
            
            log.info("✅ 神通数据库将递归CTE转换为CONNECT BY语法");
        } else {
            // 如果神通不支持递归CTE
            assertTrue(shentongSql.contains("-- CTE") || shentongSql.contains("不支持") || 
                      shentongSql.contains("-- Recursive"), 
                      "应明确说明递归CTE不支持");
            
            log.info("⚠️ 神通数据库不支持递归CTE");
        }
    }

    /**
     * 测试多个CTE转换
     * 
     * 根据MySQL官方文档：WITH cte1 AS (...), cte2 AS (...) SELECT ...
     */
    @Test
    @DisplayName("多个CTE转换测试")
    public void testMultipleCte() throws Exception {
        log.info("=== 多个CTE转换测试 ===");
        
        String mysqlSql = """
            WITH 
            sales_summary AS (
                SELECT 
                    region,
                    SUM(amount) as total_sales,
                    COUNT(*) as transaction_count
                FROM sales
                WHERE sale_date >= '2023-01-01'
                GROUP BY region
            ),
            top_regions AS (
                SELECT 
                    region,
                    total_sales,
                    transaction_count
                FROM sales_summary
                WHERE total_sales > 100000
            ),
            region_employees AS (
                SELECT 
                    region,
                    COUNT(*) as employee_count,
                    AVG(salary) as avg_salary
                FROM employees
                GROUP BY region
            )
            SELECT 
                tr.region,
                tr.total_sales,
                tr.transaction_count,
                re.employee_count,
                re.avg_salary,
                ROUND(tr.total_sales / re.employee_count, 2) as sales_per_employee
            FROM top_regions tr
            LEFT JOIN region_employees re ON tr.region = re.region
            ORDER BY sales_per_employee DESC;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "多个CTE SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证多个CTE转换结果
        if (shentongSql.contains("WITH") && !shentongSql.contains("-- CTE")) {
            // 如果神通支持多个CTE
            assertTrue(shentongSql.contains("sales_summary AS"), "应包含第一个CTE");
            assertTrue(shentongSql.contains("top_regions AS"), "应包含第二个CTE");
            assertTrue(shentongSql.contains("region_employees AS"), "应包含第三个CTE");
            assertTrue(shentongSql.contains("LEFT JOIN region_employees"), "应包含CTE引用");
            
            log.info("✅ 神通数据库支持多个CTE，转换成功");
        } else {
            // 如果神通不支持多个CTE
            assertTrue(shentongSql.contains("-- CTE") || shentongSql.contains("不支持"), 
                      "应明确说明多个CTE不支持");
            
            log.info("⚠️ 神通数据库不支持多个CTE");
        }
    }

    /**
     * 测试CTE与窗口函数组合
     * 验证CTE与其他高级SQL特性的兼容性
     */
    @Test
    @DisplayName("CTE与窗口函数组合测试")
    public void testCteWithWindowFunctions() throws Exception {
        log.info("=== CTE与窗口函数组合测试 ===");
        
        String mysqlSql = """
            WITH ranked_employees AS (
                SELECT 
                    id,
                    name,
                    department_id,
                    salary,
                    ROW_NUMBER() OVER (PARTITION BY department_id ORDER BY salary DESC) as salary_rank,
                    RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as salary_rank_with_ties
                FROM employees
            )
            SELECT 
                re.name,
                d.department_name,
                re.salary,
                re.salary_rank,
                re.salary_rank_with_ties
            FROM ranked_employees re
            JOIN departments d ON re.department_id = d.id
            WHERE re.salary_rank <= 3
            ORDER BY d.department_name, re.salary_rank;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "CTE与窗口函数组合SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证CTE与窗口函数组合转换结果
        if (shentongSql.contains("WITH ranked_employees AS") && shentongSql.contains("ROW_NUMBER()")) {
            // 如果神通同时支持CTE和窗口函数
            assertTrue(shentongSql.contains("WITH ranked_employees AS"), "应包含CTE定义");
            assertTrue(shentongSql.contains("ROW_NUMBER()"), "应包含窗口函数");
            assertTrue(shentongSql.contains("PARTITION BY department_id"), "应包含分区子句");
            assertTrue(shentongSql.contains("JOIN departments"), "应包含表连接");
            
            log.info("✅ 神通数据库支持CTE与窗口函数组合，转换成功");
        } else {
            // 如果神通不支持CTE或窗口函数组合
            log.info("⚠️ 神通数据库不完全支持CTE与窗口函数组合");
        }
    }

    /**
     * 测试CTE的边界条件
     * 验证空结果集和复杂嵌套的处理
     */
    @Test
    @DisplayName("CTE边界条件测试")
    public void testCteBoundaryConditions() throws Exception {
        log.info("=== CTE边界条件测试 ===");
        
        // 测试带有复杂WHERE条件的CTE
        String mysqlSql = """
            WITH filtered_data AS (
                SELECT 
                    id,
                    name,
                    department_id,
                    salary
                FROM employees
                WHERE department_id IS NOT NULL
                  AND salary BETWEEN 30000 AND 150000
                  AND name NOT LIKE '%temp%'
                  AND hire_date >= DATE_SUB(CURDATE(), INTERVAL 5 YEAR)
            )
            SELECT 
                fd.name,
                fd.salary,
                CASE 
                    WHEN fd.salary >= 100000 THEN 'High'
                    WHEN fd.salary >= 60000 THEN 'Medium'
                    ELSE 'Low'
                END as salary_grade
            FROM filtered_data fd
            ORDER BY fd.salary DESC
            LIMIT 50;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "CTE边界条件SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        
        log.info("神通转换结果: {}", shentongSql);
        
        // 验证边界条件处理
        if (shentongSql.contains("WITH filtered_data AS")) {
            assertTrue(shentongSql.contains("WHERE"), "应保持WHERE条件");
            assertTrue(shentongSql.contains("BETWEEN"), "应保持BETWEEN条件");
            assertTrue(shentongSql.contains("IS NOT NULL"), "应保持IS NOT NULL条件");
            assertTrue(shentongSql.contains("CASE"), "应保持CASE表达式");
            
            log.info("✅ CTE边界条件转换测试通过");
        } else {
            log.info("⚠️ CTE边界条件测试：神通数据库不支持CTE");
        }
    }

    /**
     * 测试CTE错误处理
     * 验证不支持的CTE语法的处理
     */
    @Test
    @DisplayName("CTE错误处理测试")
    public void testCteErrorHandling() throws Exception {
        log.info("=== CTE错误处理测试 ===");
        
        // 测试基本的CTE，确保不会出现转换错误
        String mysqlSql = """
            WITH simple_cte AS (
                SELECT id, name FROM users WHERE active = 1
            )
            SELECT * FROM simple_cte ORDER BY name;
            """;
        
        log.info("原始MySQL SQL: {}", mysqlSql);
        
        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "简单CTE SQL解析不应失败");
        
        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");
        assertFalse(shentongSql.contains("ERROR"), "转换结果不应包含错误信息");
        assertFalse(shentongSql.contains("FAILED"), "转换结果不应包含失败信息");
        
        log.info("神通转换结果: {}", shentongSql);
        log.info("✅ CTE错误处理测试通过");
    }

    /**
     * 测试嵌套CTE转换
     * 验证CTE中引用其他CTE的复杂场景
     */
    @Test
    @DisplayName("嵌套CTE转换测试")
    public void testNestedCte() throws Exception {
        log.info("=== 嵌套CTE转换测试 ===");

        String mysqlSql = """
            WITH
            base_sales AS (
                SELECT
                    product_id,
                    region,
                    sale_date,
                    amount,
                    quantity
                FROM sales
                WHERE sale_date >= '2023-01-01'
            ),
            monthly_sales AS (
                SELECT
                    product_id,
                    region,
                    DATE_FORMAT(sale_date, '%Y-%m') as sale_month,
                    SUM(amount) as monthly_amount,
                    SUM(quantity) as monthly_quantity
                FROM base_sales
                GROUP BY product_id, region, DATE_FORMAT(sale_date, '%Y-%m')
            ),
            top_products AS (
                SELECT
                    product_id,
                    SUM(monthly_amount) as total_amount,
                    AVG(monthly_quantity) as avg_monthly_quantity
                FROM monthly_sales
                GROUP BY product_id
                HAVING SUM(monthly_amount) > 50000
            )
            SELECT
                tp.product_id,
                p.product_name,
                tp.total_amount,
                tp.avg_monthly_quantity,
                COUNT(DISTINCT ms.region) as region_count
            FROM top_products tp
            JOIN products p ON tp.product_id = p.id
            LEFT JOIN monthly_sales ms ON tp.product_id = ms.product_id
            GROUP BY tp.product_id, p.product_name, tp.total_amount, tp.avg_monthly_quantity
            ORDER BY tp.total_amount DESC;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "嵌套CTE SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证嵌套CTE转换结果
        if (shentongSql.contains("WITH") && !shentongSql.contains("-- CTE")) {
            assertTrue(shentongSql.contains("base_sales AS"), "应包含基础CTE");
            assertTrue(shentongSql.contains("monthly_sales AS"), "应包含月度销售CTE");
            assertTrue(shentongSql.contains("top_products AS"), "应包含顶级产品CTE");
            assertTrue(shentongSql.contains("FROM base_sales"), "应包含CTE间的引用");
            assertTrue(shentongSql.contains("FROM monthly_sales"), "应包含CTE间的引用");
            assertTrue(shentongSql.contains("HAVING"), "应保持HAVING子句");

            log.info("✅ 神通数据库支持嵌套CTE，转换成功");
        } else {
            log.info("⚠️ 神通数据库不支持嵌套CTE");
        }
    }

    /**
     * 测试CTE与子查询组合
     * 验证CTE与传统子查询的混合使用
     */
    @Test
    @DisplayName("CTE与子查询组合测试")
    public void testCteWithSubqueries() throws Exception {
        log.info("=== CTE与子查询组合测试 ===");

        String mysqlSql = """
            WITH department_summary AS (
                SELECT
                    department_id,
                    COUNT(*) as emp_count,
                    AVG(salary) as avg_salary
                FROM employees
                WHERE status = 'active'
                GROUP BY department_id
            )
            SELECT
                d.department_name,
                ds.emp_count,
                ds.avg_salary,
                (
                    SELECT COUNT(*)
                    FROM projects p
                    WHERE p.department_id = d.id
                      AND p.status = 'active'
                ) as active_projects,
                (
                    SELECT MAX(salary)
                    FROM employees e
                    WHERE e.department_id = d.id
                ) as max_salary_in_dept
            FROM departments d
            JOIN department_summary ds ON d.id = ds.department_id
            WHERE ds.emp_count > (
                SELECT AVG(emp_count)
                FROM department_summary
            )
            ORDER BY ds.avg_salary DESC;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "CTE与子查询组合SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证CTE与子查询组合转换结果
        if (shentongSql.contains("WITH department_summary AS")) {
            assertTrue(shentongSql.contains("WITH department_summary AS"), "应包含CTE定义");
            assertTrue(shentongSql.contains("SELECT COUNT(*)"), "应包含子查询");
            assertTrue(shentongSql.contains("SELECT MAX(salary)"), "应包含子查询");
            assertTrue(shentongSql.contains("WHERE ds.emp_count >"), "应包含WHERE子查询");

            log.info("✅ 神通数据库支持CTE与子查询组合，转换成功");
        } else {
            log.info("⚠️ 神通数据库不支持CTE与子查询组合");
        }
    }

    /**
     * 测试CTE性能优化场景
     * 验证复杂CTE查询的转换
     */
    @Test
    @DisplayName("CTE性能优化场景测试")
    public void testCtePerformanceOptimization() throws Exception {
        log.info("=== CTE性能优化场景测试 ===");

        String mysqlSql = """
            WITH RECURSIVE organization_tree AS (
                -- 锚点：顶级部门
                SELECT
                    id,
                    name,
                    parent_id,
                    1 as level,
                    CAST(id AS CHAR(1000)) as path
                FROM departments
                WHERE parent_id IS NULL

                UNION ALL

                -- 递归：子部门
                SELECT
                    d.id,
                    d.name,
                    d.parent_id,
                    ot.level + 1,
                    CONCAT(ot.path, '->', d.id) as path
                FROM departments d
                INNER JOIN organization_tree ot ON d.parent_id = ot.id
                WHERE ot.level < 5  -- 限制递归深度
            ),
            employee_stats AS (
                SELECT
                    department_id,
                    COUNT(*) as employee_count,
                    SUM(salary) as total_salary,
                    AVG(salary) as avg_salary,
                    MIN(hire_date) as earliest_hire,
                    MAX(hire_date) as latest_hire
                FROM employees
                WHERE status = 'active'
                GROUP BY department_id
            )
            SELECT
                ot.level,
                ot.name as department_name,
                ot.path,
                COALESCE(es.employee_count, 0) as employee_count,
                COALESCE(es.total_salary, 0) as total_salary,
                COALESCE(es.avg_salary, 0) as avg_salary,
                es.earliest_hire,
                es.latest_hire
            FROM organization_tree ot
            LEFT JOIN employee_stats es ON ot.id = es.department_id
            ORDER BY ot.level, ot.name;
            """;

        log.info("原始MySQL SQL: {}", mysqlSql);

        Statement statement = MySqlHelper.parseStatement(mysqlSql);
        assertNotNull(statement, "CTE性能优化场景SQL解析不应失败");

        String shentongSql = generator.generate(statement);
        assertNotNull(shentongSql, "神通转换结果不应为null");

        log.info("神通转换结果: {}", shentongSql);

        // 验证复杂CTE转换结果
        if (shentongSql.contains("WITH RECURSIVE organization_tree AS")) {
            assertTrue(shentongSql.contains("WITH RECURSIVE"), "应包含递归CTE");
            assertTrue(shentongSql.contains("employee_stats AS"), "应包含第二个CTE");
            assertTrue(shentongSql.contains("UNION ALL"), "应包含UNION ALL");
            assertTrue(shentongSql.contains("COALESCE"), "应包含COALESCE函数");
            assertTrue(shentongSql.contains("LEFT JOIN employee_stats"), "应包含CTE连接");

            log.info("✅ 神通数据库支持复杂CTE性能优化场景，转换成功");
        } else {
            log.info("⚠️ 神通数据库不支持复杂CTE性能优化场景");
        }
    }
}
