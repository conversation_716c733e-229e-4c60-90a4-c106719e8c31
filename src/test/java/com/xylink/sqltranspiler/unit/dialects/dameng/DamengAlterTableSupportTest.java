package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 达梦数据库ALTER TABLE支持测试
 * 
 * 根据达梦官方文档，测试MySQL ALTER TABLE语句到达梦的转换
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 */
@DisplayName("达梦数据库ALTER TABLE支持测试")
public class DamengAlterTableSupportTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基础表结构用于ALTER操作")
    void testBasicTableForAlterOperations() {
        String mysqlSql = "CREATE TABLE alter_test_table (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(100), " +
                "age INT, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("基础表结构（用于ALTER操作）:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证基础表结构
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("alter_test_table"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
    }

    @Test
    @DisplayName("测试添加列的表结构模拟")
    void testAddColumnSimulation() {
        // 模拟添加列后的表结构
        String mysqlSql = "CREATE TABLE users_with_new_columns (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(100), " +
                "phone VARCHAR(20), " +
                "birth_date DATE, " +
                "status ENUM('active', 'inactive') DEFAULT 'active', " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("添加列后的表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证添加列的效果
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("users_with_new_columns"), "应保留表名");
        assertTrue(damengSql.contains("phone"), "应包含新添加的phone列");
        assertTrue(damengSql.contains("birth_date"), "应包含新添加的birth_date列");
        assertTrue(damengSql.contains("status"), "应包含新添加的status列");
        assertTrue(damengSql.contains("VARCHAR(20)"), "应保留phone列类型");
        assertTrue(damengSql.contains("DATE"), "应保留birth_date列类型");
    }

    @Test
    @DisplayName("测试修改列的表结构模拟")
    void testModifyColumnSimulation() {
        // 模拟修改列后的表结构
        String mysqlSql = "CREATE TABLE users_modified_columns (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(200) NOT NULL, " +
                "email VARCHAR(150) UNIQUE, " +
                "age INT DEFAULT 0, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("修改列后的表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证修改列的效果
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("users_modified_columns"), "应保留表名");
        assertTrue(damengSql.contains("VARCHAR(200)"), "应包含修改后的name列长度");
        assertTrue(damengSql.contains("VARCHAR(150)"), "应包含修改后的email列长度");
        assertTrue(damengSql.contains("UNIQUE"), "应包含新增的UNIQUE约束");
        assertTrue(damengSql.contains("DEFAULT 0"), "应包含新增的默认值");
    }

    @Test
    @DisplayName("测试添加索引的表结构模拟")
    void testAddIndexSimulation() {
        // 模拟添加索引后的表结构
        String mysqlSql = "CREATE TABLE users_with_indexes (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(100) UNIQUE, " +
                "phone VARCHAR(20), " +
                "created_at TIMESTAMP, " +
                "INDEX idx_name (name), " +
                "INDEX idx_phone (phone), " +
                "INDEX idx_created (created_at)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("添加索引后的表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证添加索引的效果
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("users_with_indexes"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("UNIQUE"), "应保留唯一约束");
        
        // 验证索引处理（达梦可能在CREATE TABLE中处理索引）
        assertTrue(damengSql.contains("idx_name") || damengSql.contains("name"), 
                  "应处理name列索引");
        assertTrue(damengSql.contains("idx_phone") || damengSql.contains("phone"), 
                  "应处理phone列索引");
    }

    @Test
    @DisplayName("测试添加约束的表结构模拟")
    void testAddConstraintSimulation() {
        // 模拟添加约束后的表结构
        String mysqlSql = "CREATE TABLE orders_with_constraints (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "user_id INT NOT NULL, " +
                "product_id INT NOT NULL, " +
                "quantity INT NOT NULL, " +
                "amount DECIMAL(10,2) NOT NULL, " +
                "order_date DATE NOT NULL, " +
                "CONSTRAINT fk_orders_user FOREIGN KEY (user_id) REFERENCES users(id), " +
                "CONSTRAINT fk_orders_product FOREIGN KEY (product_id) REFERENCES products(id), " +
                "CONSTRAINT chk_quantity CHECK (quantity > 0), " +
                "CONSTRAINT chk_amount CHECK (amount > 0), " +
                "CONSTRAINT uk_order_user_product UNIQUE (user_id, product_id, order_date)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("添加约束后的表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证添加约束的效果
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("orders_with_constraints"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键约束");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("FOREIGN KEY"), "应保留外键约束");
        assertTrue(damengSql.contains("REFERENCES"), "应保留引用");
        assertTrue(damengSql.contains("CHECK"), "应保留CHECK约束");
        assertTrue(damengSql.contains("UNIQUE"), "应保留UNIQUE约束");
    }

    @Test
    @DisplayName("测试重命名表的模拟")
    void testRenameTableSimulation() {
        // 模拟重命名后的表结构
        String mysqlSql = "CREATE TABLE new_users_table (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "username VARCHAR(100) NOT NULL UNIQUE, " +
                "email VARCHAR(100) NOT NULL UNIQUE, " +
                "password_hash VARCHAR(255) NOT NULL, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("重命名后的表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证重命名表的效果
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("new_users_table"), "应包含新表名");
        assertTrue(damengSql.contains("username"), "应保留列名");
        assertTrue(damengSql.contains("email"), "应保留列名");
        assertTrue(damengSql.contains("password_hash"), "应保留列名");
        assertTrue(damengSql.contains("UNIQUE"), "应保留唯一约束");
    }

    @Test
    @DisplayName("测试复杂ALTER操作的表结构模拟")
    void testComplexAlterOperationSimulation() {
        // 模拟复杂ALTER操作后的表结构
        String mysqlSql = "CREATE TABLE complex_altered_table (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "new_name VARCHAR(200) NOT NULL, " +
                "new_email VARCHAR(150) UNIQUE, " +
                "phone VARCHAR(20), " +
                "birth_date DATE, " +
                "status VARCHAR(20) DEFAULT 'active', " +
                "salary DECIMAL(12,2), " +
                "department_id INT, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "INDEX idx_new_name (new_name), " +
                "INDEX idx_phone (phone), " +
                "INDEX idx_department (department_id), " +
                "CONSTRAINT fk_department FOREIGN KEY (department_id) REFERENCES departments(id), " +
                "CONSTRAINT chk_salary CHECK (salary >= 0), " +
                "CONSTRAINT chk_status CHECK (status IN ('active', 'inactive', 'pending'))" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("复杂ALTER操作后的表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证复杂ALTER操作的效果
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("complex_altered_table"), "应保留表名");
        assertTrue(damengSql.contains("BIGINT"), "应包含修改后的ID类型");
        assertTrue(damengSql.contains("new_name"), "应包含重命名的列");
        assertTrue(damengSql.contains("new_email"), "应包含重命名的列");
        assertTrue(damengSql.contains("VARCHAR(200)"), "应包含修改后的列长度");
        assertTrue(damengSql.contains("phone"), "应包含新添加的列");
        assertTrue(damengSql.contains("birth_date"), "应包含新添加的列");
        assertTrue(damengSql.contains("salary"), "应包含新添加的列");
        assertTrue(damengSql.contains("department_id"), "应包含新添加的列");
        assertTrue(damengSql.contains("DECIMAL(12,2)"), "应包含新的数据类型");
        assertTrue(damengSql.contains("FOREIGN KEY"), "应包含新添加的外键");
        assertTrue(damengSql.contains("CHECK"), "应包含新添加的CHECK约束");
    }

    @Test
    @DisplayName("测试达梦特有的ALTER特性模拟")
    void testDamengSpecificAlterFeatures() {
        // 模拟达梦特有特性的表结构
        String mysqlSql = "CREATE TABLE dameng_specific_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "data LONGTEXT, " +
                "binary_data LONGBLOB, " +
                "numeric_data DECIMAL(20,6), " +
                "date_data DATETIME, " +
                "timestamp_data TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦特有特性表结构:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证达梦特有特性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("dameng_specific_features"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("AUTO_INCREMENT"), 
                  "应转换自增字段");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("LONGTEXT") || damengSql.contains("CLOB"), 
                  "应转换或保留长文本类型");
        assertTrue(damengSql.contains("LONGBLOB") || damengSql.contains("BLOB"), 
                  "应转换或保留长二进制类型");
        assertTrue(damengSql.contains("DECIMAL(20,6)"), "应保留高精度数值类型");
        assertTrue(damengSql.contains("DATETIME") || damengSql.contains("TIMESTAMP"), 
                  "应转换或保留日期时间类型");
    }
}
