package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库INSERT语句兼容性测试
 * 根据神通数据库官方文档，验证INSERT语句的支持情况
 */
public class ShentongInsertStatementCompatibilityTest extends BaseShentongConversionTest {

    private static final Logger log = LoggerFactory.getLogger(ShentongInsertStatementCompatibilityTest.class);

    /**
     * 测试标准INSERT INTO ... VALUES语法支持
     * 根据神通数据库官方文档，支持标准INSERT语法
     */
    @Test
    void testStandardInsertStatementSupport() throws Exception {
        String sql = "INSERT INTO employees (id, name, salary) VALUES (1, 'John', 60000)";

        String result = convertMySqlToShentongDirect(sql);

        log.info("标准INSERT语句转换结果: {}", result);

        // 验证转换成功
        assertNotNull(result);
        assertFalse(result.contains("-- INSERT"));
        assertTrue(result.contains("INSERT INTO"));
        assertTrue(result.contains("VALUES"));
        assertTrue(result.endsWith(";"));

        // 使用基类的验证方法
        assertInsertStatementConversion(result);
    }

    /**
     * 测试INSERT ... SET语法不支持
     * 根据神通数据库官方文档，不支持INSERT SET语法
     */
    @Test
    void testInsertSetStatementNotSupported() throws Exception {
        String sql = "INSERT INTO employees SET name = 'John', salary = 60000, department = 'IT'";

        String result = convertMySqlToShentongDirect(sql);

        log.info("INSERT ... SET语句转换结果: {}", result);

        // 验证转换失败，返回错误注释
        assertNotNull(result);
        assertTrue(result.contains("-- INSERT ... SET syntax is not supported in Shentong database"));
        assertTrue(result.contains("-- Please convert to INSERT INTO ... VALUES syntax"));
        assertTrue(result.contains("-- Original SQL: " + sql));
    }

    /**
     * 测试INSERT ... ON DUPLICATE KEY UPDATE语法不支持
     * 根据神通数据库官方文档，不支持ON DUPLICATE KEY UPDATE语法
     */
    @Test
    void testInsertOnDuplicateKeyUpdateNotSupported() throws Exception {
        String sql = "INSERT INTO employees (id, name, salary) VALUES (1, 'John', 60000) ON DUPLICATE KEY UPDATE salary = VALUES(salary)";

        String result = convertMySqlToShentongDirect(sql);

        log.info("INSERT ... ON DUPLICATE KEY UPDATE语句转换结果: {}", result);

        // 验证转换失败，返回错误注释
        assertNotNull(result);
        assertTrue(result.contains("-- INSERT ... ON DUPLICATE KEY UPDATE syntax is not supported in Shentong database"));
        assertTrue(result.contains("-- Please use standard INSERT INTO ... VALUES syntax"));
        assertTrue(result.contains("-- Original SQL: " + sql));
    }

    /**
     * 测试多行INSERT VALUES语法支持
     * 根据神通数据库官方文档，支持多行插入
     */
    @Test
    void testMultiRowInsertSupport() throws Exception {
        String sql = "INSERT INTO employees (id, name, salary) VALUES (1, 'John', 60000), (2, 'Jane', 65000), (3, 'Bob', 55000)";

        String result = convertMySqlToShentongDirect(sql);

        log.info("多行INSERT语句转换结果: {}", result);

        // 验证转换成功
        assertNotNull(result);
        assertFalse(result.contains("-- INSERT"));
        assertTrue(result.contains("INSERT INTO"));
        assertTrue(result.contains("VALUES"));
        assertTrue(result.contains("(1, 'John', 60000)"));
        assertTrue(result.contains("(2, 'Jane', 65000)"));
        assertTrue(result.contains("(3, 'Bob', 55000)"));
        assertTrue(result.endsWith(";"));

        // 使用基类的验证方法
        assertInsertStatementConversion(result);
    }

    /**
     * 测试INSERT INTO ... SELECT语法支持
     * 根据神通数据库官方文档，支持INSERT INTO ... SELECT语法
     */
    @Test
    void testInsertSelectSupport() throws Exception {
        String sql = "INSERT INTO employees_backup (id, name, salary) SELECT id, name, salary FROM employees WHERE salary > 50000";

        String result = convertMySqlToShentongDirect(sql);

        log.info("INSERT ... SELECT语句转换结果: {}", result);

        // 验证转换成功
        assertNotNull(result);
        assertFalse(result.contains("-- INSERT"));
        assertTrue(result.contains("INSERT INTO"));
        assertTrue(result.contains("SELECT"));
        assertTrue(result.endsWith(";"));

        // 使用基类的验证方法
        assertInsertStatementConversion(result);
    }

    /**
     * 测试带反引号的INSERT语句转换
     * 神通数据库使用双引号而不是反引号
     */
    @Test
    void testInsertWithBackticksConversion() throws Exception {
        String sql = "INSERT INTO `employees` (`id`, `name`, `salary`) VALUES (1, 'John', 60000)";

        String result = convertMySqlToShentongDirect(sql);

        log.info("带反引号的INSERT语句转换结果: {}", result);

        // 验证反引号被转换为双引号
        assertNotNull(result);
        assertFalse(result.contains("`"));
        assertTrue(result.contains("\"employees\""));
        assertTrue(result.contains("\"id\""));
        assertTrue(result.contains("\"name\""));
        assertTrue(result.contains("\"salary\""));
        assertTrue(result.endsWith(";"));

        // 使用基类的验证方法
        assertInsertStatementConversion(result);
    }

    /**
     * 测试REPLACE INTO语句不支持
     * 根据神通数据库官方文档，不支持REPLACE INTO语法
     */
    @Test
    void testReplaceIntoNotSupported() throws Exception {
        String sql = "REPLACE INTO employees (id, name, salary) VALUES (1, 'John', 60000)";

        String result = convertMySqlToShentongDirect(sql);

        log.info("REPLACE INTO语句转换结果: {}", result);

        // 验证转换失败，返回错误注释
        assertNotNull(result);
        assertTrue(result.contains("-- REPLACE INTO is not supported in Shentong database"));
        assertTrue(result.contains("-- Please use standard INSERT INTO ... VALUES syntax"));
    }

    /**
     * 测试复杂的不支持语法组合
     */
    @Test
    void testComplexUnsupportedSyntax() throws Exception {
        String sql = "INSERT INTO employees SET name = 'John', salary = 60000 ON DUPLICATE KEY UPDATE salary = salary + 1000";

        String result = convertMySqlToShentongDirect(sql);

        log.info("复杂不支持语法转换结果: {}", result);

        // 验证转换失败，应该检测到INSERT SET语法
        assertNotNull(result);
        assertTrue(result.contains("-- INSERT ... SET syntax is not supported in Shentong database") ||
                  result.contains("-- INSERT ... ON DUPLICATE KEY UPDATE syntax is not supported in Shentong database"));
    }
}
