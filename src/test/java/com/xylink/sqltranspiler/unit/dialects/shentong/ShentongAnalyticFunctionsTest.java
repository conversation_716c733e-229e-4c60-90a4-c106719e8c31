package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;

/**
 * 神通数据库分析函数（窗口函数）支持测试
 * 基于神通数据库官方文档，验证LAG、LEAD、FIRST_VALUE、LAST_VALUE等分析函数的转换
 */
public class ShentongAnalyticFunctionsTest {

    private static final Logger log = LoggerFactory.getLogger(ShentongAnalyticFunctionsTest.class);

    @Test
    public void testLagFunction() {
        log.info("=== LAG函数测试 ===");
        
        String originalSql = "SELECT employee_id, salary, LAG(salary, 1, 0) OVER (ORDER BY employee_id) as prev_salary FROM employees;";
        log.info("原始SQL：{}", originalSql);
        
        Transpiler transpiler = new Transpiler();
        String result = transpiler.transpile(originalSql, "mysql", "shentong").translatedSql();
        log.info("神通转换结果：{}", result);

        // 验证LAG函数保持不变（神通数据库原生支持）
        assertTrue(result.contains("LAG(salary, 1, 0) OVER (ORDER BY employee_id)"), "LAG函数应该保持不变");
        assertTrue(result.contains("prev_salary"), "别名应该正确保持");

        log.info("✅ LAG函数正确处理，神通数据库原生支持");
        log.info("LAG函数测试完成");
    }

    @Test
    public void testLeadFunction() {
        log.info("=== LEAD函数测试 ===");

        String originalSql = "SELECT employee_id, salary, LEAD(salary, 1, 0) OVER (ORDER BY employee_id) as next_salary FROM employees;";
        log.info("原始SQL：{}", originalSql);

        Transpiler transpiler = new Transpiler();
        String result = transpiler.transpile(originalSql, "mysql", "shentong").translatedSql();
        log.info("神通转换结果：{}", result);

        // 验证LEAD函数保持不变（神通数据库原生支持）
        assertTrue(result.contains("LEAD(salary, 1, 0) OVER (ORDER BY employee_id)"), "LEAD函数应该保持不变");
        assertTrue(result.contains("next_salary"), "别名应该正确保持");

        log.info("✅ LEAD函数正确处理，神通数据库原生支持");
        log.info("LEAD函数测试完成");
    }

    @Test
    public void testFirstValueFunction() {
        log.info("=== FIRST_VALUE函数测试 ===");

        String originalSql = "SELECT employee_id, salary, FIRST_VALUE(salary) OVER (PARTITION BY department_id ORDER BY salary ROWS UNBOUNDED PRECEDING) as first_salary FROM employees;";
        log.info("原始SQL：{}", originalSql);

        Transpiler transpiler = new Transpiler();
        String result = transpiler.transpile(originalSql, "mysql", "shentong").translatedSql();
        log.info("神通转换结果：{}", result);

        // 验证FIRST_VALUE函数保持不变（神通数据库原生支持）
        assertTrue(result.contains("FIRST_VALUE(salary) OVER"), "FIRST_VALUE函数应该保持不变");
        assertTrue(result.contains("PARTITION BY department_id"), "PARTITION BY子句应该正确保持");
        assertTrue(result.contains("ROWS UNBOUNDED PRECEDING"), "窗口框架应该正确保持");

        log.info("✅ FIRST_VALUE函数正确处理，神通数据库原生支持");
        log.info("FIRST_VALUE函数测试完成");
    }

    @Test
    public void testLastValueFunction() {
        log.info("=== LAST_VALUE函数测试 ===");

        String originalSql = "SELECT employee_id, salary, LAST_VALUE(salary) OVER (PARTITION BY department_id ORDER BY salary ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) as last_salary FROM employees;";
        log.info("原始SQL：{}", originalSql);

        Transpiler transpiler = new Transpiler();
        String result = transpiler.transpile(originalSql, "mysql", "shentong").translatedSql();
        log.info("神通转换结果：{}", result);

        // 验证LAST_VALUE函数保持不变（神通数据库原生支持）
        assertTrue(result.contains("LAST_VALUE(salary) OVER"), "LAST_VALUE函数应该保持不变");
        assertTrue(result.contains("PARTITION BY department_id"), "PARTITION BY子句应该正确保持");
        assertTrue(result.contains("ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING"), "窗口框架应该正确保持");

        log.info("✅ LAST_VALUE函数正确处理，神通数据库原生支持");
        log.info("LAST_VALUE函数测试完成");
    }

    @Test
    public void testNtileFunction() {
        log.info("=== NTILE函数测试 ===");

        String originalSql = "SELECT employee_id, salary, NTILE(4) OVER (ORDER BY salary) as quartile FROM employees;";
        log.info("原始SQL：{}", originalSql);

        Transpiler transpiler = new Transpiler();
        String result = transpiler.transpile(originalSql, "mysql", "shentong").translatedSql();
        log.info("神通转换结果：{}", result);

        // 验证NTILE函数保持不变（神通数据库原生支持）
        assertTrue(result.contains("NTILE(4) OVER (ORDER BY salary)"), "NTILE函数应该保持不变");
        assertTrue(result.contains("quartile"), "别名应该正确保持");

        log.info("✅ NTILE函数正确处理，神通数据库原生支持");
        log.info("NTILE函数测试完成");
    }

    @Test
    public void testRankingFunctions() {
        log.info("=== 排名函数测试 ===");

        String originalSql = "SELECT employee_id, salary, " +
                "RANK() OVER (ORDER BY salary DESC) as rank_num, " +
                "DENSE_RANK() OVER (ORDER BY salary DESC) as dense_rank_num, " +
                "ROW_NUMBER() OVER (ORDER BY salary DESC) as row_num, " +
                "PERCENT_RANK() OVER (ORDER BY salary) as percent_rank, " +
                "CUME_DIST() OVER (ORDER BY salary) as cume_dist " +
                "FROM employees;";
        log.info("原始SQL：{}", originalSql);

        Transpiler transpiler = new Transpiler();
        String result = transpiler.transpile(originalSql, "mysql", "shentong").translatedSql();
        log.info("神通转换结果：{}", result);

        // 验证所有排名函数保持不变（神通数据库原生支持）
        assertTrue(result.contains("RANK() OVER (ORDER BY salary DESC)"), "RANK函数应该保持不变");
        assertTrue(result.contains("DENSE_RANK() OVER (ORDER BY salary DESC)"), "DENSE_RANK函数应该保持不变");
        assertTrue(result.contains("ROW_NUMBER() OVER (ORDER BY salary DESC)"), "ROW_NUMBER函数应该保持不变");
        assertTrue(result.contains("PERCENT_RANK() OVER (ORDER BY salary)"), "PERCENT_RANK函数应该保持不变");
        assertTrue(result.contains("CUME_DIST() OVER (ORDER BY salary)"), "CUME_DIST函数应该保持不变");

        log.info("✅ 所有排名函数正确处理，神通数据库原生支持");
        log.info("排名函数测试完成");
    }

    @Test
    public void testComplexAnalyticQuery() {
        log.info("=== 复杂分析函数查询测试 ===");

        String originalSql = "SELECT " +
                "employee_id, " +
                "department_id, " +
                "salary, " +
                "LAG(salary, 1) OVER (PARTITION BY department_id ORDER BY employee_id) as prev_salary, " +
                "LEAD(salary, 1) OVER (PARTITION BY department_id ORDER BY employee_id) as next_salary, " +
                "FIRST_VALUE(salary) OVER (PARTITION BY department_id ORDER BY salary ROWS UNBOUNDED PRECEDING) as min_salary, " +
                "LAST_VALUE(salary) OVER (PARTITION BY department_id ORDER BY salary ROWS BETWEEN UNBOUNDED PRECEDING AND UNBOUNDED FOLLOWING) as max_salary, " +
                "RANK() OVER (PARTITION BY department_id ORDER BY salary DESC) as dept_rank " +
                "FROM employees " +
                "ORDER BY department_id, employee_id;";
        log.info("原始SQL：{}", originalSql);

        Transpiler transpiler = new Transpiler();
        String result = transpiler.transpile(originalSql, "mysql", "shentong").translatedSql();
        log.info("神通转换结果：{}", result);

        // 验证复杂查询中的所有分析函数都正确处理
        assertTrue(result.contains("LAG(salary, 1) OVER"), "LAG函数应该正确处理");
        assertTrue(result.contains("LEAD(salary, 1) OVER"), "LEAD函数应该正确处理");
        assertTrue(result.contains("FIRST_VALUE(salary) OVER"), "FIRST_VALUE函数应该正确处理");
        assertTrue(result.contains("LAST_VALUE(salary) OVER"), "LAST_VALUE函数应该正确处理");
        assertTrue(result.contains("RANK() OVER"), "RANK函数应该正确处理");
        assertTrue(result.contains("PARTITION BY department_id"), "PARTITION BY子句应该正确处理");

        log.info("处理结果统计：");
        log.info("  LAG函数：正确处理");
        log.info("  LEAD函数：正确处理");
        log.info("  FIRST_VALUE函数：正确处理");
        log.info("  LAST_VALUE函数：正确处理");
        log.info("  RANK函数：正确处理");
        log.info("  窗口子句：正确处理");
        log.info("✅ 复杂分析函数查询处理完美");
        log.info("复杂分析函数查询测试完成");
    }

    /**
     * 测试Oracle语法被MySQL强制校验正确拒绝
     * 验证DECODE函数和SYSDATE（不带括号）等Oracle语法被正确检测和拒绝
     */
    @Test
    public void testOracleSyntaxRejection() {
        log.info("=== Oracle语法拒绝测试 ===");

        String oracleSql = "SELECT " +
                "DECODE(department_id, 10, 'Admin', 20, 'Sales', 'Other') as dept_name, " +
                "ROW_NUMBER() OVER (ORDER BY SYSDATE) as row_num " +
                "FROM dual;";
        log.info("Oracle语法SQL：{}", oracleSql);

        Transpiler transpiler = new Transpiler();
        String result = transpiler.transpile(oracleSql, "mysql", "shentong").translatedSql();
        log.info("转换结果：{}", result);

        // 验证Oracle语法被正确拒绝 - 根据MySQL 8.4官方文档，DECODE和SYSDATE（不带括号）不是MySQL语法
        assertTrue(result.isEmpty(), "Oracle语法应该被MySQL强制校验拒绝，转换结果应为空");

        log.info("✅ Oracle语法被正确拒绝");
        log.info("Oracle语法拒绝测试完成");
    }

    /**
     * 测试标准MySQL语法的窗口函数转换
     * 使用正确的MySQL语法进行窗口函数转换测试
     */
    @Test
    public void testMySqlWindowFunctionsWithDual() {
        log.info("=== MySQL标准窗口函数与DUAL表测试 ===");

        // 使用MySQL 8.4官方文档支持的标准语法
        String mysqlSql = "SELECT " +
                "CASE WHEN department_id = 10 THEN 'Admin' WHEN department_id = 20 THEN 'Sales' ELSE 'Other' END as dept_name, " +
                "ROW_NUMBER() OVER (ORDER BY NOW()) as row_num " +
                "FROM dual;";
        log.info("MySQL标准SQL：{}", mysqlSql);

        Transpiler transpiler = new Transpiler();
        String result = transpiler.transpile(mysqlSql, "mysql", "shentong").translatedSql();
        log.info("神通转换结果：{}", result);

        // 验证MySQL标准语法成功转换
        assertFalse(result.isEmpty(), "MySQL标准语法应该成功转换");
        assertTrue(result.contains("CASE WHEN"), "CASE WHEN语法应该被保持");
        assertTrue(result.contains("ROW_NUMBER() OVER"), "ROW_NUMBER窗口函数应该被保持");
        assertTrue(result.contains("FROM dual"), "DUAL表应该被保持（MySQL 8.4官方文档支持）");

        log.info("✅ MySQL标准窗口函数转换成功");
        log.info("MySQL标准窗口函数与DUAL表测试完成");
    }

    @Test
    public void testComprehensiveAnalyticFunctions() {
        log.info("=== 综合分析函数功能验证 ===");

        // 测试多种分析函数的组合使用
        String[] testQueries = {
            "SELECT LAG(salary) OVER (ORDER BY employee_id) FROM employees;",
            "SELECT LEAD(salary, 2, 0) OVER (PARTITION BY dept ORDER BY salary) FROM employees;",
            "SELECT FIRST_VALUE(name) OVER (ORDER BY salary ROWS UNBOUNDED PRECEDING) FROM employees;",
            "SELECT NTILE(10) OVER (ORDER BY salary) FROM employees;"
        };

        Transpiler transpiler = new Transpiler();
        int successCount = 0;

        for (int i = 0; i < testQueries.length; i++) {
            log.info("综合测试 {}: {}", i + 1, testQueries[i]);
            String result = transpiler.transpile(testQueries[i], "mysql", "shentong").translatedSql();
            log.info("综合转换结果 {}: {}", i + 1, result);

            // 验证基本转换成功
            assertNotNull(result, "转换结果不应为null");
            assertFalse(result.trim().isEmpty(), "转换结果不应为空");
            assertTrue(result.contains("OVER"), "应该包含OVER子句");

            successCount++;
            log.info("✅ 综合测试 {} 成功", i + 1);
        }

        log.info("综合分析函数测试结果：{}/{} 成功，成功率：{:.1f}%",
                successCount, testQueries.length, (successCount * 100.0 / testQueries.length));
        log.info("✅ 综合分析函数功能支持良好");
        log.info("综合分析函数功能验证完成");
    }
}
