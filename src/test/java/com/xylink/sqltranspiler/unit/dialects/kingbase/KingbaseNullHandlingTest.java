package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金仓数据库NULL处理测试
 * 
 * 根据金仓官方文档，测试MySQL NULL值处理到金仓的转换
 * 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * 
 * 测试覆盖：
 * - NULL和NOT NULL约束的转换
 * - 默认值与NULL的处理
 * - NULL值在不同数据类型中的处理
 * - NULL相关函数的转换
 */
@DisplayName("金仓数据库NULL处理测试")
public class KingbaseNullHandlingTest {

    private KingbaseGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new KingbaseGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本NULL约束转换")
    void testBasicNullConstraints() {
        String mysqlSql = "CREATE TABLE null_constraints (" +
                "id INT NOT NULL, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(100), " +
                "phone VARCHAR(20) NULL, " +
                "description TEXT" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("基本NULL约束转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证NULL约束转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"null_constraints\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("\"id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"email\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"phone\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"description\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试NULL与默认值组合")
    void testNullWithDefaultValues() {
        String mysqlSql = "CREATE TABLE null_defaults (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "status VARCHAR(20) DEFAULT 'active', " +
                "count_value INT DEFAULT 0, " +
                "nullable_count INT DEFAULT NULL, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP NULL DEFAULT NULL" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("NULL与默认值组合转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证NULL与默认值组合
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"null_defaults\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("'active'"), "应保留字符串默认值");
        assertTrue(kingbaseSql.contains("DEFAULT 0"), "应保留数值默认值");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("CURRENT_TIMESTAMP"), "应保留时间函数");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试不同数据类型的NULL处理")
    void testNullHandlingForDifferentDataTypes() {
        String mysqlSql = "CREATE TABLE null_data_types (" +
                "int_null INT NULL, " +
                "int_not_null INT NOT NULL, " +
                "varchar_null VARCHAR(255), " +
                "varchar_not_null VARCHAR(255) NOT NULL, " +
                "text_null TEXT, " +
                "text_not_null TEXT NOT NULL, " +
                "decimal_null DECIMAL(10,2), " +
                "decimal_not_null DECIMAL(10,2) NOT NULL, " +
                "date_null DATE, " +
                "date_not_null DATE NOT NULL, " +
                "timestamp_null TIMESTAMP, " +
                "timestamp_not_null TIMESTAMP NOT NULL, " +
                "boolean_null BOOLEAN, " +
                "boolean_not_null BOOLEAN NOT NULL" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("不同数据类型NULL处理结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证不同数据类型的NULL处理
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"null_data_types\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("INT"), "应保留INT类型");
        assertTrue(kingbaseSql.contains("VARCHAR(255)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        assertTrue(kingbaseSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        assertTrue(kingbaseSql.contains("DATE"), "应保留DATE类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("BOOLEAN"), "应保留BOOLEAN类型");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试NULL在主键和唯一约束中的处理")
    void testNullInKeyConstraints() {
        String mysqlSql = "CREATE TABLE null_key_constraints (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "unique_not_null VARCHAR(100) NOT NULL UNIQUE, " +
                "unique_nullable VARCHAR(100) UNIQUE, " +
                "composite_key1 INT NOT NULL, " +
                "composite_key2 VARCHAR(50) NOT NULL, " +
                "INDEX idx_composite (composite_key1, composite_key2), " +
                "UNIQUE KEY uk_composite (composite_key1, composite_key2)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("NULL在键约束中的处理结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证NULL在键约束中的处理
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"null_key_constraints\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(kingbaseSql.contains("\"unique_not_null\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"unique_nullable\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"composite_key1\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"composite_key2\""), "列名应使用双引号");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试NULL在外键约束中的处理")
    void testNullInForeignKeyConstraints() {
        String mysqlSql = "CREATE TABLE null_foreign_keys (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "required_user_id INT NOT NULL, " +
                "optional_category_id INT, " +
                "nullable_parent_id INT NULL, " +
                "FOREIGN KEY (required_user_id) REFERENCES users(id), " +
                "FOREIGN KEY (optional_category_id) REFERENCES categories(id) ON DELETE SET NULL, " +
                "FOREIGN KEY (nullable_parent_id) REFERENCES null_foreign_keys(id) ON DELETE CASCADE" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("NULL在外键约束中的处理结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证NULL在外键约束中的处理
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"null_foreign_keys\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("\"required_user_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"optional_category_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"nullable_parent_id\""), "列名应使用双引号");
        
        // 验证外键处理（金仓支持PostgreSQL兼容的外键）
        assertTrue(kingbaseSql.contains("FOREIGN KEY") || kingbaseSql.contains("REFERENCES") || 
                  kingbaseSql.contains("required_user_id"), "应处理外键约束");
        assertTrue(kingbaseSql.contains("\"users\"") || kingbaseSql.contains("users"), 
                  "应处理引用表名");
        assertTrue(kingbaseSql.contains("\"categories\"") || kingbaseSql.contains("categories"), 
                  "应处理引用表名");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试NULL在CHECK约束中的处理")
    void testNullInCheckConstraints() {
        String mysqlSql = "CREATE TABLE null_check_constraints (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "age INT CHECK (age IS NULL OR age >= 0), " +
                "salary DECIMAL(10,2) CHECK (salary IS NULL OR salary > 0), " +
                "email VARCHAR(100) CHECK (email IS NULL OR email LIKE '%@%'), " +
                "status VARCHAR(20) NOT NULL CHECK (status IN ('active', 'inactive', 'pending'))" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("NULL在CHECK约束中的处理结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证NULL在CHECK约束中的处理
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"null_check_constraints\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("\"age\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"salary\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"email\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"status\""), "列名应使用双引号");
        
        // 验证CHECK约束处理（金仓支持PostgreSQL兼容的CHECK约束）
        assertTrue(kingbaseSql.contains("CHECK") || kingbaseSql.contains("age"), 
                  "应处理CHECK约束");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试NULL值的默认行为")
    void testNullDefaultBehavior() {
        String mysqlSql = "CREATE TABLE null_default_behavior (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "implicit_null VARCHAR(100), " +
                "explicit_null VARCHAR(100) NULL, " +
                "explicit_not_null VARCHAR(100) NOT NULL, " +
                "with_default VARCHAR(100) DEFAULT 'default_value', " +
                "null_with_default VARCHAR(100) NULL DEFAULT 'default_value', " +
                "timestamp_implicit TIMESTAMP, " +
                "timestamp_explicit TIMESTAMP NULL, " +
                "timestamp_not_null TIMESTAMP NOT NULL" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("NULL默认行为处理结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证NULL默认行为
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"null_default_behavior\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("'default_value'"), "应保留字符串默认值");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试金仓PostgreSQL兼容的NULL处理特性")
    void testKingbasePostgreSQLNullFeatures() {
        String mysqlSql = "CREATE TABLE kingbase_null_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "json_data JSON, " +
                "text_data TEXT NOT NULL, " +
                "blob_data LONGBLOB, " +
                "numeric_data DECIMAL(15,4) DEFAULT NULL, " +
                "boolean_data BOOLEAN NULL DEFAULT NULL, " +
                "array_data TEXT, " +
                "timestamp_data TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓PostgreSQL兼容NULL处理特性:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证金仓PostgreSQL兼容NULL处理特性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"kingbase_null_features\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        
        // 验证PostgreSQL兼容类型的NULL处理
        assertTrue(kingbaseSql.contains("JSON"), "应支持JSON类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        assertTrue(kingbaseSql.contains("LONGBLOB") || kingbaseSql.contains("BYTEA"), 
                  "应保留或转换大二进制类型");
        assertTrue(kingbaseSql.contains("DECIMAL(15,4)"), "应保留DECIMAL类型");
        assertTrue(kingbaseSql.contains("BOOLEAN"), "应保留BOOLEAN类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        
        // 验证NULL约束和默认值
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("CURRENT_TIMESTAMP"), "应保留时间函数");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试复杂NULL处理场景")
    void testComplexNullHandlingScenario() {
        String mysqlSql = "CREATE TABLE complex_null_handling (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "required_name VARCHAR(100) NOT NULL, " +
                "optional_description TEXT, " +
                "nullable_price DECIMAL(12,2) DEFAULT NULL, " +
                "status ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft', " +
                "created_by INT NOT NULL, " +
                "updated_by INT, " +
                "created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP NULL DEFAULT NULL, " +
                "metadata JSON, " +
                "is_active BOOLEAN DEFAULT TRUE, " +
                "FOREIGN KEY (created_by) REFERENCES users(id), " +
                "FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL, " +
                "CHECK (nullable_price IS NULL OR nullable_price >= 0)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("复杂NULL处理场景结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证复杂NULL处理场景
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"complex_null_handling\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("BIGSERIAL") || kingbaseSql.contains("BIGINT"), 
                  "应转换BIGINT AUTO_INCREMENT为BIGSERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        assertTrue(kingbaseSql.contains("DECIMAL(12,2)"), "应保留DECIMAL类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("JSON"), "应保留JSON类型");
        assertTrue(kingbaseSql.contains("BOOLEAN"), "应保留BOOLEAN类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("'draft'"), "应保留字符串默认值");
        assertTrue(kingbaseSql.contains("TRUE"), "应保留布尔默认值");
        
        // 验证时间函数
        assertTrue(kingbaseSql.contains("CURRENT_TIMESTAMP"), "应保留时间函数");
        
        // 验证约束处理
        assertTrue(kingbaseSql.contains("FOREIGN KEY") || kingbaseSql.contains("REFERENCES") || 
                  kingbaseSql.contains("created_by"), "应处理外键约束");
        assertTrue(kingbaseSql.contains("CHECK") || kingbaseSql.contains("nullable_price"), 
                  "应处理CHECK约束");
        assertTrue(kingbaseSql.contains("\"users\"") || kingbaseSql.contains("users"), 
                  "应处理引用表名");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }
}
