package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 达梦数据库高级特性测试
 * 
 * 根据达梦官方文档，测试MySQL高级特性到达梦的转换
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 */
@DisplayName("达梦数据库高级特性测试")
public class DamengAdvancedFeaturesTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试复杂表结构转换")
    void testComplexTableStructureConversion() {
        String mysqlSql = "CREATE TABLE complex_table (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "uuid CHAR(36) NOT NULL UNIQUE, " +
                "data JSON, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "status ENUM('active', 'inactive', 'pending') DEFAULT 'active', " +
                "metadata TEXT, " +
                "INDEX idx_status (status), " +
                "INDEX idx_created (created_at)" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("复杂表结构转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证复杂表结构转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("complex_table"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY") || damengSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("UNIQUE"), "应保留唯一约束");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), "应转换TEXT为CLOB或保留TEXT");
    }

    @Test
    @DisplayName("测试高级数据类型转换")
    void testAdvancedDataTypeConversion() {
        String mysqlSql = "CREATE TABLE advanced_types (" +
                "id INT PRIMARY KEY, " +
                "binary_data BINARY(16), " +
                "var_binary VARBINARY(255), " +
                "tiny_blob TINYBLOB, " +
                "medium_blob MEDIUMBLOB, " +
                "long_blob LONGBLOB, " +
                "tiny_text TINYTEXT, " +
                "medium_text MEDIUMTEXT, " +
                "long_text LONGTEXT, " +
                "year_col YEAR, " +
                "bit_col BIT(8)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("高级数据类型转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证高级数据类型转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("advanced_types"), "应保留表名");
        assertTrue(damengSql.contains("BINARY") || damengSql.contains("VARBINARY"), 
                  "应转换或保留二进制类型");
        assertTrue(damengSql.contains("BLOB") || damengSql.contains("CLOB"), 
                  "应转换或保留大对象类型");
        assertTrue(damengSql.contains("TEXT") || damengSql.contains("CLOB"), 
                  "应转换或保留文本类型");
    }

    @Test
    @DisplayName("测试高级约束转换")
    void testAdvancedConstraintConversion() {
        String mysqlSql = "CREATE TABLE advanced_constraints (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "email VARCHAR(100) NOT NULL, " +
                "age INT CHECK (age >= 0 AND age <= 150), " +
                "salary DECIMAL(10,2) CHECK (salary > 0), " +
                "department_id INT, " +
                "CONSTRAINT uk_email UNIQUE (email), " +
                "CONSTRAINT fk_department FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL, " +
                "CONSTRAINT chk_email_format CHECK (email LIKE '%@%.%')" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("高级约束转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证高级约束转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("advanced_constraints"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键约束");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("CHECK"), "应保留CHECK约束");
        assertTrue(damengSql.contains("UNIQUE"), "应保留UNIQUE约束");

        // 验证外键约束（达梦可能在单独的语句中处理外键）
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("department_id"),
                  "应处理外键约束");
        assertTrue(damengSql.contains("REFERENCES") || damengSql.contains("department_id"),
                  "应处理引用关系");
    }

    @Test
    @DisplayName("测试高级索引转换")
    void testAdvancedIndexConversion() {
        String mysqlSql = "CREATE TABLE advanced_indexes (" +
                "id INT PRIMARY KEY, " +
                "title VARCHAR(200), " +
                "content TEXT, " +
                "tags VARCHAR(500), " +
                "created_at TIMESTAMP, " +
                "INDEX idx_title (title), " +
                "INDEX idx_title_partial (title(50)), " +
                "INDEX idx_composite (title, created_at), " +
                "FULLTEXT INDEX ft_content (content), " +
                "FULLTEXT INDEX ft_title_content (title, content)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("高级索引转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证高级索引转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("advanced_indexes"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        
        // 验证索引转换（达梦可能不支持所有MySQL索引特性）
        assertTrue(damengSql.contains("idx_title") || damengSql.contains("INDEX"), 
                  "应处理索引定义");
    }

    @Test
    @DisplayName("测试高级函数和表达式转换")
    void testAdvancedFunctionConversion() {
        String mysqlSql = "CREATE TABLE advanced_functions (" +
                "id INT PRIMARY KEY, " +
                "name VARCHAR(100), " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "hash_value VARCHAR(64) DEFAULT (SHA2(CONCAT(name, UNIX_TIMESTAMP()), 256)), " +
                "random_value DOUBLE DEFAULT (RAND()), " +
                "uuid_value VARCHAR(36) DEFAULT (UUID())" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("高级函数转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证高级函数转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("advanced_functions"), "应保留表名");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证时间戳函数转换
        assertTrue(damengSql.contains("CURRENT_TIMESTAMP") || damengSql.contains("SYSDATE") || 
                  damengSql.contains("NOW"), "应转换时间戳函数");
    }

    @Test
    @DisplayName("测试高级字符集和排序规则转换")
    void testAdvancedCharsetCollationConversion() {
        String mysqlSql = "CREATE TABLE advanced_charset (" +
                "id INT PRIMARY KEY, " +
                "utf8_col VARCHAR(100) CHARACTER SET utf8 COLLATE utf8_general_ci, " +
                "utf8mb4_col VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci, " +
                "latin1_col VARCHAR(100) CHARACTER SET latin1 COLLATE latin1_swedish_ci, " +
                "binary_col VARCHAR(100) CHARACTER SET binary" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("高级字符集转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证字符集转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("advanced_charset"), "应保留表名");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        
        // 验证字符集处理（达梦可能移除MySQL特有的字符集声明）
        assertTrue(damengSql.contains("utf8_col"), "应保留列名");
        assertTrue(damengSql.contains("utf8mb4_col"), "应保留列名");
        assertTrue(damengSql.contains("latin1_col"), "应保留列名");
        assertTrue(damengSql.contains("binary_col"), "应保留列名");
    }

    @Test
    @DisplayName("测试高级存储引擎转换")
    void testAdvancedStorageEngineConversion() {
        String mysqlSql = "CREATE TABLE advanced_engine (" +
                "id INT PRIMARY KEY, " +
                "data VARCHAR(100)" +
                ") ENGINE=InnoDB " +
                "AUTO_INCREMENT=1000 " +
                "DEFAULT CHARSET=utf8mb4 " +
                "COLLATE=utf8mb4_unicode_ci " +
                "COMMENT='高级存储引擎测试表' " +
                "ROW_FORMAT=DYNAMIC " +
                "KEY_BLOCK_SIZE=8;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("高级存储引擎转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证存储引擎转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("advanced_engine"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留数据类型");
        
        // 验证注释保留
        assertTrue(damengSql.contains("COMMENT") || damengSql.contains("高级存储引擎测试表"), 
                  "应保留表注释");
    }

    @Test
    @DisplayName("测试高级分区表转换")
    void testAdvancedPartitionConversion() {
        String mysqlSql = "CREATE TABLE advanced_partition (" +
                "id INT AUTO_INCREMENT, " +
                "user_id INT NOT NULL, " +
                "order_date DATE NOT NULL, " +
                "amount DECIMAL(10,2), " +
                "status VARCHAR(20), " +
                "PRIMARY KEY (id, order_date)" +
                ") PARTITION BY RANGE (YEAR(order_date)) (" +
                "PARTITION p2020 VALUES LESS THAN (2021), " +
                "PARTITION p2021 VALUES LESS THAN (2022), " +
                "PARTITION p2022 VALUES LESS THAN (2023), " +
                "PARTITION p_future VALUES LESS THAN MAXVALUE" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("高级分区表转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证分区表转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("advanced_partition"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        
        // 验证分区转换（达梦支持分区表）
        assertTrue(damengSql.contains("PARTITION") || damengSql.contains("advanced_partition"), 
                  "应处理分区定义");
    }
}
