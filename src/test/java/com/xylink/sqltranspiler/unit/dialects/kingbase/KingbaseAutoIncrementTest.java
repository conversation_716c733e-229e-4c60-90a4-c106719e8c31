package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金仓数据库自增字段测试
 * 
 * 根据金仓官方文档，测试MySQL AUTO_INCREMENT到金仓SERIAL的转换
 * 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 */
@DisplayName("金仓数据库自增字段测试")
public class KingbaseAutoIncrementTest {

    private KingbaseGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new KingbaseGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本AUTO_INCREMENT转换")
    void testBasicAutoIncrementConversion() {
        String mysqlSql = "CREATE TABLE auto_increment_basic (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("基本AUTO_INCREMENT转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证AUTO_INCREMENT转换为SERIAL
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"auto_increment_basic\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留数据类型");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试BIGINT AUTO_INCREMENT转换")
    void testBigintAutoIncrementConversion() {
        String mysqlSql = "CREATE TABLE auto_increment_bigint (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "data VARCHAR(255)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("BIGINT AUTO_INCREMENT转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证BIGINT AUTO_INCREMENT转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"auto_increment_bigint\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("BIGSERIAL") || kingbaseSql.contains("BIGINT"), 
                  "应转换BIGINT AUTO_INCREMENT为BIGSERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"data\""), "列名应使用双引号");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试带起始值的AUTO_INCREMENT转换")
    void testAutoIncrementWithStartValue() {
        String mysqlSql = "CREATE TABLE auto_increment_start (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100)" +
                ") AUTO_INCREMENT=1000;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("带起始值的AUTO_INCREMENT转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证带起始值的AUTO_INCREMENT转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"auto_increment_start\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        
        // 验证起始值处理（金仓可能需要单独的ALTER SEQUENCE语句）
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("1000") || 
                  kingbaseSql.contains("auto_increment_start"), "应处理起始值");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试复合主键中的AUTO_INCREMENT转换")
    void testAutoIncrementInCompositeKey() {
        String mysqlSql = "CREATE TABLE auto_increment_composite (" +
                "id INT AUTO_INCREMENT, " +
                "category_id INT NOT NULL, " +
                "name VARCHAR(100), " +
                "PRIMARY KEY (id, category_id)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("复合主键中的AUTO_INCREMENT转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证复合主键中的AUTO_INCREMENT转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"auto_increment_composite\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留复合主键");
        assertTrue(kingbaseSql.contains("\"category_id\""), "复合主键列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"name\""), "列名应使用双引号");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试多个表的AUTO_INCREMENT转换")
    void testMultipleTablesAutoIncrement() {
        String mysqlSql = "CREATE TABLE users (" +
                "user_id INT AUTO_INCREMENT PRIMARY KEY, " +
                "username VARCHAR(50) UNIQUE NOT NULL, " +
                "email VARCHAR(100) NOT NULL" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("用户表AUTO_INCREMENT转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证用户表的AUTO_INCREMENT转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"users\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(kingbaseSql.contains("\"username\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"email\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(50)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试AUTO_INCREMENT与其他约束的组合")
    void testAutoIncrementWithConstraints() {
        String mysqlSql = "CREATE TABLE orders (" +
                "order_id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "user_id INT NOT NULL, " +
                "order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount > 0), " +
                "status ENUM('pending', 'paid', 'shipped', 'delivered') DEFAULT 'pending', " +
                "FOREIGN KEY (user_id) REFERENCES users(user_id)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("AUTO_INCREMENT与约束组合转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证AUTO_INCREMENT与约束的组合转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"orders\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("BIGSERIAL") || kingbaseSql.contains("BIGINT"), 
                  "应转换BIGINT AUTO_INCREMENT为BIGSERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        assertTrue(kingbaseSql.contains("\"user_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"order_date\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"total_amount\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"status\""), "列名应使用双引号");
        
        // 验证约束处理（金仓可能在单独的语句中处理约束）
        assertTrue(kingbaseSql.contains("CHECK") || kingbaseSql.contains("total_amount"), 
                  "应处理CHECK约束");
        assertTrue(kingbaseSql.contains("FOREIGN KEY") || kingbaseSql.contains("REFERENCES"), 
                  "应处理外键约束");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试AUTO_INCREMENT与索引的组合")
    void testAutoIncrementWithIndexes() {
        String mysqlSql = "CREATE TABLE products (" +
                "product_id INT AUTO_INCREMENT PRIMARY KEY, " +
                "sku VARCHAR(50) UNIQUE NOT NULL, " +
                "name VARCHAR(200) NOT NULL, " +
                "category_id INT, " +
                "price DECIMAL(8,2), " +
                "INDEX idx_category (category_id), " +
                "INDEX idx_price (price), " +
                "INDEX idx_name (name)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("AUTO_INCREMENT与索引组合转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证AUTO_INCREMENT与索引的组合转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"products\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(kingbaseSql.contains("\"sku\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"category_id\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"price\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(50)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("VARCHAR(200)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("DECIMAL(8,2)"), "应保留DECIMAL类型");
        
        // 验证索引处理（金仓可能在单独的语句中处理索引）
        assertTrue(kingbaseSql.contains("idx_category") || kingbaseSql.contains("category_id"), 
                  "应处理分类索引");
        assertTrue(kingbaseSql.contains("idx_price") || kingbaseSql.contains("price"), 
                  "应处理价格索引");
        assertTrue(kingbaseSql.contains("idx_name") || kingbaseSql.contains("name"), 
                  "应处理名称索引");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试AUTO_INCREMENT在分区表中的转换")
    void testAutoIncrementInPartitionedTable() {
        String mysqlSql = "CREATE TABLE sales_data (" +
                "id BIGINT AUTO_INCREMENT, " +
                "sale_date DATE NOT NULL, " +
                "amount DECIMAL(10,2), " +
                "region VARCHAR(50), " +
                "PRIMARY KEY (id, sale_date)" +
                ") PARTITION BY RANGE (YEAR(sale_date)) (" +
                "PARTITION p2020 VALUES LESS THAN (2021), " +
                "PARTITION p2021 VALUES LESS THAN (2022), " +
                "PARTITION p2022 VALUES LESS THAN (2023)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("分区表中AUTO_INCREMENT转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证分区表中的AUTO_INCREMENT转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"sales_data\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("BIGSERIAL") || kingbaseSql.contains("BIGINT"), 
                  "应转换BIGINT AUTO_INCREMENT为BIGSERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"sale_date\""), "分区键列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"amount\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"region\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("DATE"), "应保留DATE类型");
        assertTrue(kingbaseSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        
        // 验证分区处理（金仓支持PostgreSQL兼容的分区）
        assertTrue(kingbaseSql.contains("PARTITION") || kingbaseSql.contains("sales_data"), 
                  "应处理分区定义");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试金仓SERIAL特有特性")
    void testKingbaseSerialSpecificFeatures() {
        String mysqlSql = "CREATE TABLE kingbase_serial_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "code VARCHAR(20) NOT NULL, " +
                "description TEXT, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓SERIAL特有特性:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证金仓SERIAL特有特性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"kingbase_serial_features\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"code\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"description\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"created_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"updated_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(20)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("CURRENT_TIMESTAMP"), "应保留时间函数");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }
}
