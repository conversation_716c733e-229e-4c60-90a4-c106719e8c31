package com.xylink.sqltranspiler.unit.dialects;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.privilege.Grant;
import com.xylink.sqltranspiler.core.ast.privilege.Revoke;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;

/**
 * 权限管理功能集成测试
 * 验证解析器层面的GRANT和REVOKE语句解析支持以及三种国产数据库的权限管理功能
 * 
 * 基于官方文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/grant.html
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/definition-statement.html
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: 神通数据库官方文档权限管理章节
 */
@DisplayName("权限管理功能集成测试")
public class PrivilegeManagementIntegrationTest {

    private DamengGenerator damengGenerator;
    private KingbaseGenerator kingbaseGenerator;
    private ShentongGenerator shentongGenerator;

    @BeforeEach
    public void setUp() {
        damengGenerator = new DamengGenerator();
        kingbaseGenerator = new KingbaseGenerator();
        shentongGenerator = new ShentongGenerator();
    }

    @Test
    @DisplayName("测试基本GRANT语句在三种数据库中的转换")
    public void testBasicGrantStatementConversion() {
        String originalSql = "GRANT SELECT, INSERT ON test_table TO user1";
        Grant grant = new Grant(originalSql);

        // 测试达梦数据库转换
        String damengResult = damengGenerator.generate(grant);
        assertTrue(damengResult.contains("GRANT"));
        assertTrue(damengResult.contains("SELECT, INSERT"));
        assertTrue(damengResult.contains("ON"));
        assertTrue(damengResult.contains("test_table"));
        assertTrue(damengResult.contains("TO"));
        assertTrue(damengResult.contains("user1"));
        assertTrue(damengResult.endsWith(";"));

        // 测试金仓数据库转换
        String kingbaseResult = kingbaseGenerator.generate(grant);
        assertTrue(kingbaseResult.contains("GRANT"));
        assertTrue(kingbaseResult.contains("SELECT, INSERT"));
        assertTrue(kingbaseResult.contains("ON"));
        assertTrue(kingbaseResult.contains("test_table"));
        assertTrue(kingbaseResult.contains("TO"));
        assertTrue(kingbaseResult.contains("user1"));
        assertTrue(kingbaseResult.endsWith(";"));

        // 测试神通数据库转换
        String shentongResult = shentongGenerator.generate(grant);
        assertTrue(shentongResult.contains("GRANT"));
        assertTrue(shentongResult.contains("SELECT, INSERT"));
        assertTrue(shentongResult.contains("ON"));
        assertTrue(shentongResult.contains("test_table"));
        assertTrue(shentongResult.contains("TO"));
        assertTrue(shentongResult.contains("user1"));
        assertTrue(shentongResult.endsWith(";"));
    }

    @Test
    @DisplayName("测试基本REVOKE语句在三种数据库中的转换")
    public void testBasicRevokeStatementConversion() {
        String originalSql = "REVOKE SELECT, INSERT ON test_table FROM user1";
        Revoke revoke = new Revoke(originalSql);

        // 测试达梦数据库转换
        String damengResult = damengGenerator.generate(revoke);
        assertTrue(damengResult.contains("REVOKE"));
        assertTrue(damengResult.contains("SELECT, INSERT"));
        assertTrue(damengResult.contains("ON"));
        assertTrue(damengResult.contains("test_table"));
        assertTrue(damengResult.contains("FROM"));
        assertTrue(damengResult.contains("user1"));
        assertTrue(damengResult.endsWith(";"));

        // 测试金仓数据库转换
        String kingbaseResult = kingbaseGenerator.generate(revoke);
        assertTrue(kingbaseResult.contains("REVOKE"));
        assertTrue(kingbaseResult.contains("SELECT, INSERT"));
        assertTrue(kingbaseResult.contains("ON"));
        assertTrue(kingbaseResult.contains("test_table"));
        assertTrue(kingbaseResult.contains("FROM"));
        assertTrue(kingbaseResult.contains("user1"));
        assertTrue(kingbaseResult.endsWith(";"));

        // 测试神通数据库转换
        String shentongResult = shentongGenerator.generate(revoke);
        assertTrue(shentongResult.contains("REVOKE"));
        assertTrue(shentongResult.contains("SELECT, INSERT"));
        assertTrue(shentongResult.contains("ON"));
        assertTrue(shentongResult.contains("test_table"));
        assertTrue(shentongResult.contains("FROM"));
        assertTrue(shentongResult.contains("user1"));
        assertTrue(shentongResult.endsWith(";"));
    }

    @Test
    @DisplayName("测试GRANT ALL PRIVILEGES语句在三种数据库中的转换")
    public void testGrantAllPrivilegesConversion() {
        String originalSql = "GRANT ALL PRIVILEGES ON testdb.* TO user2";
        Grant grant = new Grant(originalSql);

        // 测试达梦数据库转换
        String damengResult = damengGenerator.generate(grant);
        assertTrue(damengResult.contains("GRANT"));
        assertTrue(damengResult.contains("ALL PRIVILEGES"));
        assertTrue(damengResult.contains("ON"));
        assertTrue(damengResult.contains("testdb.*"));
        assertTrue(damengResult.contains("TO"));
        assertTrue(damengResult.contains("user2"));
        assertTrue(damengResult.endsWith(";"));

        // 测试金仓数据库转换
        String kingbaseResult = kingbaseGenerator.generate(grant);
        assertTrue(kingbaseResult.contains("GRANT"));
        assertTrue(kingbaseResult.contains("ALL PRIVILEGES"));
        assertTrue(kingbaseResult.contains("ON"));
        assertTrue(kingbaseResult.contains("testdb.*"));
        assertTrue(kingbaseResult.contains("TO"));
        assertTrue(kingbaseResult.contains("user2"));
        assertTrue(kingbaseResult.endsWith(";"));

        // 测试神通数据库转换
        String shentongResult = shentongGenerator.generate(grant);
        assertTrue(shentongResult.contains("GRANT"));
        assertTrue(shentongResult.contains("ALL PRIVILEGES"));
        assertTrue(shentongResult.contains("ON"));
        assertTrue(shentongResult.contains("testdb.*"));
        assertTrue(shentongResult.contains("TO"));
        assertTrue(shentongResult.contains("user2"));
        assertTrue(shentongResult.endsWith(";"));
    }

    @Test
    @DisplayName("测试反引号转换在权限管理语句中的正确处理")
    public void testBacktickConversionInPrivilegeStatements() {
        String originalSql = "GRANT SELECT ON `testdb`.`table` TO `user`";
        Grant grant = new Grant(originalSql);

        // 测试达梦数据库转换
        String damengResult = damengGenerator.generate(grant);
        assertTrue(damengResult.contains("GRANT"));
        assertTrue(damengResult.contains("SELECT"));
        assertTrue(damengResult.contains("ON"));
        assertTrue(damengResult.contains("\"testdb\".\"table\""));
        assertTrue(damengResult.contains("TO"));
        assertTrue(damengResult.contains("\"user\""));
        assertFalse(damengResult.contains("`")); // 确保反引号被转换
        assertTrue(damengResult.endsWith(";"));

        // 测试金仓数据库转换
        String kingbaseResult = kingbaseGenerator.generate(grant);
        assertTrue(kingbaseResult.contains("GRANT"));
        assertTrue(kingbaseResult.contains("SELECT"));
        assertTrue(kingbaseResult.contains("ON"));
        assertTrue(kingbaseResult.contains("\"testdb\".\"table\""));
        assertTrue(kingbaseResult.contains("TO"));
        assertTrue(kingbaseResult.contains("\"user\""));
        assertFalse(kingbaseResult.contains("`")); // 确保反引号被转换
        assertTrue(kingbaseResult.endsWith(";"));

        // 测试神通数据库转换
        String shentongResult = shentongGenerator.generate(grant);
        assertTrue(shentongResult.contains("GRANT"));
        assertTrue(shentongResult.contains("SELECT"));
        assertTrue(shentongResult.contains("ON"));
        assertTrue(shentongResult.contains("\"testdb\".\"table\""));
        assertTrue(shentongResult.contains("TO"));
        assertTrue(shentongResult.contains("\"user\""));
        assertFalse(shentongResult.contains("`")); // 确保反引号被转换
        assertTrue(shentongResult.endsWith(";"));
    }

    @Test
    @DisplayName("测试GRANT WITH GRANT OPTION语句转换")
    public void testGrantWithGrantOptionConversion() {
        String originalSql = "GRANT SELECT, UPDATE ON test_table TO user3 WITH GRANT OPTION";
        Grant grant = new Grant(originalSql);

        // 测试达梦数据库转换
        String damengResult = damengGenerator.generate(grant);
        assertTrue(damengResult.contains("GRANT"));
        assertTrue(damengResult.contains("SELECT, UPDATE"));
        assertTrue(damengResult.contains("ON"));
        assertTrue(damengResult.contains("test_table"));
        assertTrue(damengResult.contains("TO"));
        assertTrue(damengResult.contains("user3"));
        assertTrue(damengResult.contains("WITH GRANT OPTION"));
        assertTrue(damengResult.endsWith(";"));

        // 测试金仓数据库转换
        String kingbaseResult = kingbaseGenerator.generate(grant);
        assertTrue(kingbaseResult.contains("GRANT"));
        assertTrue(kingbaseResult.contains("SELECT, UPDATE"));
        assertTrue(kingbaseResult.contains("ON"));
        assertTrue(kingbaseResult.contains("test_table"));
        assertTrue(kingbaseResult.contains("TO"));
        assertTrue(kingbaseResult.contains("user3"));
        assertTrue(kingbaseResult.contains("WITH GRANT OPTION"));
        assertTrue(kingbaseResult.endsWith(";"));

        // 测试神通数据库转换
        String shentongResult = shentongGenerator.generate(grant);
        assertTrue(shentongResult.contains("GRANT"));
        assertTrue(shentongResult.contains("SELECT, UPDATE"));
        assertTrue(shentongResult.contains("ON"));
        assertTrue(shentongResult.contains("test_table"));
        assertTrue(shentongResult.contains("TO"));
        assertTrue(shentongResult.contains("user3"));
        assertTrue(shentongResult.contains("WITH GRANT OPTION"));
        assertTrue(shentongResult.endsWith(";"));
    }

    @Test
    @DisplayName("测试REVOKE ALL PRIVILEGES语句转换")
    public void testRevokeAllPrivilegesConversion() {
        String originalSql = "REVOKE ALL PRIVILEGES ON testdb.* FROM user2";
        Revoke revoke = new Revoke(originalSql);

        // 测试达梦数据库转换
        String damengResult = damengGenerator.generate(revoke);
        assertTrue(damengResult.contains("REVOKE"));
        assertTrue(damengResult.contains("ALL PRIVILEGES"));
        assertTrue(damengResult.contains("ON"));
        assertTrue(damengResult.contains("testdb.*"));
        assertTrue(damengResult.contains("FROM"));
        assertTrue(damengResult.contains("user2"));
        assertTrue(damengResult.endsWith(";"));

        // 测试金仓数据库转换
        String kingbaseResult = kingbaseGenerator.generate(revoke);
        assertTrue(kingbaseResult.contains("REVOKE"));
        assertTrue(kingbaseResult.contains("ALL PRIVILEGES"));
        assertTrue(kingbaseResult.contains("ON"));
        assertTrue(kingbaseResult.contains("testdb.*"));
        assertTrue(kingbaseResult.contains("FROM"));
        assertTrue(kingbaseResult.contains("user2"));
        assertTrue(kingbaseResult.endsWith(";"));

        // 测试神通数据库转换
        String shentongResult = shentongGenerator.generate(revoke);
        assertTrue(shentongResult.contains("REVOKE"));
        assertTrue(shentongResult.contains("ALL PRIVILEGES"));
        assertTrue(shentongResult.contains("ON"));
        assertTrue(shentongResult.contains("testdb.*"));
        assertTrue(shentongResult.contains("FROM"));
        assertTrue(shentongResult.contains("user2"));
        assertTrue(shentongResult.endsWith(";"));
    }

    @Test
    @DisplayName("验证权限管理语句不会被错误转换")
    public void testPrivilegeStatementsNotMisconverted() {
        // 测试确保关键字不会被错误地加引号
        String originalSql = "GRANT CREATE, ALTER, DROP ON SCHEMA test_schema TO db_admin";
        Grant grant = new Grant(originalSql);

        // 测试达梦数据库转换
        String damengResult = damengGenerator.generate(grant);
        assertFalse(damengResult.contains("\"GRANT\""));
        assertFalse(damengResult.contains("\"CREATE\""));
        assertFalse(damengResult.contains("\"ALTER\""));
        assertFalse(damengResult.contains("\"DROP\""));
        assertFalse(damengResult.contains("\"ON\""));
        assertFalse(damengResult.contains("\"SCHEMA\""));
        assertFalse(damengResult.contains("\"TO\""));
        assertTrue(damengResult.contains("GRANT CREATE, ALTER, DROP"));
        assertTrue(damengResult.contains("ON SCHEMA test_schema"));
        assertTrue(damengResult.contains("TO db_admin"));

        // 测试金仓数据库转换
        String kingbaseResult = kingbaseGenerator.generate(grant);
        assertFalse(kingbaseResult.contains("\"GRANT\""));
        assertFalse(kingbaseResult.contains("\"CREATE\""));
        assertFalse(kingbaseResult.contains("\"ALTER\""));
        assertFalse(kingbaseResult.contains("\"DROP\""));
        assertFalse(kingbaseResult.contains("\"ON\""));
        assertFalse(kingbaseResult.contains("\"SCHEMA\""));
        assertFalse(kingbaseResult.contains("\"TO\""));
        assertTrue(kingbaseResult.contains("GRANT CREATE, ALTER, DROP"));
        assertTrue(kingbaseResult.contains("ON SCHEMA test_schema"));
        assertTrue(kingbaseResult.contains("TO db_admin"));

        // 测试神通数据库转换
        String shentongResult = shentongGenerator.generate(grant);
        assertFalse(shentongResult.contains("\"GRANT\""));
        assertFalse(shentongResult.contains("\"CREATE\""));
        assertFalse(shentongResult.contains("\"ALTER\""));
        assertFalse(shentongResult.contains("\"DROP\""));
        assertFalse(shentongResult.contains("\"ON\""));
        assertFalse(shentongResult.contains("\"SCHEMA\""));
        assertFalse(shentongResult.contains("\"TO\""));
        assertTrue(shentongResult.contains("GRANT CREATE, ALTER, DROP"));
        assertTrue(shentongResult.contains("ON SCHEMA test_schema"));
        assertTrue(shentongResult.contains("TO db_admin"));
    }
}
