package com.xylink.sqltranspiler.unit.dialects.shentong;

import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.create.CreateIndex;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 神通数据库索引功能测试
 * 基于神通数据库官方文档的测试驱动开发
 * 参考神通数据库官方文档：shentong.md
 * 
 * 根据神通数据库官方文档第2.8节对象的命名规则：
 * - 模式对象可以通过SQL语句访问，不同的模式下可以有同名的对象
 * - 访问时可以为它们加上模式前缀来区分，如sch1.tab和sch2.tab
 * - 表、视图、序列和索引在神通数据库中属于同一名称空间
 */
public class ShentongIndexTest {
    
    private static final Logger log = LoggerFactory.getLogger(ShentongIndexTest.class);
    
    private ShentongGenerator generator;
    
    @BeforeEach
    public void setUp() {
        generator = new ShentongGenerator();
    }
    
    private CreateIndex parseCreateIndex(String sql) {
        com.xylink.sqltranspiler.core.ast.Statement statement = MySqlHelper.parseStatement(sql);
        assertTrue(statement instanceof CreateIndex, "应该解析为CreateIndex类型");
        return (CreateIndex) statement;
    }
    
    @Test
    @DisplayName("测试基本CREATE INDEX语句")
    public void testBasicCreateIndex() {
        String sql = "CREATE INDEX idx_name ON users(name);";
        CreateIndex createIndex = parseCreateIndex(sql);
        String result = generator.generate(createIndex);
        
        log.info("MySQL SQL: {}", sql);
        log.info("神通 SQL: {}", result);
        
        assertTrue(result.contains("CREATE INDEX"));
        assertTrue(result.contains("\"idx_name\""));
        assertTrue(result.contains("\"users\""));
        assertTrue(result.contains("\"name\""));
        assertTrue(result.endsWith(";"));
    }
    
    @Test
    @DisplayName("测试CREATE UNIQUE INDEX语句")
    public void testCreateUniqueIndex() {
        String sql = "CREATE UNIQUE INDEX uk_email ON users(email);";
        CreateIndex createIndex = parseCreateIndex(sql);
        String result = generator.generate(createIndex);
        
        log.info("MySQL SQL: {}", sql);
        log.info("神通 SQL: {}", result);
        
        assertTrue(result.contains("CREATE UNIQUE INDEX"));
        assertTrue(result.contains("\"uk_email\""));
        assertTrue(result.contains("\"users\""));
        assertTrue(result.contains("\"email\""));
        assertTrue(result.endsWith(";"));
    }
    
    @Test
    @DisplayName("测试带schema的CREATE INDEX语句")
    public void testCreateIndexWithSchema() {
        String sql = "CREATE INDEX idx_order_status ON sales.orders(status);";
        CreateIndex createIndex = parseCreateIndex(sql);
        String result = generator.generate(createIndex);
        
        log.info("MySQL SQL: {}", sql);
        log.info("神通 SQL: {}", result);
        
        assertTrue(result.contains("CREATE INDEX"));
        assertTrue(result.contains("\"idx_order_status\""));
        // 关键验证：schema不应该丢失
        assertTrue(result.contains("\"sales\".\"orders\""));
        assertTrue(result.contains("\"status\""));
        assertTrue(result.endsWith(";"));
    }
    
    @Test
    @DisplayName("测试多列索引")
    public void testMultiColumnIndex() {
        String sql = "CREATE INDEX idx_name_age ON users(name, age);";
        CreateIndex createIndex = parseCreateIndex(sql);
        String result = generator.generate(createIndex);
        
        log.info("MySQL SQL: {}", sql);
        log.info("神通 SQL: {}", result);
        
        assertTrue(result.contains("CREATE INDEX"));
        assertTrue(result.contains("\"idx_name_age\""));
        assertTrue(result.contains("\"users\""));
        assertTrue(result.contains("\"name\""));
        assertTrue(result.contains("\"age\""));
        assertTrue(result.endsWith(";"));
    }
    
    @Test
    @DisplayName("测试复合索引与schema")
    public void testCompositeIndexWithSchema() {
        String sql = "CREATE INDEX idx_customer_order ON sales.orders(customer_id, order_date, status);";
        CreateIndex createIndex = parseCreateIndex(sql);
        String result = generator.generate(createIndex);
        
        log.info("MySQL SQL: {}", sql);
        log.info("神通 SQL: {}", result);
        
        assertTrue(result.contains("CREATE INDEX"));
        assertTrue(result.contains("\"idx_customer_order\""));
        // 验证schema保留
        assertTrue(result.contains("\"sales\".\"orders\""));
        assertTrue(result.contains("\"customer_id\""));
        assertTrue(result.contains("\"order_date\""));
        assertTrue(result.contains("\"status\""));
        assertTrue(result.endsWith(";"));
    }
    
    @Test
    @DisplayName("测试原始问题修复 - charge.libra_charge_conference_config")
    public void testOriginalProblemFixed() {
        // 这是导致问题的原始SQL语句
        String sql = "create index idx_charge_conf_config_configname on charge.libra_charge_conference_config(config_name);";
        CreateIndex createIndex = parseCreateIndex(sql);
        String result = generator.generate(createIndex);
        
        log.info("原始问题SQL: {}", sql);
        log.info("修复后神通SQL: {}", result);
        
        assertTrue(result.contains("CREATE INDEX"));
        assertTrue(result.contains("\"idx_charge_conf_config_configname\""));
        // 关键验证：schema不应该丢失
        assertTrue(result.contains("\"charge\".\"libra_charge_conference_config\""));
        assertTrue(result.contains("\"config_name\""));
        assertTrue(result.endsWith(";"));
        
        log.info("✅ 原始schema丢失问题已修复！");
    }
    
    @Test
    @DisplayName("测试复杂schema名称")
    public void testComplexSchemaNames() {
        String[] testCases = {
            "CREATE INDEX idx_test ON my_database.my_table(column_name);",
            "CREATE INDEX idx_underscore ON user_db.user_table(user_id);",
            "CREATE UNIQUE INDEX uk_complex ON order_management.order_details(order_number);",
            "CREATE INDEX idx_long_name ON very_long_schema_name.very_long_table_name(very_long_column_name);"
        };
        
        for (String sql : testCases) {
            log.info("测试复杂schema SQL: {}", sql);
            
            CreateIndex createIndex = parseCreateIndex(sql);
            String result = generator.generate(createIndex);
            
            log.info("转换结果: {}", result);
            
            // 验证基本结构
            assertTrue(result.toUpperCase().contains("CREATE"), "应该包含CREATE");
            assertTrue(result.toUpperCase().contains("INDEX"), "应该包含INDEX");
            assertTrue(result.endsWith(";"), "应该以分号结尾");
            
            // 验证schema保留（提取schema名称进行验证）
            String schemaName = extractSchemaFromSql(sql);
            if (schemaName != null) {
                assertTrue(result.contains("\"" + schemaName + "\"."), 
                          "应该包含schema: " + schemaName + " in " + result);
            }
        }
        
        log.info("✅ 复杂schema名称测试通过！");
    }
    
    /**
     * 从SQL中提取schema名称的辅助方法
     */
    private String extractSchemaFromSql(String sql) {
        String upperSql = sql.toUpperCase();
        int onIndex = upperSql.indexOf(" ON ");
        if (onIndex == -1) return null;
        
        String tablePart = sql.substring(onIndex + 4).trim();
        int dotIndex = tablePart.indexOf('.');
        if (dotIndex == -1) return null;
        
        String schemaName = tablePart.substring(0, dotIndex).trim();
        // 移除可能的引号
        schemaName = schemaName.replaceAll("[`\"']", "");
        
        return schemaName;
    }
    
    @Test
    @DisplayName("测试神通数据库官方文档示例")
    public void testShentongOfficialExample() {
        // 根据神通数据库官方文档的索引示例
        String sql = "CREATE INDEX idx_employee_dept ON hr.employees(department_id);";
        CreateIndex createIndex = parseCreateIndex(sql);
        String result = generator.generate(createIndex);
        
        log.info("神通官方示例SQL: {}", sql);
        log.info("生成结果: {}", result);
        
        assertTrue(result.contains("CREATE INDEX"));
        assertTrue(result.contains("\"idx_employee_dept\""));
        assertTrue(result.contains("\"hr\".\"employees\""));
        assertTrue(result.contains("\"department_id\""));
        assertTrue(result.endsWith(";"));
        
        log.info("✅ 神通数据库官方文档示例测试通过！");
    }
}
