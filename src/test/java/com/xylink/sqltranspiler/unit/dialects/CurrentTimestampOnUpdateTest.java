package com.xylink.sqltranspiler.unit.dialects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;

/**
 * 测试CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP语法在各数据库中的处理
 * 
 * 根据官方文档要求：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: 官方文档支持此语法
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DisplayName("CURRENT_TIMESTAMP ON UPDATE语法处理测试")
public class CurrentTimestampOnUpdateTest {

    private Transpiler transpiler;
    private ListAppender<ILoggingEvent> logAppender;
    private Logger damengLogger;
    private Logger kingbaseLogger;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
        
        // 设置日志捕获器
        logAppender = new ListAppender<>();
        logAppender.start();
        
        // 为达梦和金仓数据库的Generator添加日志捕获
        damengLogger = (Logger) LoggerFactory.getLogger(DamengGenerator.class);
        kingbaseLogger = (Logger) LoggerFactory.getLogger(KingbaseGenerator.class);
        
        damengLogger.addAppender(logAppender);
        kingbaseLogger.addAppender(logAppender);
    }

    @Test
    @DisplayName("达梦数据库：CURRENT_TIMESTAMP ON UPDATE转换为SYSDATE并记录警告")
    void testDamengCurrentTimestampOnUpdate() {
        String inputSql = "CREATE TABLE test_table (\n" +
                "    id INT AUTO_INCREMENT PRIMARY KEY,\n" +
                "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n" +
                ");";

        logAppender.list.clear();
        var transpilationResult = transpiler.transpile(inputSql, "mysql", "dameng");
        String result = transpilationResult.translatedSql();

        // 验证转换结果
        assertNotNull(result, "转换结果不应为null");
        assertTrue(result.contains("DEFAULT SYSDATE"),
                "达梦数据库应将CURRENT_TIMESTAMP转换为SYSDATE");
        assertFalse(result.contains("ON UPDATE"),
                "达梦数据库不支持ON UPDATE语法，应被移除");
        assertTrue(result.contains("IDENTITY(1,1)"),
                "达梦数据库应将AUTO_INCREMENT转换为IDENTITY(1,1)");

        // 验证警告日志
        boolean foundWarning = logAppender.list.stream()
                .anyMatch(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("UNSUPPORTED_FEATURE") &&
                        event.getFormattedMessage().contains("ON UPDATE clause") &&
                        event.getFormattedMessage().contains("DM database"));
        
        assertTrue(foundWarning, "达梦数据库应记录ON UPDATE不支持的警告日志");
    }

    @Test
    @DisplayName("金仓数据库：移除ON UPDATE部分并记录警告")
    void testKingbaseCurrentTimestampOnUpdate() {
        String inputSql = "CREATE TABLE test_table (\n" +
                "    id INT AUTO_INCREMENT PRIMARY KEY,\n" +
                "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n" +
                ");";

        logAppender.list.clear();
        var transpilationResult = transpiler.transpile(inputSql, "mysql", "kingbase");
        String result = transpilationResult.translatedSql();

        // 验证转换结果
        assertNotNull(result, "转换结果不应为null");
        assertTrue(result.contains("DEFAULT CURRENT_TIMESTAMP"),
                "金仓数据库应保留CURRENT_TIMESTAMP默认值");
        assertFalse(result.contains("ON UPDATE"),
                "金仓数据库不支持ON UPDATE语法，应被移除");
        assertTrue(result.contains("SERIAL"),
                "金仓数据库应将AUTO_INCREMENT转换为SERIAL");

        // 验证警告日志
        boolean foundWarning = logAppender.list.stream()
                .anyMatch(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("UNSUPPORTED_FEATURE") &&
                        event.getFormattedMessage().contains("ON UPDATE clause") &&
                        event.getFormattedMessage().contains("KingbaseES"));
        
        assertTrue(foundWarning, "金仓数据库应记录ON UPDATE不支持的警告日志");
    }

    @Test
    @DisplayName("神通数据库：完全保留CURRENT_TIMESTAMP ON UPDATE语法")
    void testShentongCurrentTimestampOnUpdate() {
        String inputSql = "CREATE TABLE test_table (\n" +
                "    id INT AUTO_INCREMENT PRIMARY KEY,\n" +
                "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n" +
                ");";

        logAppender.list.clear();
        var transpilationResult = transpiler.transpile(inputSql, "mysql", "shentong");
        String result = transpilationResult.translatedSql();

        // 验证转换结果
        assertNotNull(result, "转换结果不应为null");
        assertTrue(result.contains("DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
                "神通数据库应完全保留CURRENT_TIMESTAMP ON UPDATE语法");
        assertTrue(result.contains("AUTO_INCREMENT"),
                "神通数据库应保留AUTO_INCREMENT语法");
        assertTrue(result.contains("CHARACTER SET UTF8"),
                "神通数据库应添加字符集设置");

        // 验证没有警告日志（神通数据库支持此语法）
        boolean foundWarning = logAppender.list.stream()
                .anyMatch(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("ON UPDATE"));
        
        assertFalse(foundWarning, "神通数据库支持ON UPDATE语法，不应记录警告日志");
    }

    @Test
    @DisplayName("测试只有CURRENT_TIMESTAMP默认值（无ON UPDATE）")
    void testCurrentTimestampWithoutOnUpdate() {
        String inputSql = "CREATE TABLE test_table (\n" +
                "    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n" +
                ");";

        // 达梦数据库测试
        logAppender.list.clear();
        String damengResult = transpiler.transpile(inputSql, "mysql", "dameng").translatedSql();
        assertTrue(damengResult.contains("DEFAULT SYSDATE"),
                "达梦数据库应将CURRENT_TIMESTAMP转换为SYSDATE");

        // 金仓数据库测试
        String kingbaseResult = transpiler.transpile(inputSql, "mysql", "kingbase").translatedSql();
        assertTrue(kingbaseResult.contains("DEFAULT CURRENT_TIMESTAMP"),
                "金仓数据库应保留CURRENT_TIMESTAMP");

        // 神通数据库测试
        String shentongResult = transpiler.transpile(inputSql, "mysql", "shentong").translatedSql();
        assertTrue(shentongResult.contains("DEFAULT CURRENT_TIMESTAMP"),
                "神通数据库应保留CURRENT_TIMESTAMP");

        // 验证没有ON UPDATE相关的警告日志
        boolean foundOnUpdateWarning = logAppender.list.stream()
                .anyMatch(event -> event.getFormattedMessage().contains("ON UPDATE"));
        
        assertFalse(foundOnUpdateWarning, "没有ON UPDATE语法时不应记录相关警告");
    }

    @Test
    @DisplayName("测试复杂的ON UPDATE表达式")
    void testComplexOnUpdateExpressions() {
        String inputSql = "CREATE TABLE test_table (\n" +
                "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n" +
                "    modified_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n" +
                ");";

        // 达梦数据库测试
        logAppender.list.clear();
        String damengResult = transpiler.transpile(inputSql, "mysql", "dameng").translatedSql();
        
        // 验证两个字段都被正确转换
        long sysDateCount = damengResult.chars().mapToObj(c -> (char) c)
                .map(String::valueOf)
                .reduce("", String::concat)
                .split("SYSDATE", -1).length - 1;
        assertEquals(2, sysDateCount, "达梦数据库应转换两个CURRENT_TIMESTAMP为SYSDATE");
        
        // 验证记录了两次ON UPDATE警告
        long onUpdateWarnings = logAppender.list.stream()
                .filter(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("ON UPDATE clause"))
                .count();
        assertEquals(2, onUpdateWarnings, "应为两个ON UPDATE字段记录警告");
    }
}
