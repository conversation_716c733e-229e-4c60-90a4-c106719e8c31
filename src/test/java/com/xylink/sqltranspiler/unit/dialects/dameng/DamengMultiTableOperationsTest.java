package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 达梦数据库多表操作测试
 * 
 * 根据达梦官方文档，测试MySQL多表操作到达梦的转换
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * 
 * 测试覆盖：
 * - 多表关联的外键约束
 * - 复杂的表间关系定义
 * - 级联操作的转换
 * - 多表索引策略
 */
@DisplayName("达梦数据库多表操作测试")
public class DamengMultiTableOperationsTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本外键关系")
    void testBasicForeignKeyRelationships() {
        String mysqlSql = "CREATE TABLE orders (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "user_id INT NOT NULL, " +
                "product_id INT NOT NULL, " +
                "quantity INT DEFAULT 1, " +
                "order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (user_id) REFERENCES users(id), " +
                "FOREIGN KEY (product_id) REFERENCES products(id)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("基本外键关系转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证外键关系转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("orders"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("user_id"), "应保留user_id列");
        assertTrue(damengSql.contains("product_id"), "应保留product_id列");
        assertTrue(damengSql.contains("quantity"), "应保留quantity列");
        assertTrue(damengSql.contains("order_date"), "应保留order_date列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
        
        // 验证外键处理（达梦可能在单独的语句中处理外键）
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("user_id"), "应处理外键约束");
    }

    @Test
    @DisplayName("测试级联操作")
    void testCascadeOperations() {
        String mysqlSql = "CREATE TABLE order_items (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "order_id INT NOT NULL, " +
                "product_id INT NOT NULL, " +
                "quantity INT NOT NULL DEFAULT 1, " +
                "price DECIMAL(10,2) NOT NULL, " +
                "FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE RESTRICT" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("级联操作转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证级联操作转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("order_items"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("order_id"), "应保留order_id列");
        assertTrue(damengSql.contains("product_id"), "应保留product_id列");
        assertTrue(damengSql.contains("quantity"), "应保留quantity列");
        assertTrue(damengSql.contains("price"), "应保留price列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        
        // 验证级联操作处理（达梦可能在单独的语句中处理级联）
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("CASCADE") || damengSql.contains("order_id"), 
                  "应处理级联操作");
    }

    @Test
    @DisplayName("测试复合外键")
    void testCompositeKeys() {
        String mysqlSql = "CREATE TABLE user_roles (" +
                "user_id INT NOT NULL, " +
                "role_id INT NOT NULL, " +
                "assigned_date DATE NOT NULL, " +
                "assigned_by INT, " +
                "is_active BOOLEAN DEFAULT TRUE, " +
                "PRIMARY KEY (user_id, role_id), " +
                "FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("复合外键转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证复合外键转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("user_roles"), "应保留表名");
        assertTrue(damengSql.contains("user_id"), "应保留user_id列");
        assertTrue(damengSql.contains("role_id"), "应保留role_id列");
        assertTrue(damengSql.contains("assigned_date"), "应保留assigned_date列");
        assertTrue(damengSql.contains("assigned_by"), "应保留assigned_by列");
        assertTrue(damengSql.contains("is_active"), "应保留is_active列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留复合主键");
        assertTrue(damengSql.contains("DATE"), "应保留DATE类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证复合外键处理
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("user_id"), "应处理复合外键约束");
    }

    @Test
    @DisplayName("测试自引用外键")
    void testSelfReferencingForeignKeys() {
        String mysqlSql = "CREATE TABLE categories (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "parent_id INT, " +
                "level INT DEFAULT 0, " +
                "sort_order INT DEFAULT 0, " +
                "is_active BOOLEAN DEFAULT TRUE, " +
                "FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE CASCADE, " +
                "INDEX idx_parent (parent_id), " +
                "INDEX idx_level (level)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("自引用外键转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证自引用外键转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("categories"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("name"), "应保留name列");
        assertTrue(damengSql.contains("parent_id"), "应保留parent_id列");
        assertTrue(damengSql.contains("level"), "应保留level列");
        assertTrue(damengSql.contains("sort_order"), "应保留sort_order列");
        assertTrue(damengSql.contains("is_active"), "应保留is_active列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证自引用外键处理
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("parent_id"), "应处理自引用外键");
        
        // 验证索引处理
        assertTrue(damengSql.contains("idx_parent") || damengSql.contains("parent_id"), 
                  "应处理parent索引");
        assertTrue(damengSql.contains("idx_level") || damengSql.contains("level"), 
                  "应处理level索引");
    }

    @Test
    @DisplayName("测试多对多关系表")
    void testManyToManyRelationshipTables() {
        String mysqlSql = "CREATE TABLE product_categories (" +
                "product_id INT NOT NULL, " +
                "category_id INT NOT NULL, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "created_by INT, " +
                "PRIMARY KEY (product_id, category_id), " +
                "FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("多对多关系表转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证多对多关系表转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("product_categories"), "应保留表名");
        assertTrue(damengSql.contains("product_id"), "应保留product_id列");
        assertTrue(damengSql.contains("category_id"), "应保留category_id列");
        assertTrue(damengSql.contains("created_at"), "应保留created_at列");
        assertTrue(damengSql.contains("created_by"), "应保留created_by列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留复合主键");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
        
        // 验证多对多外键处理
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("product_id"), "应处理多对多外键约束");
    }

    @Test
    @DisplayName("测试复杂表间关系")
    void testComplexTableRelationships() {
        String mysqlSql = "CREATE TABLE order_audit (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "order_id INT NOT NULL, " +
                "user_id INT NOT NULL, " +
                "action VARCHAR(50) NOT NULL, " +
                "old_status VARCHAR(20), " +
                "new_status VARCHAR(20), " +
                "reason TEXT, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "created_by INT NOT NULL, " +
                "FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE RESTRICT, " +
                "FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE RESTRICT, " +
                "INDEX idx_order_audit_order (order_id), " +
                "INDEX idx_order_audit_user (user_id), " +
                "INDEX idx_order_audit_created (created_at)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("复杂表间关系转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证复杂表间关系转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("order_audit"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("order_id"), "应保留order_id列");
        assertTrue(damengSql.contains("user_id"), "应保留user_id列");
        assertTrue(damengSql.contains("action"), "应保留action列");
        assertTrue(damengSql.contains("old_status"), "应保留old_status列");
        assertTrue(damengSql.contains("new_status"), "应保留new_status列");
        assertTrue(damengSql.contains("reason"), "应保留reason列");
        assertTrue(damengSql.contains("created_at"), "应保留created_at列");
        assertTrue(damengSql.contains("created_by"), "应保留created_by列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("VARCHAR(50)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
        
        // 验证复杂外键处理
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("order_id"), "应处理复杂外键约束");
        
        // 验证索引处理
        assertTrue(damengSql.contains("idx_order_audit") || damengSql.contains("order_id"), 
                  "应处理审计索引");
    }

    @Test
    @DisplayName("测试跨表约束")
    void testCrossTableConstraints() {
        String mysqlSql = "CREATE TABLE inventory (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "product_id INT NOT NULL, " +
                "warehouse_id INT NOT NULL, " +
                "quantity INT NOT NULL DEFAULT 0, " +
                "reserved_quantity INT DEFAULT 0, " +
                "last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "updated_by INT, " +
                "FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE, " +
                "FOREIGN KEY (warehouse_id) REFERENCES warehouses(id) ON DELETE RESTRICT, " +
                "FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL, " +
                "UNIQUE KEY uk_product_warehouse (product_id, warehouse_id), " +
                "CHECK (quantity >= 0), " +
                "CHECK (reserved_quantity >= 0), " +
                "CHECK (reserved_quantity <= quantity)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("跨表约束转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证跨表约束转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("inventory"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("product_id"), "应保留product_id列");
        assertTrue(damengSql.contains("warehouse_id"), "应保留warehouse_id列");
        assertTrue(damengSql.contains("quantity"), "应保留quantity列");
        assertTrue(damengSql.contains("reserved_quantity"), "应保留reserved_quantity列");
        assertTrue(damengSql.contains("last_updated"), "应保留last_updated列");
        assertTrue(damengSql.contains("updated_by"), "应保留updated_by列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
        
        // 验证跨表约束处理
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("product_id"), "应处理跨表外键约束");
        assertTrue(damengSql.contains("UNIQUE") || damengSql.contains("uk_product_warehouse"), 
                  "应处理唯一约束");
        assertTrue(damengSql.contains("CHECK") || damengSql.contains("quantity"), 
                  "应处理CHECK约束");
    }

    @Test
    @DisplayName("测试达梦特有的多表特性")
    void testDamengSpecificMultiTableFeatures() {
        String mysqlSql = "CREATE TABLE dameng_multi_table_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "oracle_number NUMBER(15,2), " +
                "oracle_varchar2 VARCHAR2(200), " +
                "mysql_text TEXT, " +
                "mysql_blob BLOB, " +
                "parent_id INT, " +
                "category_id INT, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "FOREIGN KEY (parent_id) REFERENCES dameng_multi_table_features(id), " +
                "FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦特有多表特性:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证达梦特有多表特性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("dameng_multi_table_features"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        
        // 验证Oracle兼容类型在多表中的支持
        assertTrue(damengSql.contains("NUMBER(15,2)"), "应支持Oracle NUMBER类型");
        assertTrue(damengSql.contains("VARCHAR2(200)"), "应支持Oracle VARCHAR2类型");
        
        // 验证MySQL类型转换在多表中的处理
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        assertTrue(damengSql.contains("BLOB"), "应保留BLOB类型");
        
        // 验证多表外键处理
        assertTrue(damengSql.contains("parent_id"), "应保留parent_id列");
        assertTrue(damengSql.contains("category_id"), "应保留category_id列");
        assertTrue(damengSql.contains("created_at"), "应保留created_at列");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
        
        // 验证外键约束处理
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("parent_id"), "应处理多表外键约束");
    }
}
