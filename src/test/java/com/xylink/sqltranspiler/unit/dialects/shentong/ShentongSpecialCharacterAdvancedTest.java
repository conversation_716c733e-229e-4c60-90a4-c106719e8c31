package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库特殊字符高级处理测试
 * 基于神通数据库官方文档 shentong.md 第2.4节特殊字符规范
 * 
 * 根据官方文档：
 * - 第144行：分号（;）结束一条SQL命令
 * - 第132-134行：美元符号（$）后面跟着数字用于在一个函数体中表示参数的位置
 * - 第140行：方括弧（[]）用于选取数组元素
 * 
 * 测试覆盖：
 * 1. 分号结束SQL命令的严格测试
 * 2. 美元符号参数位置测试
 * 3. 方括弧数组元素选取测试
 * 4. 其他特殊字符处理
 */
public class ShentongSpecialCharacterAdvancedTest extends BaseShentongConversionTest {

    /**
     * 测试分号结束SQL命令
     * 根据官方文档第144行：分号（;）结束一条SQL命令
     */
    @Test
    @DisplayName("验证分号结束SQL命令")
    public void testSemicolonSqlTermination() throws Exception {
        String mysqlSql = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                name VARCHAR(50)
            );
            
            INSERT INTO test_table VALUES (1, 'test');
            
            UPDATE test_table SET name = 'updated' WHERE id = 1;
            
            SELECT * FROM test_table;
            
            DELETE FROM test_table WHERE id = 1;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证每个SQL语句都以分号结尾
        // 使用分号分割来获取完整的SQL语句，而不是按行分割
        String[] statements = shentongSql.split(";");
        boolean foundCreate = false, foundInsert = false, foundUpdate = false,
                foundSelect = false, foundDelete = false;

        for (String statement : statements) {
            String trimmed = statement.trim();
            if (trimmed.isEmpty()) continue;

            // 添加分号以检查完整语句
            String fullStatement = trimmed + ";";

            if (trimmed.contains("CREATE TABLE")) {
                foundCreate = true;
                assertTrue(fullStatement.endsWith(";"), "CREATE TABLE语句应以分号结尾: " + fullStatement);
            } else if (trimmed.contains("INSERT INTO")) {
                foundInsert = true;
                assertTrue(fullStatement.endsWith(";"), "INSERT语句应以分号结尾: " + fullStatement);
            } else if (trimmed.contains("UPDATE")) {
                foundUpdate = true;
                assertTrue(fullStatement.endsWith(";"), "UPDATE语句应以分号结尾: " + fullStatement);
            } else if (trimmed.contains("SELECT")) {
                foundSelect = true;
                assertTrue(fullStatement.endsWith(";"), "SELECT语句应以分号结尾: " + fullStatement);
            } else if (trimmed.contains("DELETE")) {
                foundDelete = true;
                assertTrue(fullStatement.endsWith(";"), "DELETE语句应以分号结尾: " + fullStatement);
            }
        }
        
        // 验证所有语句类型都被处理
        assertTrue(foundCreate, "应包含CREATE语句");
        assertTrue(foundInsert, "应包含INSERT语句");
        assertTrue(foundUpdate, "应包含UPDATE语句");
        assertTrue(foundSelect, "应包含SELECT语句");
        assertTrue(foundDelete, "应包含DELETE语句");
    }

    /**
     * 测试非MySQL函数语法被MySQL强制校验正确拒绝
     * 验证非标准语法被正确检测和拒绝
     */
    @Test
    @DisplayName("非MySQL函数语法拒绝测试")
    public void testNonMySqlFunctionSyntaxRejection() throws Exception {
        String nonMysqlSql = """
            CREATE FUNCTION calculate_total(price DECIMAL(10,2), tax_rate DECIMAL(5,4))
            RETURNS DECIMAL(10,2)
            READS SQL DATA
            DETERMINISTIC
            BEGIN
                DECLARE total DECIMAL(10,2);
                SET total = price * (1 + tax_rate);
                RETURN total;
            END;

            -- 在神通中，函数参数可以用$1, $2等表示
            CREATE OR REPLACE FUNCTION shentong_calculate_total(DECIMAL, DECIMAL)
            RETURNS DECIMAL AS $$
            BEGIN
                RETURN $1 * (1 + $2);
            END;
            $$ LANGUAGE plpgsql;
            """;

        String result = convertMySqlToShentong(nonMysqlSql);

        // 验证非MySQL语法被正确拒绝 - 根据MySQL 8.4官方文档，CREATE OR REPLACE FUNCTION不是MySQL语法
        assertTrue(result.isEmpty(), "非MySQL函数语法应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准函数语法的转换
     * 使用正确的MySQL语法进行函数测试
     */
    @Test
    @DisplayName("MySQL标准函数语法转换测试")
    public void testMySqlStandardFunctionSyntax() throws Exception {
        String mysqlSql = """
            CREATE FUNCTION calculate_total(price DECIMAL(10,2), tax_rate DECIMAL(5,4))
            RETURNS DECIMAL(10,2)
            READS SQL DATA
            DETERMINISTIC
            BEGIN
                DECLARE total DECIMAL(10,2);
                SET total = price * (1 + tax_rate);
                RETURN total;
            END;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准函数语法转换
        assertTrue(shentongSql.contains("CREATE FUNCTION"), "应支持CREATE FUNCTION");
        assertTrue(shentongSql.contains("calculate_total"), "应保持函数名称");
        assertTrue(shentongSql.contains("DECIMAL"), "应支持DECIMAL类型");
        assertTrue(shentongSql.contains("RETURNS"), "应支持RETURNS");
        // DETERMINISTIC可能在转换过程中被处理，这是正常的
        assertTrue(shentongSql.contains("BEGIN"), "应支持BEGIN");
        assertTrue(shentongSql.contains("END"), "应支持END");
        
        // 验证函数创建
        assertTrue(shentongSql.contains("CREATE") && 
                   (shentongSql.contains("FUNCTION") || shentongSql.contains("PROCEDURE")), 
                   "应包含函数或存储过程创建");
        
        // 验证美元符号参数处理
        if (shentongSql.contains("$1") || shentongSql.contains("$2")) {
            assertTrue(shentongSql.contains("$1"), "应支持$1参数引用");
            assertTrue(shentongSql.contains("$2"), "应支持$2参数引用");
            
            // 验证美元符号参数在正确的上下文中使用
            assertTrue(shentongSql.contains("RETURN $1") || 
                       shentongSql.contains("$1 *"), 
                       "$1应在表达式中正确使用");
        }
        
        // 验证函数体结构
        assertTrue(shentongSql.contains("DECIMAL"), "应包含DECIMAL类型");
        assertTrue(shentongSql.contains("RETURN"), "应包含RETURN语句");
    }

    /**
     * 测试PostgreSQL数组语法被MySQL强制校验正确拒绝
     * 验证PostgreSQL特有数组语法被正确检测和拒绝
     */
    @Test
    @DisplayName("PostgreSQL数组语法拒绝测试")
    public void testPostgreSqlArraySyntaxRejection() throws Exception {
        String postgresqlSql = """
            CREATE TABLE array_test (
                id INT PRIMARY KEY,
                tags JSON,
                numbers TEXT
            );

            INSERT INTO array_test VALUES
            (1, '["tag1", "tag2", "tag3"]', '1,2,3,4,5');

            -- PostgreSQL数组语法
            CREATE TABLE shentong_array_test (
                id INT PRIMARY KEY,
                int_array INT[],
                text_array TEXT[]
            );

            INSERT INTO shentong_array_test VALUES
            (1, ARRAY[1,2,3,4,5], ARRAY['a','b','c']);

            -- 数组元素访问示例
            SELECT id, int_array[1], text_array[2]
            FROM shentong_array_test
            WHERE int_array[3] > 2;
            """;

        String result = convertMySqlToShentong(postgresqlSql);

        // 验证PostgreSQL语法被正确拒绝 - 根据MySQL 8.4官方文档，数组类型不是MySQL语法
        assertTrue(result.isEmpty(), "PostgreSQL数组语法应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准JSON数据类型的转换
     * 使用正确的MySQL语法进行JSON数据处理测试
     */
    @Test
    @DisplayName("MySQL标准JSON数据类型转换测试")
    public void testMySqlStandardJsonDataType() throws Exception {
        String mysqlSql = """
            CREATE TABLE array_test (
                id INT PRIMARY KEY,
                tags JSON,
                numbers TEXT
            );

            INSERT INTO array_test VALUES
            (1, '["tag1", "tag2", "tag3"]', '1,2,3,4,5');

            -- MySQL JSON函数
            SELECT
                id,
                JSON_EXTRACT(tags, '$[0]') as first_tag,
                JSON_LENGTH(tags) as tag_count
            FROM array_test;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证MySQL标准JSON数据类型转换
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE");
        assertTrue(shentongSql.contains("JSON"), "应支持JSON数据类型");
        assertTrue(shentongSql.contains("INSERT"), "应支持INSERT语句");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("JSON_EXTRACT") || shentongSql.contains("JSON"), "应支持JSON函数或保持JSON类型");
        assertTrue(shentongSql.contains("array_test"), "应保持表名");
        
        // 验证表创建
        assertTrue(shentongSql.contains("CREATE TABLE"), "应包含CREATE TABLE语句");
        
        // 验证数组类型处理
        if (shentongSql.contains("[]") || shentongSql.contains("ARRAY")) {
            // 如果支持数组类型
            assertTrue(shentongSql.contains("INT[]") || 
                       shentongSql.contains("ARRAY["), 
                       "应支持数组类型或ARRAY构造函数");
            
            // 验证数组元素访问
            if (shentongSql.contains("[1]") || shentongSql.contains("[2]")) {
                assertTrue(shentongSql.contains("int_array[1]") || 
                           shentongSql.contains("text_array[2]"), 
                           "应支持数组元素访问语法");
            }
        } else {
            // 如果不支持数组类型，应该有适当的转换
            assertTrue(shentongSql.contains("JSON") || 
                       shentongSql.contains("TEXT") || 
                       shentongSql.contains("VARCHAR"), 
                       "数组类型应被转换为支持的类型");
        }
        
        // 验证数据插入
        assertTrue(shentongSql.contains("INSERT INTO"), "应包含INSERT INTO语句");
    }

    /**
     * 测试PostgreSQL正则表达式操作符被MySQL强制校验正确拒绝
     * 验证PostgreSQL特有操作符被正确检测和拒绝
     */
    @Test
    @DisplayName("PostgreSQL正则表达式操作符拒绝测试")
    public void testPostgreSqlRegexOperatorSyntaxRejection() throws Exception {
        String postgresqlSql = """
            CREATE TABLE test_table (
                id INT PRIMARY KEY,
                name VARCHAR(100)
            );

            -- PostgreSQL正则表达式操作符
            SELECT * FROM test_table WHERE name ~ '^[A-Z]';
            SELECT * FROM test_table WHERE name !~ '^[0-9]';
            """;

        String result = convertMySqlToShentong(postgresqlSql);

        // 验证PostgreSQL语法被正确拒绝 - 根据MySQL 8.4官方文档，~ 和 !~ 不是MySQL操作符
        assertTrue(result.isEmpty(), "PostgreSQL正则表达式操作符应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试MySQL标准特殊字符处理
     * 使用正确的MySQL语法进行特殊字符测试
     */
    @Test
    @DisplayName("MySQL标准特殊字符处理测试")
    public void testMySqlStandardSpecialCharacterHandling() throws Exception {
        String mysqlSql = """
            CREATE TABLE special_chars (
                id INT PRIMARY KEY,
                at_symbol VARCHAR(100),      -- @ 符号
                hash_symbol VARCHAR(100),    -- # 符号
                percent_symbol VARCHAR(100), -- % 符号
                ampersand VARCHAR(100),      -- & 符号
                pipe_symbol VARCHAR(100),    -- | 符号
                caret_symbol VARCHAR(100),   -- ^ 符号
                path_symbol VARCHAR(100)     -- 路径符号
            );

            INSERT INTO special_chars VALUES (
                1,
                '<EMAIL>',          -- @ 在邮箱中
                '#hashtag #tag2',            -- # 在标签中
                'discount: 50% off',         -- % 在文本中
                'Tom & Jerry',               -- & 在文本中
                'option1 | option2',         -- | 作为分隔符
                'x^2 + y^2 = z^2',          -- ^ 在数学表达式中
                '/home/<USER>/documents'       -- 路径
            );

            -- 特殊字符在WHERE条件中的使用
            SELECT * FROM special_chars
            WHERE at_symbol LIKE '%@%'
            AND percent_symbol LIKE '%\\%%' ESCAPE '\\\\';
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);

        // 验证表结构
        assertTrue(shentongSql.contains("CREATE TABLE"), "应包含CREATE TABLE语句");
        assertTrue(shentongSql.contains("special_chars"), "应包含表名");

        // 验证特殊字符在数据中被正确保留
        assertTrue(shentongSql.contains("<EMAIL>"), "应保留@符号");
        assertTrue(shentongSql.contains("#hashtag"), "应保留#符号");
        assertTrue(shentongSql.contains("50% off"), "应保留%符号");
        assertTrue(shentongSql.contains("Tom & Jerry"), "应保留&符号");
        assertTrue(shentongSql.contains("option1 | option2"), "应保留|符号");
        assertTrue(shentongSql.contains("x^2 + y^2"), "应保留^符号");
        assertTrue(shentongSql.contains("/home/<USER>"), "应保留路径");

        // 验证LIKE查询中的特殊字符处理
        assertTrue(shentongSql.contains("LIKE '%@%'"), "应保留LIKE模式中的@符号");
        assertTrue(shentongSql.contains("ESCAPE"), "应保留ESCAPE子句");
        
        // 验证表结构
        assertTrue(shentongSql.contains("CREATE TABLE"), "应包含CREATE TABLE语句");
        assertTrue(shentongSql.contains("special_chars"), "应包含表名");
        
        // 验证特殊字符在数据中被正确保留
        assertTrue(shentongSql.contains("<EMAIL>"), "应保留@符号");
        assertTrue(shentongSql.contains("#hashtag"), "应保留#符号");
        assertTrue(shentongSql.contains("50% off"), "应保留%符号");
        assertTrue(shentongSql.contains("Tom & Jerry"), "应保留&符号");
        assertTrue(shentongSql.contains("option1 | option2"), "应保留|符号");
        assertTrue(shentongSql.contains("x^2 + y^2"), "应保留^符号");
        assertTrue(shentongSql.contains("/home/<USER>"), "应保留路径");
        
        // 验证LIKE查询中的特殊字符处理
        assertTrue(shentongSql.contains("LIKE '%@%'"), "应保留LIKE模式中的@符号");
        assertTrue(shentongSql.contains("LIKE '%\\%%'") ||
                   shentongSql.contains("LIKE '%\\%'"),
                   "应正确处理转义的%符号");
        
        // 验证ESCAPE子句
        assertTrue(shentongSql.contains("ESCAPE"), "应保留ESCAPE子句");
    }

    /**
     * 测试特殊字符转义
     * 验证需要转义的特殊字符的正确处理
     */
    @Test
    @DisplayName("验证特殊字符转义")
    public void testSpecialCharacterEscaping() throws Exception {
        String mysqlSql = """
            CREATE TABLE escape_test (
                id INT PRIMARY KEY,
                single_quotes VARCHAR(200),
                double_quotes VARCHAR(200),
                backslashes VARCHAR(200),
                mixed_quotes VARCHAR(200)
            );
            
            INSERT INTO escape_test VALUES (
                1,
                'It''s a beautiful day',
                'He said "Hello World"',
                'Path: C:\\Users\\<USER>\\file.txt',
                'Mixed: ''single'' and "double" quotes'
            );
            
            -- 测试转义字符在查询中的使用
            SELECT * FROM escape_test 
            WHERE single_quotes LIKE '%''%' 
            OR double_quotes LIKE '%"%'
            OR backslashes LIKE '%\\\\%';
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证转义字符被正确处理
        assertTrue(shentongSql.contains("It''s a beautiful day"), 
                   "单引号转义应被正确处理");
        assertTrue(shentongSql.contains("He said \"Hello World\""), 
                   "双引号应被正确处理");
        assertTrue(shentongSql.contains("C:\\\\Users") || 
                   shentongSql.contains("C:\\Users"), 
                   "反斜杠转义应被正确处理");
        assertTrue(shentongSql.contains("''single'' and \"double\""), 
                   "混合引号应被正确处理");
        
        // 验证查询条件中的转义
        assertTrue(shentongSql.contains("LIKE '%''%'"), 
                   "查询条件中的单引号转义应被保留");
        assertTrue(shentongSql.contains("LIKE '%\"%'"), 
                   "查询条件中的双引号应被保留");
    }

    /**
     * 测试Unicode特殊字符
     * 验证Unicode范围内的特殊字符处理
     */
    @Test
    @DisplayName("验证Unicode特殊字符")
    public void testUnicodeSpecialCharacters() throws Exception {
        String mysqlSql = """
            CREATE TABLE unicode_special (
                id INT PRIMARY KEY,
                symbols VARCHAR(200),
                math_symbols VARCHAR(200),
                currency VARCHAR(200),
                arrows VARCHAR(200)
            );
            
            INSERT INTO unicode_special VALUES (
                1,
                '★☆♠♣♥♦♪♫☀☁☂☃',
                '∑∏∫∂∇∆∞≠≤≥±×÷√',
                '€£¥₹₽₩₪₫₨₦₡₢',
                '←→↑↓↔↕⇐⇒⇑⇓⇔⇕'
            );
            
            SELECT * FROM unicode_special 
            WHERE symbols LIKE '%★%' 
            OR math_symbols LIKE '%∞%';
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证Unicode特殊字符被正确保留
        assertTrue(shentongSql.contains("★☆♠♣"), "应保留符号字符");
        assertTrue(shentongSql.contains("∑∏∫∂"), "应保留数学符号");
        assertTrue(shentongSql.contains("€£¥₹"), "应保留货币符号");
        assertTrue(shentongSql.contains("←→↑↓"), "应保留箭头符号");
        
        // 验证Unicode字符在查询条件中的使用
        assertTrue(shentongSql.contains("LIKE '%★%'"), 
                   "查询条件中应保留Unicode字符");
        assertTrue(shentongSql.contains("LIKE '%∞%'"), 
                   "查询条件中应保留数学符号");
        
        // 验证字符集支持
        assertTrue(shentongSql.contains("CHARACTER SET UTF8") || 
                   !shentongSql.contains("CHARACTER SET"), 
                   "应使用支持Unicode的字符集");
    }
}
