package com.xylink.sqltranspiler.unit.dialects.kingbase;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 金仓数据库标识符引用统一性测试
 * 
 * 测试目标：
 * 1. 验证预处理阶段的引号处理不会对金仓转换造成负面影响
 * 2. 验证DDL和DML语句中标识符引用的一致性
 * 3. 验证统一的标识符引用策略的可行性
 * 
 * 遵循官方文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/identifiers.html
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 */
public class KingbaseIdentifierQuotingTest {

    private KingbaseGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new KingbaseGenerator();
    }

    @Test
    @DisplayName("测试DDL语句中的标识符引用")
    public void testDDLIdentifierQuoting() {
        // 测试CREATE TABLE语句中的标识符引用
        String sql = "CREATE TABLE test_table (id INT, name VARCHAR(50), order_date DATE)";
        CreateTable createTable = (CreateTable) MySqlHelper.parseStatement(sql);

        String result = generator.generate(createTable);

        // 验证表名和列名都被正确引用
        assertTrue(result.contains("\"test_table\""), "表名应该被双引号包围");
        assertTrue(result.contains("\"id\""), "列名id应该被双引号包围");
        assertTrue(result.contains("\"name\""), "列名name应该被双引号包围");
        assertTrue(result.contains("\"order_date\""), "列名order_date应该被双引号包围");

        System.out.println("DDL Result: " + result);
    }

    @Test
    @DisplayName("测试DML语句中的标识符引用")
    public void testDMLIdentifierQuoting() {
        // 测试SELECT语句中的标识符引用
        String sql = "SELECT id, name FROM test_table WHERE order_date > '2023-01-01'";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        
        String result = generator.generate(queryStmt);
        
        // 当前行为：只有反引号标识符被转换为双引号
        // 普通标识符保持原样（符合PostgreSQL兼容性）
        assertTrue(result.contains("test_table"), "表名应该保持原样或被引用");
        assertTrue(result.contains("id"), "列名id应该保持原样或被引用");
        assertTrue(result.contains("name"), "列名name应该保持原样或被引用");
        assertTrue(result.contains("order_date"), "列名order_date应该保持原样或被引用");
        
        System.out.println("DML Result: " + result);
    }

    @Test
    @DisplayName("测试反引号标识符的转换")
    public void testBacktickIdentifierConversion() {
        // 测试带反引号的标识符转换
        String sql = "SELECT `id`, `name` FROM `test_table` WHERE `order_date` > '2023-01-01'";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        
        String result = generator.generate(queryStmt);
        
        // 反引号标识符应该被转换为双引号
        assertTrue(result.contains("\"test_table\""), "反引号表名应该被转换为双引号");
        assertTrue(result.contains("\"id\""), "反引号列名id应该被转换为双引号");
        assertTrue(result.contains("\"name\""), "反引号列名name应该被转换为双引号");
        assertTrue(result.contains("\"order_date\""), "反引号列名order_date应该被转换为双引号");
        
        // 确保没有残留的反引号
        assertFalse(result.contains("`"), "结果中不应该包含反引号");
        
        System.out.println("Backtick Conversion Result: " + result);
    }

    @Test
    @DisplayName("测试混合引用标识符的处理")
    public void testMixedQuotingIdentifiers() {
        // 测试混合引用的标识符
        String sql = "SELECT id, `name`, order_date FROM `test_table` WHERE status = 'active'";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        
        String result = generator.generate(queryStmt);
        
        // 反引号标识符应该被转换为双引号
        assertTrue(result.contains("\"test_table\""), "反引号表名应该被转换为双引号");
        assertTrue(result.contains("\"name\""), "反引号列名name应该被转换为双引号");
        
        // 普通标识符保持原样
        assertTrue(result.contains("id"), "普通列名id应该保持原样");
        assertTrue(result.contains("order_date"), "普通列名order_date应该保持原样");
        
        System.out.println("Mixed Quoting Result: " + result);
    }

    @Test
    @DisplayName("测试关键字标识符的处理")
    public void testKeywordIdentifierHandling() {
        // 测试MySQL关键字作为标识符的情况
        String sql = "SELECT `order`, `select`, `from` FROM `table` WHERE `where` = 'test'";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        
        String result = generator.generate(queryStmt);
        
        // 所有关键字标识符都应该被正确转换为双引号
        assertTrue(result.contains("\"order\""), "关键字order应该被转换为双引号");
        assertTrue(result.contains("\"select\""), "关键字select应该被转换为双引号");
        assertTrue(result.contains("\"from\""), "关键字from应该被转换为双引号");
        assertTrue(result.contains("\"table\""), "关键字table应该被转换为双引号");
        assertTrue(result.contains("\"where\""), "关键字where应该被转换为双引号");
        
        System.out.println("Keyword Identifier Result: " + result);
    }

    @Test
    @DisplayName("测试schema.table格式的标识符")
    public void testSchemaTableIdentifiers() {
        // 测试schema.table格式的标识符
        String sql = "SELECT * FROM `schema1`.`table1` WHERE `schema1`.`table1`.`id` > 100";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(sql);
        
        String result = generator.generate(queryStmt);
        
        // schema和table都应该被正确转换
        assertTrue(result.contains("\"schema1\".\"table1\""), "schema.table格式应该被正确转换");
        assertTrue(result.contains("\"schema1\".\"table1\".\"id\""), "完整的schema.table.column格式应该被正确转换");
        
        System.out.println("Schema.Table Result: " + result);
    }

    @Test
    @DisplayName("验证DDL和DML标识符处理的一致性")
    public void testDDLDMLConsistency() {
        // 创建表的DDL
        String createSql = "CREATE TABLE user_order (order_id INT, user_name VARCHAR(50))";
        CreateTable createTable = (CreateTable) MySqlHelper.parseStatement(createSql);
        String ddlResult = generator.generate(createTable);

        // 查询表的DML
        String selectSql = "SELECT order_id, user_name FROM user_order";
        QueryStmt queryStmt = new QueryStmt();
        queryStmt.setSql(selectSql);
        String dmlResult = generator.generate(queryStmt);

        System.out.println("DDL Result: " + ddlResult);
        System.out.println("DML Result: " + dmlResult);

        // 分析一致性
        boolean ddlHasQuotes = ddlResult.contains("\"user_order\"") && ddlResult.contains("\"order_id\"");
        boolean dmlHasQuotes = dmlResult.contains("\"user_order\"") && dmlResult.contains("\"order_id\"");

        // 记录当前行为
        System.out.println("DDL uses quotes: " + ddlHasQuotes);
        System.out.println("DML uses quotes: " + dmlHasQuotes);

        // 当前设计：DDL使用引号，DML不使用引号（除非原本有反引号）
        // 这是合理的设计，因为：
        // 1. DDL需要最大兼容性
        // 2. DML遵循PostgreSQL标准行为
        assertTrue(ddlHasQuotes, "DDL语句应该为标识符添加引号");

        // DML的行为取决于是否有反引号，这里测试的是普通标识符
        // 所以不强制要求引号
    }
}
