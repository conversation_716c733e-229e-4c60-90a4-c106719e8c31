package com.xylink.sqltranspiler.unit.dialects.dameng;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.create.CreateFunction;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.drop.DropFunction;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.shared.base.BaseDamengTest;

/**
 * 达梦数据库函数功能测试
 * 基于达梦官方文档的测试驱动开发
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/practice-func.html
 */
public class DamengFunctionTest extends BaseDamengTest {

    private DropFunction parseDropFunction(String sql) {
        return (DropFunction) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本CREATE FUNCTION语句")
    public void testBasicCreateFunction() {
        String sql = "CREATE FUNCTION get_employee_name(emp_id INT) RETURNS VARCHAR(100) " +
                    "BEGIN " +
                    "DECLARE emp_name VARCHAR(100); " +
                    "SELECT name INTO emp_name FROM employees WHERE id = emp_id; " +
                    "RETURN emp_name; " +
                    "END;";
        CreateFunction createFunction = parseCreateFunction(sql);
        String result = generator.generate(createFunction);

        System.out.println("Generated result: " + result);
        assertTrue(result.contains("CREATE FUNCTION"));
        assertTrue(result.contains("get_employee_name"));
        assertTrue(result.contains("RETURN")); // 达梦数据库使用RETURN而不是RETURNS
        assertTrue(result.contains("VARCHAR(100)"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试CREATE FUNCTION语句 - MySQL标准语法")
    public void testCreateFunction() {
        String sql = "CREATE FUNCTION calculate_bonus(salary DECIMAL(10,2)) RETURNS DECIMAL(10,2) " +
                    "BEGIN " +
                    "RETURN salary * 0.1; " +
                    "END;";
        CreateFunction createFunction = parseCreateFunction(sql);
        String result = generator.generate(createFunction);

        assertTrue(result.contains("CREATE FUNCTION"));
        assertTrue(result.contains("calculate_bonus"));
        assertTrue(result.contains("DECIMAL(10,2)"));
        assertTrue(result.contains("RETURN")); // 达梦数据库使用RETURN而不是RETURNS
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DETERMINISTIC函数")
    public void testDeterministicFunction() {
        String sql = "CREATE FUNCTION square_number(num INT) RETURNS INT " +
                    "DETERMINISTIC " +
                    "BEGIN " +
                    "RETURN num * num; " +
                    "END;";
        CreateFunction createFunction = parseCreateFunction(sql);
        String result = generator.generate(createFunction);

        assertTrue(result.contains("CREATE FUNCTION"));
        assertTrue(result.contains("square_number"));
        // 根据达梦官方文档，达梦数据库支持DETERMINISTIC关键字
        assertTrue(result.contains("DETERMINISTIC"));
        // 根据达梦官方文档，达梦数据库使用RETURNS语法，与MySQL兼容
        assertTrue(result.contains("RETURNS INT"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试复杂的CREATE FUNCTION语句")
    public void testComplexCreateFunction() {
        String sql = "CREATE FUNCTION get_department_stats(dept_id INT) " +
                    "RETURNS VARCHAR(500) " +
                    "BEGIN " +
                    "DECLARE emp_count INT DEFAULT 0; " +
                    "DECLARE avg_salary DECIMAL(10,2) DEFAULT 0; " +
                    "DECLARE result_text VARCHAR(500); " +
                    "SELECT COUNT(*), AVG(salary) INTO emp_count, avg_salary " +
                    "FROM employees WHERE department_id = dept_id; " +
                    "SET result_text = CONCAT('Count: ', emp_count, ', Avg Salary: ', avg_salary); " +
                    "RETURN result_text; " +
                    "END;";
        CreateFunction createFunction = parseCreateFunction(sql);
        String result = generator.generate(createFunction);
        
        assertTrue(result.contains("CREATE FUNCTION"));
        assertTrue(result.contains("get_department_stats"));
        // 根据达梦官方文档，达梦数据库支持RETURNS语法，与MySQL兼容
        assertTrue(result.contains("RETURNS VARCHAR(500)"));
        assertTrue(result.contains("BEGIN"));
        assertTrue(result.contains("END"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试基本DROP FUNCTION语句")
    public void testBasicDropFunction() {
        String sql = "DROP FUNCTION get_employee_name;";
        DropFunction dropFunction = parseDropFunction(sql);
        String result = generator.generate(dropFunction);
        
        assertTrue(result.contains("DROP FUNCTION"));
        assertTrue(result.contains("get_employee_name"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试DROP FUNCTION IF EXISTS语句")
    public void testDropFunctionIfExists() {
        String sql = "DROP FUNCTION IF EXISTS calculate_bonus;";
        DropFunction dropFunction = parseDropFunction(sql);
        String result = generator.generate(dropFunction);
        
        assertTrue(result.contains("DROP FUNCTION"));
        assertTrue(result.contains("IF EXISTS"));
        assertTrue(result.contains("calculate_bonus"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库Oracle兼容的函数语法")
    public void testDamengOracleCompatibility() {
        // 根据达梦官方文档，达梦数据库支持Oracle兼容的函数语法
        // 使用MySQL兼容语法进行测试，生成器会转换为达梦格式
        String sql = "CREATE FUNCTION format_currency(amount DECIMAL(15,2)) " +
                    "RETURNS VARCHAR(50) " +
                    "BEGIN " +
                    "RETURN CONCAT('$', amount); " +
                    "END;";
        CreateFunction createFunction = parseCreateFunction(sql);
        String result = generator.generate(createFunction);

        assertTrue(result.contains("CREATE FUNCTION"));
        assertTrue(result.contains("format_currency"));
        assertTrue(result.contains("DECIMAL(15,2)"));
        assertTrue(result.contains("VARCHAR(50)"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库函数参数处理")
    public void testDamengFunctionParameters() {
        String sql = "CREATE FUNCTION calculate_tax(income DECIMAL(15,2), rate DECIMAL(5,4)) " +
                    "RETURNS DECIMAL(15,2) " +
                    "BEGIN " +
                    "RETURN income * rate; " +
                    "END;";
        CreateFunction createFunction = parseCreateFunction(sql);
        String result = generator.generate(createFunction);
        
        assertTrue(result.contains("CREATE FUNCTION"));
        assertTrue(result.contains("calculate_tax"));
        assertTrue(result.contains("DECIMAL(15,2)"));
        assertTrue(result.contains("DECIMAL(5,4)"));
        assertTrue(result.contains("DECIMAL(5,4)"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库函数的反引号转换")
    public void testDamengFunctionBacktickConversion() {
        String sql = "CREATE FUNCTION `get_user_info`(`user_id` INT) RETURNS VARCHAR(200) " +
                    "BEGIN " +
                    "DECLARE `user_name` VARCHAR(100); " +
                    "SELECT `name` INTO `user_name` FROM `users` WHERE `id` = `user_id`; " +
                    "RETURN `user_name`; " +
                    "END;";
        CreateFunction createFunction = parseCreateFunction(sql);
        String result = generator.generate(createFunction);
        
        assertTrue(result.contains("CREATE FUNCTION"));
        assertTrue(result.contains("get_user_info")); // 反引号被移除，标识符正常显示
        assertTrue(result.contains("user_id"));
        assertTrue(result.contains("user_name"));
        assertTrue(result.contains("users"));
        assertFalse(result.contains("`")); // 确保反引号被移除
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试达梦数据库官方文档示例")
    public void testDamengOfficialExample() {
        // 根据达梦官方文档的函数示例
        // 使用MySQL兼容语法进行测试，生成器会转换为达梦格式
        String sql = "CREATE FUNCTION get_employee_count(dept_name VARCHAR(100)) " +
                    "RETURNS INT " +
                    "BEGIN " +
                    "DECLARE emp_count INT DEFAULT 0; " +
                    "SELECT COUNT(*) INTO emp_count FROM employees e " +
                    "JOIN departments d ON e.department_id = d.id " +
                    "WHERE d.name = dept_name; " +
                    "RETURN emp_count; " +
                    "END;";
        CreateFunction createFunction = parseCreateFunction(sql);
        String result = generator.generate(createFunction);

        assertTrue(result.contains("CREATE FUNCTION"));
        assertTrue(result.contains("get_employee_count"));
        assertTrue(result.contains("VARCHAR(100)"));
        assertTrue(result.contains("BEGIN"));
        assertTrue(result.contains("END"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试MySQL特有函数转换 - 基于达梦官方文档")
    public void testMySqlSpecificFunctions() {
        // 测试MySQL特有函数的转换，这些函数在达梦中需要等价实现
        // 根据达梦官方文档，某些MySQL函数需要转换为达梦等价函数

        // 注意：这里主要测试函数相关的表结构创建，实际函数转换在查询中处理
        String sql = "CREATE TABLE function_test (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "find_result INT, " +  // FIND_IN_SET结果
                    "yearweek_result INT, " +  // YEARWEEK结果
                    "period_diff_result INT, " +  // PERIOD_DIFF结果
                    "encrypted_data VARBINARY(255), " +  // AES_ENCRYPT结果
                    "division_result DECIMAL(10,4)" +  // 除零操作结果
                    ");";

        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        // 调试输出
        System.out.println("MySQL特有函数测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("function_test"));
        assertTrue(result.contains("find_result INT"));
        assertTrue(result.contains("yearweek_result INT"));
        assertTrue(result.contains("period_diff_result INT"));
        assertTrue(result.contains("encrypted_data VARBINARY(255)"));
        assertTrue(result.contains("division_result DECIMAL(10,4)"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试时间函数相关表结构 - 基于达梦官方文档")
    public void testTimeFunctionTables() {
        // 根据达梦官方文档，测试时间函数相关的表结构
        String sql = "CREATE TABLE time_function_test (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                    "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                    "date_field DATE, " +
                    "time_field TIME, " +
                    "datetime_field DATETIME, " +
                    "year_field YEAR" +
                    ");";

        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        // 调试输出
        System.out.println("时间函数表结构测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("time_function_test"));
        assertTrue(result.contains("created_at TIMESTAMP"));
        assertTrue(result.contains("updated_at TIMESTAMP"));
        assertTrue(result.contains("date_field DATE"));
        assertTrue(result.contains("time_field TIME"));
        assertTrue(result.contains("datetime_field DATETIME"));
        assertTrue(result.contains("year_field INT"));  // YEAR转换为INT
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试字符串函数相关表结构 - 基于达梦官方文档")
    public void testStringFunctionTables() {
        // 根据达梦官方文档，测试字符串函数相关的表结构
        String sql = "CREATE TABLE string_function_test (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "concat_result VARCHAR(500), " +
                    "substring_result VARCHAR(100), " +
                    "replace_result VARCHAR(255), " +
                    "upper_result VARCHAR(100), " +
                    "lower_result VARCHAR(100), " +
                    "trim_result VARCHAR(100)" +
                    ");";

        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        // 调试输出
        System.out.println("字符串函数表结构测试实际输出: " + result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("string_function_test"));
        assertTrue(result.contains("concat_result VARCHAR(500)"));
        assertTrue(result.contains("substring_result VARCHAR(100)"));
        assertTrue(result.contains("replace_result VARCHAR(255)"));
        assertTrue(result.contains("upper_result VARCHAR(100)"));
        assertTrue(result.contains("lower_result VARCHAR(100)"));
        assertTrue(result.contains("trim_result VARCHAR(100)"));
        assertTrue(result.endsWith(";"));
    }
}
