package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库NULL值处理测试
 * 基于神通数据库官方文档第2.5节空值NULL规范
 * 
 * 根据文档：
 * - 表中一行的某列没有值，称该列为空(NULL)，或包含空值
 * - 空值可以出现在任意没有NOT NULL或PRIMARY KEY限制的列中
 * - 不要用NULL代表0，它们不等
 * - 长度为零的字符串也不等于NULL
 * - 任何包含NULL的数值表达式的结果通常也为NULL
 * 
 * 参考文档：神通数据库SQL参考手册 第2.5节
 */
@DisplayName("神通数据库NULL值处理测试")
public class ShentongNullHandlingTest extends BaseShentongConversionTest {

    /**
     * 测试NULL值的基本定义和使用
     * 根据文档：表中一行的某列没有值，称该列为空(NULL)
     */
    @Test
    @DisplayName("测试NULL值的基本定义和使用")
    public void testBasicNullDefinition() throws Exception {
        String mysqlSql = """
            CREATE TABLE null_test (
                id INT PRIMARY KEY,
                nullable_col VARCHAR(100),
                not_null_col VARCHAR(100) NOT NULL,
                default_null_col VARCHAR(100) DEFAULT NULL
            );
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证NULL值定义支持
        assertTrue(shentongSql.contains("CREATE TABLE"), "应支持CREATE TABLE语句");
        assertTrue(shentongSql.contains("\"null_test\""), "表名应正确转换");
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持主键约束");
        assertTrue(shentongSql.contains("NOT NULL"), "应支持NOT NULL约束");
        assertTrue(shentongSql.contains("DEFAULT NULL"), "应支持DEFAULT NULL");
    }

    /**
     * 测试NULL与0的区别
     * 根据文档：不要用NULL代表0，它们不等
     */
    @Test
    @DisplayName("测试NULL与0的区别")
    public void testNullVsZeroDistinction() throws Exception {
        String mysqlSql = """
            INSERT INTO comparison_test (id, null_col, zero_col, empty_string_col) VALUES
            (1, NULL, 0, ''),
            (2, NULL, 0, ''),
            (3, 100, 0, 'test');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证NULL与0的区别处理
        assertTrue(shentongSql.contains("INSERT INTO"), "应支持INSERT语句");
        assertTrue(shentongSql.contains("NULL"), "应支持NULL值");
        assertTrue(shentongSql.contains("0"), "应支持数值0");
        assertTrue(shentongSql.contains("''"), "应支持空字符串");
        assertTrue(shentongSql.contains("'test'"), "应支持非空字符串");
    }

    /**
     * 测试NULL与空字符串的区别
     * 根据文档：长度为零的字符串也不等于NULL
     */
    @Test
    @DisplayName("测试NULL与空字符串的区别")
    public void testNullVsEmptyStringDistinction() throws Exception {
        String mysqlSql = """
            SELECT 
                CASE 
                    WHEN col1 IS NULL THEN 'NULL值'
                    WHEN col1 = '' THEN '空字符串'
                    ELSE '有值'
                END as result
            FROM string_null_test;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证NULL与空字符串的区别处理
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("CASE"), "应支持CASE表达式");
        assertTrue(shentongSql.contains("IS NULL"), "应支持IS NULL判断");
        assertTrue(shentongSql.contains("= ''"), "应支持空字符串比较");
        assertTrue(shentongSql.contains("NULL值"), "应支持中文字符串");
        assertTrue(shentongSql.contains("空字符串"), "应支持中文字符串");
    }

    /**
     * 测试包含NULL的数值表达式
     * 根据文档：任何包含NULL的数值表达式的结果通常也为NULL - 双重测试策略第二部分
     */
    @Test
    @DisplayName("测试包含NULL的数值表达式")
    public void testValidMySqlNullInNumericExpressions() throws Exception {
        String mysqlSql = """
            SELECT
                NULL + 12 as null_plus_number,
                NULL * 5 as null_multiply,
                NULL / 2 as null_divide,
                NULL - 10 as null_minus,
                COALESCE(NULL, 0) as null_coalesce;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证NULL在数值表达式中的处理
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("NULL + 12"), "应支持NULL加法运算");
        assertTrue(shentongSql.contains("NULL * 5"), "应支持NULL乘法运算");
        assertTrue(shentongSql.contains("NULL / 2"), "应支持NULL除法运算");
        assertTrue(shentongSql.contains("NULL - 10"), "应支持NULL减法运算");
        assertTrue(shentongSql.contains("COALESCE"), "应支持COALESCE函数");
    }

    /**
     * 测试MySQL标准dual表语法转换成功
     * 验证MySQL 8.4官方支持的dual表语法能正确转换 - 双重测试策略第二部分
     */
    @Test
    @DisplayName("测试MySQL标准dual表语法转换成功")
    public void testValidMySqlDualTableConversion() throws Exception {
        String mysqlSql = """
            SELECT
                NULL + 12 as null_plus_number,
                COALESCE(NULL, 0) as null_coalesce
            FROM dual;
            """;

        String result = convertMySqlToShentong(mysqlSql);

        // 验证MySQL dual表语法转换成功 - 根据MySQL 8.4官方文档，dual是支持的虚拟表
        assertBasicConversionRequirements(result);
        assertTrue(result.contains("FROM dual"), "应保持FROM dual语法，因为MySQL 8.4官方文档支持此语法");
        assertTrue(result.contains("NULL + 12"), "应支持NULL加法运算");
        assertTrue(result.contains("COALESCE"), "应支持COALESCE函数");
    }

    /**
     * 测试Oracle SYSDATE语法被正确拒绝
     * 验证MySQL强制语法校验工作正常 - 双重测试策略第一部分
     */
    @Test
    @DisplayName("测试Oracle SYSDATE语法被正确拒绝")
    public void testInvalidOracleSysdateRejection() throws Exception {
        String oracleSql = """
            SELECT
                NULL + 12 as null_plus_number,
                SYSDATE as current_time
            FROM dual;
            """;

        String result = convertMySqlToShentong(oracleSql);

        // 验证Oracle SYSDATE语法被正确拒绝 - 根据MySQL 8.4官方文档，SYSDATE（不带括号）不是MySQL语法
        assertTrue(result.isEmpty(), "Oracle SYSDATE语法应该被MySQL强制校验拒绝，转换结果应为空");
    }

    /**
     * 测试NULL值在WHERE条件中的使用
     * 验证IS NULL和IS NOT NULL的正确处理
     */
    @Test
    @DisplayName("测试NULL值在WHERE条件中的使用")
    public void testNullInWhereConditions() throws Exception {
        String mysqlSql = """
            SELECT * FROM users 
            WHERE email IS NULL 
               OR phone IS NOT NULL
               AND (description IS NULL OR description != '');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证NULL在WHERE条件中的处理
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("WHERE"), "应支持WHERE子句");
        assertTrue(shentongSql.contains("IS NULL"), "应支持IS NULL条件");
        assertTrue(shentongSql.contains("IS NOT NULL"), "应支持IS NOT NULL条件");
        assertTrue(shentongSql.contains("OR"), "应支持OR逻辑运算");
        assertTrue(shentongSql.contains("AND"), "应支持AND逻辑运算");
    }

    /**
     * 测试NULL值在聚合函数中的处理
     * 验证COUNT、SUM、AVG等函数对NULL值的处理
     */
    @Test
    @DisplayName("测试NULL值在聚合函数中的处理")
    public void testNullInAggregateFunctions() throws Exception {
        String mysqlSql = """
            SELECT 
                COUNT(*) as total_rows,
                COUNT(nullable_col) as non_null_count,
                SUM(nullable_num) as sum_result,
                AVG(nullable_num) as avg_result,
                MAX(nullable_col) as max_result,
                MIN(nullable_col) as min_result
            FROM aggregate_test;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证NULL在聚合函数中的处理
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("COUNT(*)"), "应支持COUNT(*)函数");
        assertTrue(shentongSql.contains("COUNT("), "应支持COUNT函数");
        assertTrue(shentongSql.contains("SUM("), "应支持SUM函数");
        assertTrue(shentongSql.contains("AVG("), "应支持AVG函数");
        assertTrue(shentongSql.contains("MAX("), "应支持MAX函数");
        assertTrue(shentongSql.contains("MIN("), "应支持MIN函数");
    }

    /**
     * 测试NULL值在JOIN操作中的处理
     * 验证NULL值在表连接中的行为
     */
    @Test
    @DisplayName("测试NULL值在JOIN操作中的处理")
    public void testNullInJoinOperations() throws Exception {
        String mysqlSql = """
            SELECT u.id, u.name, p.phone
            FROM users u
            LEFT JOIN phone_numbers p ON u.id = p.user_id
            WHERE u.email IS NULL
               OR p.phone IS NOT NULL;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证NULL在JOIN操作中的处理
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("LEFT JOIN"), "应支持LEFT JOIN");
        assertTrue(shentongSql.contains("ON"), "应支持ON条件");
        assertTrue(shentongSql.contains("IS NULL"), "应支持IS NULL条件");
        assertTrue(shentongSql.contains("IS NOT NULL"), "应支持IS NOT NULL条件");
    }

    /**
     * 测试NULL值的更新操作
     * 验证UPDATE语句中NULL值的处理
     */
    @Test
    @DisplayName("测试NULL值的更新操作")
    public void testNullInUpdateOperations() throws Exception {
        String mysqlSql = """
            UPDATE users 
            SET email = NULL,
                phone = CASE 
                    WHEN phone IS NULL THEN '未知'
                    ELSE phone
                END,
                updated_at = NOW()
            WHERE id = 1;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证NULL在UPDATE操作中的处理
        assertTrue(shentongSql.contains("UPDATE"), "应支持UPDATE语句");
        assertTrue(shentongSql.contains("SET"), "应支持SET子句");
        assertTrue(shentongSql.contains("= NULL"), "应支持设置为NULL");
        assertTrue(shentongSql.contains("CASE"), "应支持CASE表达式");
        assertTrue(shentongSql.contains("IS NULL"), "应支持IS NULL判断");
        assertTrue(shentongSql.contains("WHERE"), "应支持WHERE条件");
    }

    /**
     * 测试NULL值的排序行为
     * 验证ORDER BY中NULL值的排序规则
     */
    @Test
    @DisplayName("测试NULL值的排序行为")
    public void testNullInOrderBy() throws Exception {
        String mysqlSql = """
            SELECT id, name, email, phone
            FROM users
            ORDER BY email ASC, phone DESC, name ASC;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        // 验证基本转换成功
        assertBasicConversionRequirements(shentongSql);
        
        // 验证NULL在ORDER BY中的处理
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT语句");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY子句");
        assertTrue(shentongSql.contains("ASC"), "应支持ASC排序");
        assertTrue(shentongSql.contains("DESC"), "应支持DESC排序");
    }
}
