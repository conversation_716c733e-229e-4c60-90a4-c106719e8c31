package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金仓数据库兼容性测试
 * 
 * 根据金仓官方文档，测试MySQL语法到金仓的兼容性转换
 * 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 */
@DisplayName("金仓数据库兼容性测试")
public class KingbaseCompatibilityTest {

    private KingbaseGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new KingbaseGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试MySQL数据类型兼容性")
    void testMySQLDataTypeCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_types (" +
                "tinyint_col TINYINT, " +
                "smallint_col SMALLINT, " +
                "mediumint_col MEDIUMINT, " +
                "int_col INT, " +
                "bigint_col BIGINT, " +
                "float_col FLOAT, " +
                "double_col DOUBLE, " +
                "decimal_col DECIMAL(10,2), " +
                "varchar_col VARCHAR(255), " +
                "text_col TEXT, " +
                "date_col DATE, " +
                "datetime_col DATETIME, " +
                "timestamp_col TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("MySQL数据类型兼容性转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证MySQL数据类型兼容性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"mysql_types\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("\"tinyint_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"smallint_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"int_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"bigint_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"varchar_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"text_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("TINYINT"), "应保留TINYINT类型");
        assertTrue(kingbaseSql.contains("SMALLINT"), "应保留SMALLINT类型");
        assertTrue(kingbaseSql.contains("INT"), "应保留INT类型");
        assertTrue(kingbaseSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(kingbaseSql.contains("FLOAT"), "应保留FLOAT类型");
        assertTrue(kingbaseSql.contains("DOUBLE"), "应保留DOUBLE类型");
        assertTrue(kingbaseSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        assertTrue(kingbaseSql.contains("VARCHAR(255)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        assertTrue(kingbaseSql.contains("DATE"), "应保留DATE类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试MySQL约束兼容性")
    void testMySQLConstraintCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_constraints (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "email VARCHAR(100) NOT NULL UNIQUE, " +
                "age INT CHECK (age >= 0), " +
                "status ENUM('active', 'inactive') DEFAULT 'active', " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("MySQL约束兼容性转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证MySQL约束兼容性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"mysql_constraints\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键约束");
        assertTrue(kingbaseSql.contains("\"email\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"age\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"status\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"created_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        
        // 验证约束处理（金仓可能在单独的语句中处理约束）
        assertTrue(kingbaseSql.contains("CHECK") || kingbaseSql.contains("age"), 
                  "应处理CHECK约束");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试MySQL函数兼容性")
    void testMySQLFunctionCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_functions (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "uuid_col VARCHAR(36) DEFAULT (UUID()), " +
                "random_col DOUBLE DEFAULT (RAND())" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("MySQL函数兼容性转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证MySQL函数兼容性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"mysql_functions\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"created_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"updated_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"uuid_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"random_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(kingbaseSql.contains("VARCHAR(36)"), "应保留UUID列类型");
        assertTrue(kingbaseSql.contains("DOUBLE"), "应保留DOUBLE类型");
        
        // 验证时间函数转换（金仓支持PostgreSQL兼容的函数）
        assertTrue(kingbaseSql.contains("CURRENT_TIMESTAMP") || kingbaseSql.contains("NOW"), 
                  "应转换时间函数");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试MySQL存储引擎兼容性")
    void testMySQLStorageEngineCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_engines (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "data VARCHAR(100)" +
                ") ENGINE=InnoDB " +
                "AUTO_INCREMENT=1000 " +
                "DEFAULT CHARSET=utf8mb4 " +
                "COLLATE=utf8mb4_unicode_ci " +
                "COMMENT='MySQL存储引擎测试表';";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("MySQL存储引擎兼容性转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证MySQL存储引擎兼容性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"mysql_engines\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留数据类型");
        // 验证注释处理（金仓可能在单独的语句中处理注释）
        assertTrue(kingbaseSql.contains("COMMENT") || kingbaseSql.contains("mysql_engines"),
                  "应处理注释");
        assertTrue(kingbaseSql.contains("MySQL存储引擎测试表") || kingbaseSql.contains("mysql_engines"),
                  "应处理注释内容");
        
        // 验证MySQL特有选项的处理（金仓可能忽略或转换）
        assertFalse(kingbaseSql.contains("ENGINE=InnoDB"), "不应包含MySQL特有的ENGINE选项");
        assertFalse(kingbaseSql.contains("utf8mb4"), "不应包含MySQL特有的字符集");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试MySQL索引兼容性")
    void testMySQLIndexCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_indexes (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "email VARCHAR(100) UNIQUE, " +
                "content TEXT, " +
                "INDEX idx_name (name), " +
                "INDEX idx_name_email (name, email), " +
                "FULLTEXT INDEX ft_content (content)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("MySQL索引兼容性转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证MySQL索引兼容性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"mysql_indexes\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"name\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"email\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"content\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        
        // 验证索引处理（金仓可能在单独的语句中处理索引）
        assertTrue(kingbaseSql.contains("idx_name") || kingbaseSql.contains("name"), 
                  "应处理name索引");
        assertTrue(kingbaseSql.contains("idx_name_email") || kingbaseSql.contains("email"), 
                  "应处理复合索引");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试MySQL分区表兼容性")
    void testMySQLPartitionCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_partitions (" +
                "id INT AUTO_INCREMENT, " +
                "order_date DATE NOT NULL, " +
                "amount DECIMAL(10,2), " +
                "PRIMARY KEY (id, order_date)" +
                ") PARTITION BY RANGE (YEAR(order_date)) (" +
                "PARTITION p2020 VALUES LESS THAN (2021), " +
                "PARTITION p2021 VALUES LESS THAN (2022), " +
                "PARTITION p2022 VALUES LESS THAN (2023)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("MySQL分区表兼容性转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证MySQL分区表兼容性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"mysql_partitions\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"order_date\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"amount\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("DATE"), "应保留DATE类型");
        assertTrue(kingbaseSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        
        // 验证分区处理（金仓支持PostgreSQL兼容的分区）
        assertTrue(kingbaseSql.contains("PARTITION") || kingbaseSql.contains("mysql_partitions"), 
                  "应处理分区定义");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试MySQL字符集兼容性")
    void testMySQLCharsetCompatibility() {
        String mysqlSql = "CREATE TABLE mysql_charset (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "utf8_col VARCHAR(100) CHARACTER SET utf8, " +
                "utf8mb4_col VARCHAR(100) CHARACTER SET utf8mb4, " +
                "latin1_col VARCHAR(100) CHARACTER SET latin1, " +
                "binary_col VARCHAR(100) CHARACTER SET binary" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("MySQL字符集兼容性转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证MySQL字符集兼容性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"mysql_charset\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"utf8_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"utf8mb4_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"latin1_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"binary_col\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        
        // 验证字符集处理（金仓移除MySQL特有的字符集声明）
        assertFalse(kingbaseSql.contains("CHARACTER SET utf8mb4"), 
                   "不应包含MySQL特有的字符集声明");
        assertFalse(kingbaseSql.contains("ENGINE=InnoDB"), 
                   "不应包含MySQL特有的存储引擎");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试金仓PostgreSQL兼容性特性")
    void testKingbasePostgreSQLCompatibilityFeatures() {
        String mysqlSql = "CREATE TABLE kingbase_postgresql_compatibility (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "json_data JSON, " +
                "text_array TEXT, " +
                "numeric_data DECIMAL(20,6), " +
                "timestamp_data TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "boolean_data BOOLEAN DEFAULT FALSE" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓PostgreSQL兼容性特性:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证金仓PostgreSQL兼容性特性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"kingbase_postgresql_compatibility\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"json_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"text_array\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"numeric_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"timestamp_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"boolean_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("JSON"), "应保留JSON类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        assertTrue(kingbaseSql.contains("DECIMAL(20,6)"), "应保留高精度数值类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("BOOLEAN"), "应保留BOOLEAN类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试复杂兼容性场景")
    void testComplexCompatibilityScenario() {
        String mysqlSql = "CREATE TABLE complex_compatibility (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "user_data JSON, " +
                "file_content LONGBLOB, " +
                "search_text TEXT, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "status ENUM('draft', 'published', 'archived') DEFAULT 'draft'" +
                ") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("复杂兼容性场景转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证复杂兼容性场景
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"complex_compatibility\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("BIGSERIAL") || kingbaseSql.contains("BIGINT"), 
                  "应转换BIGINT AUTO_INCREMENT为BIGSERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"user_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"file_content\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"search_text\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"created_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"updated_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"status\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("JSON"), "应保留JSON类型");
        assertTrue(kingbaseSql.contains("LONGBLOB") || kingbaseSql.contains("BYTEA"), 
                  "应保留或转换大二进制类型");
        assertTrue(kingbaseSql.contains("TEXT"), "应保留TEXT类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证MySQL特有选项的移除
        assertFalse(kingbaseSql.contains("ENGINE=InnoDB"), 
                   "不应包含MySQL特有的存储引擎");
        assertFalse(kingbaseSql.contains("utf8mb4_unicode_ci"), 
                   "不应包含MySQL特有的排序规则");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }
}
