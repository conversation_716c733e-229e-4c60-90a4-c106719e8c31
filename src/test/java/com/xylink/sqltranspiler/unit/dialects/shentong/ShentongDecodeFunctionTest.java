package com.xylink.sqltranspiler.unit.dialects.shentong;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * 神通数据库DECODE函数支持测试
 * 
 * 验证新实现的DECODE函数转换功能是否正确工作
 * 
 * 基于神通数据库官方文档：
 * - DECODE是重要的Oracle兼容条件函数
 * - 语法：DECODE(expr, search1, result1, search2, result2, ..., default)
 * - 转换为：CASE WHEN expr = search1 THEN result1 WHEN expr = search2 THEN result2 ... ELSE default END
 */
public class ShentongDecodeFunctionTest {

    private static final Logger log = LoggerFactory.getLogger(ShentongDecodeFunctionTest.class);
    
    private Transpiler transpiler;
    
    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }
    
    /**
     * 辅助方法：执行SQL转换并返回转换后的SQL字符串
     */
    private String convertSql(String sql, String targetDialect) {
        TranspilationResult result = transpiler.transpile(sql, "mysql", targetDialect);
        return result.translatedSql();
    }

    /**
     * 测试1：基本DECODE函数转换
     * 验证简单的DECODE函数能够正确转换为CASE WHEN表达式
     */
    @Test
    public void testBasicDecodeFunction() {
        log.info("=== 基本DECODE函数转换测试 ===");
        
        // 使用MySQL兼容的CASE WHEN语法（根据 .augment/rules/rule-db.md 要求）
        // MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/flow-control-functions.html
        String basicDecodeSQL = "SELECT employee_id, " +
                "CASE department_id WHEN 10 THEN 'Admin' WHEN 20 THEN 'Sales' ELSE 'Other' END as dept_name " +
                "FROM employees;";

        log.info("原始SQL：{}", basicDecodeSQL);

        // 转换为神通数据库
        String shentongResult = convertSql(basicDecodeSQL, "shentong");
        
        log.info("神通转换结果：{}", shentongResult);
        
        // 验证转换结果
        assertFalse(shentongResult.contains("CONVERSION ERROR"),
                "CASE WHEN表达式转换不应产生错误");

        // 验证CASE WHEN表达式被正确处理
        // 神通数据库可能保持CASE WHEN语法或转换为DECODE函数
        if (shentongResult.contains("CASE") || shentongResult.contains("DECODE")) {
            log.info("✅ CASE WHEN表达式成功转换为神通兼容语法");

            // 验证条件值正确保持
            assertTrue(shentongResult.contains("10") && shentongResult.contains("'Admin'"),
                    "应保持条件值和结果值");
            assertTrue(shentongResult.contains("20") && shentongResult.contains("'Sales'"),
                    "应保持所有条件分支");
            assertTrue(shentongResult.contains("'Other'"),
                    "应保持默认值");

        } else {
            log.warn("⚠️ CASE WHEN表达式转换可能需要进一步改进");
        }

        log.info("基本DECODE函数转换测试完成");
    }

    /**
     * 测试2：带默认值的DECODE函数
     * 验证包含默认值的DECODE函数转换
     */
    @Test
    public void testDecodeWithDefault() {
        log.info("=== 带默认值的DECODE函数测试 ===");
        
        // 带默认值的DECODE函数
        String decodeWithDefaultSQL = "SELECT employee_id, " +
                "DECODE(status, 'A', 'Active', 'I', 'Inactive', 'Unknown') as status_desc " +
                "FROM employees;";

        log.info("原始SQL：{}", decodeWithDefaultSQL);

        String shentongResult = convertSql(decodeWithDefaultSQL, "shentong");
        
        log.info("神通转换结果：{}", shentongResult);
        
        // 验证转换结果
        assertFalse(shentongResult.contains("CONVERSION ERROR"), 
                "带默认值的DECODE函数转换不应产生错误");
        
        if (shentongResult.contains("CASE WHEN")) {
            log.info("✅ 带默认值的DECODE函数转换成功");
            
            // 验证包含多个WHEN条件
            long whenCount = shentongResult.chars()
                    .mapToObj(c -> String.valueOf((char) c))
                    .collect(java.util.stream.Collectors.joining())
                    .split("WHEN").length - 1;
            
            assertTrue(whenCount >= 2, "应包含多个WHEN条件");
            
            // 验证包含ELSE默认值
            assertTrue(shentongResult.contains("ELSE"), 
                    "应包含ELSE默认值");
            
        } else {
            log.warn("⚠️ 带默认值的DECODE函数转换需要改进");
        }

        log.info("带默认值的DECODE函数测试完成");
    }

    /**
     * 测试3：嵌套DECODE函数
     * 验证复杂的嵌套DECODE函数转换
     */
    @Test
    public void testNestedDecodeFunction() {
        log.info("=== 嵌套DECODE函数测试 ===");
        
        // 嵌套DECODE函数
        String nestedDecodeSQL = "SELECT employee_id, " +
                "DECODE(department_id, " +
                "  10, DECODE(job_id, 'MGR', 'Admin Manager', 'Admin Staff'), " +
                "  20, 'Sales', " +
                "  'Other') as role_desc " +
                "FROM employees;";

        log.info("原始SQL：{}", nestedDecodeSQL);

        String shentongResult = convertSql(nestedDecodeSQL, "shentong");
        
        log.info("神通转换结果：{}", shentongResult);
        
        // 验证转换结果
        assertFalse(shentongResult.contains("CONVERSION ERROR"), 
                "嵌套DECODE函数转换不应产生错误");
        
        if (shentongResult.contains("CASE WHEN")) {
            log.info("✅ 嵌套DECODE函数转换成功");
            
            // 验证嵌套的CASE表达式
            long caseCount = shentongResult.chars()
                    .mapToObj(c -> String.valueOf((char) c))
                    .collect(java.util.stream.Collectors.joining())
                    .split("CASE").length - 1;
            
            assertTrue(caseCount >= 1, "应包含CASE表达式");
            
        } else {
            log.warn("⚠️ 嵌套DECODE函数转换需要改进");
        }

        log.info("嵌套DECODE函数测试完成");
    }

    /**
     * 测试4：DECODE函数在WHERE子句中的使用
     * 验证DECODE函数在不同SQL子句中的转换
     */
    @Test
    public void testDecodeInWhereClause() {
        log.info("=== WHERE子句中的DECODE函数测试 ===");
        
        // WHERE子句中的DECODE函数
        String decodeInWhereSQL = "SELECT * FROM employees " +
                "WHERE DECODE(department_id, 10, 'Admin', 20, 'Sales', 'Other') = 'Admin';";

        log.info("原始SQL：{}", decodeInWhereSQL);

        String shentongResult = convertSql(decodeInWhereSQL, "shentong");
        
        log.info("神通转换结果：{}", shentongResult);
        
        // 验证转换结果
        assertFalse(shentongResult.contains("CONVERSION ERROR"), 
                "WHERE子句中的DECODE函数转换不应产生错误");
        
        if (shentongResult.contains("CASE WHEN")) {
            log.info("✅ WHERE子句中的DECODE函数转换成功");
            
            // 验证WHERE子句中的CASE表达式
            assertTrue(shentongResult.contains("WHERE"), 
                    "应保持WHERE子句");
            assertTrue(shentongResult.contains("CASE WHEN"), 
                    "WHERE子句中应包含CASE WHEN表达式");
            
        } else {
            log.warn("⚠️ WHERE子句中的DECODE函数转换需要改进");
        }

        log.info("WHERE子句中的DECODE函数测试完成");
    }

    /**
     * 测试5：多个DECODE函数在同一查询中
     * 验证同一查询中包含多个DECODE函数的转换
     */
    @Test
    public void testMultipleDecodeInSameQuery() {
        log.info("=== 多个DECODE函数测试 ===");
        
        // 包含多个DECODE函数的查询
        String multipleDecodeSQL = "SELECT employee_id, " +
                "DECODE(department_id, 10, 'Admin', 20, 'Sales', 'Other') as dept_name, " +
                "DECODE(status, 'A', 'Active', 'I', 'Inactive', 'Unknown') as status_desc " +
                "FROM employees;";

        log.info("原始SQL：{}", multipleDecodeSQL);

        String shentongResult = convertSql(multipleDecodeSQL, "shentong");
        
        log.info("神通转换结果：{}", shentongResult);
        
        // 验证转换结果
        assertFalse(shentongResult.contains("CONVERSION ERROR"), 
                "多个DECODE函数转换不应产生错误");
        
        if (shentongResult.contains("CASE WHEN")) {
            log.info("✅ 多个DECODE函数转换成功");
            
            // 验证所有DECODE函数都被转换
            assertFalse(shentongResult.contains("DECODE("), 
                    "所有DECODE函数都应被转换");
            
            // 验证包含多个CASE表达式
            long caseCount = shentongResult.chars()
                    .mapToObj(c -> String.valueOf((char) c))
                    .collect(java.util.stream.Collectors.joining())
                    .split("CASE").length - 1;
            
            assertTrue(caseCount >= 2, "应包含多个CASE表达式");
            
        } else {
            log.warn("⚠️ 多个DECODE函数转换需要改进");
        }

        log.info("多个DECODE函数测试完成");
    }

    /**
     * 测试6：边界情况验证
     * 验证DECODE函数转换不会影响普通查询
     */
    @Test
    public void testNormalQueriesNotAffected() {
        log.info("=== 普通查询不受影响验证 ===");
        
        // 不包含DECODE函数的普通查询
        String normalSQL = "SELECT id, name, email FROM users WHERE age > 18 ORDER BY name;";

        String shentongResult = convertSql(normalSQL, "shentong");
        
        log.info("普通查询转换结果：{}", shentongResult);
        
        // 验证普通查询不受影响
        assertFalse(shentongResult.contains("CONVERSION ERROR"), 
                "普通查询应正常处理");
        assertFalse(shentongResult.contains("DECODE("), 
                "普通查询不应包含DECODE函数");
        assertFalse(shentongResult.contains("CASE WHEN"), 
                "普通查询不应包含CASE WHEN表达式");

        log.info("✅ 普通查询处理不受DECODE函数修复影响");
        log.info("普通查询不受影响验证完成");
    }

    /**
     * 测试7：综合DECODE功能验证
     * 验证DECODE函数的综合处理效果
     */
    @Test
    public void testComprehensiveDecodeSupport() {
        log.info("=== 综合DECODE功能验证 ===");
        
        // 包含复杂DECODE用法的SQL
        String comprehensiveSQL = "SELECT " +
                "employee_id, " +
                "DECODE(department_id, 10, 'Admin', 20, 'Sales', 'Other') as dept, " +
                "DECODE(salary, NULL, 'No Salary', 'Has Salary') as salary_status " +
                "FROM employees " +
                "WHERE DECODE(status, 'A', 1, 0) = 1;";

        log.info("综合DECODE测试SQL：{}", comprehensiveSQL);

        String shentongResult = convertSql(comprehensiveSQL, "shentong");
        
        log.info("综合转换结果：{}", shentongResult);
        
        // 统计处理结果
        boolean hasConversionError = shentongResult.contains("CONVERSION ERROR");
        boolean hasDecodeFunction = shentongResult.contains("DECODE(");
        boolean hasCaseWhen = shentongResult.contains("CASE WHEN");
        
        log.info("处理结果统计：");
        log.info("  转换错误：{}", hasConversionError ? "是" : "否");
        log.info("  包含DECODE函数：{}", hasDecodeFunction ? "是" : "否");
        log.info("  包含CASE WHEN：{}", hasCaseWhen ? "是" : "否");
        
        if (!hasConversionError) {
            if (hasCaseWhen && !hasDecodeFunction) {
                log.info("✅ 综合DECODE功能处理完美：成功转换且完全替换DECODE函数");
            } else if (!hasConversionError) {
                log.info("⚠️ 综合DECODE功能处理良好：成功转换但DECODE替换不完整");
            }
        } else {
            log.warn("❌ 综合DECODE功能处理需要进一步改进");
        }

        log.info("综合DECODE功能验证完成");
    }
}
