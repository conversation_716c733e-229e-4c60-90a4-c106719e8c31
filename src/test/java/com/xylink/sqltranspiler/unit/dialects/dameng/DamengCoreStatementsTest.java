package com.xylink.sqltranspiler.unit.dialects.dameng;

import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 达梦数据库核心语句测试
 * 
 * 根据达梦官方文档，测试MySQL核心SQL语句到达梦的转换
 * 参考：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
 * 
 * 测试覆盖：
 * - CREATE TABLE语句的核心功能
 * - 数据类型转换的核心支持
 * - 约束定义的核心转换
 * - 索引定义的核心转换
 */
@DisplayName("达梦数据库核心语句测试")
public class DamengCoreStatementsTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基础CREATE TABLE语句转换")
    void testBasicCreateTableStatement() {
        String mysqlSql = "CREATE TABLE basic_table (" +
                "id INT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL, " +
                "age INT" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("基础CREATE TABLE语句转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证基础CREATE TABLE语句转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE关键字");
        assertTrue(damengSql.contains("basic_table"), "应保留表名");
        assertTrue(damengSql.contains("id"), "应保留id列");
        assertTrue(damengSql.contains("name"), "应保留name列");
        assertTrue(damengSql.contains("age"), "应保留age列");
        assertTrue(damengSql.contains("INT"), "应保留INT数据类型");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留VARCHAR数据类型");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键约束");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
    }

    @Test
    @DisplayName("测试核心数据类型转换")
    void testCoreDataTypeConversion() {
        String mysqlSql = "CREATE TABLE core_types (" +
                "int_col INT, " +
                "varchar_col VARCHAR(255), " +
                "text_col TEXT, " +
                "decimal_col DECIMAL(10,2), " +
                "date_col DATE, " +
                "timestamp_col TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("核心数据类型转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证核心数据类型转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("core_types"), "应保留表名");
        assertTrue(damengSql.contains("INT"), "应保留INT类型");
        assertTrue(damengSql.contains("VARCHAR(255)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        assertTrue(damengSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        assertTrue(damengSql.contains("DATE"), "应保留DATE类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
    }

    @Test
    @DisplayName("测试核心约束转换")
    void testCoreConstraintConversion() {
        String mysqlSql = "CREATE TABLE core_constraints (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "email VARCHAR(100) NOT NULL UNIQUE, " +
                "age INT CHECK (age >= 0), " +
                "status VARCHAR(20) DEFAULT 'active'" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("核心约束转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证核心约束转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("core_constraints"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键约束");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(damengSql.contains("CHECK") || damengSql.contains("age"), 
                  "应处理CHECK约束");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("'active'"), "应保留字符串默认值");
    }

    @Test
    @DisplayName("测试核心索引转换")
    void testCoreIndexConversion() {
        String mysqlSql = "CREATE TABLE core_indexes (" +
                "id INT PRIMARY KEY, " +
                "name VARCHAR(100), " +
                "email VARCHAR(100) UNIQUE, " +
                "content TEXT, " +
                "INDEX idx_name (name)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("核心索引转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证核心索引转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("core_indexes"), "应保留表名");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        
        // 验证索引处理（达梦可能在单独的语句中处理索引）
        assertTrue(damengSql.contains("idx_name") || damengSql.contains("name"), 
                  "应处理name索引");
    }

    @Test
    @DisplayName("测试外键约束转换")
    void testForeignKeyConstraintConversion() {
        String mysqlSql = "CREATE TABLE orders (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "user_id INT NOT NULL, " +
                "product_id INT, " +
                "FOREIGN KEY (user_id) REFERENCES users(id), " +
                "FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("外键约束转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证外键约束转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("orders"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("user_id"), "应保留user_id列");
        assertTrue(damengSql.contains("product_id"), "应保留product_id列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        
        // 验证外键处理（达梦可能在单独的语句中处理外键）
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("user_id"), "应处理外键约束");
    }

    @Test
    @DisplayName("测试复合主键转换")
    void testCompositeKeyConversion() {
        String mysqlSql = "CREATE TABLE composite_key (" +
                "user_id INT NOT NULL, " +
                "role_id INT NOT NULL, " +
                "assigned_date DATE NOT NULL, " +
                "status VARCHAR(20) DEFAULT 'active', " +
                "PRIMARY KEY (user_id, role_id)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("复合主键转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证复合主键转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("composite_key"), "应保留表名");
        assertTrue(damengSql.contains("user_id"), "应保留user_id列");
        assertTrue(damengSql.contains("role_id"), "应保留role_id列");
        assertTrue(damengSql.contains("assigned_date"), "应保留assigned_date列");
        assertTrue(damengSql.contains("status"), "应保留status列");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留复合主键");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("'active'"), "应保留字符串默认值");
    }

    @Test
    @DisplayName("测试时间戳和默认值转换")
    void testTimestampAndDefaultValueConversion() {
        String mysqlSql = "CREATE TABLE timestamp_defaults (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, " +
                "status VARCHAR(20) DEFAULT 'pending', " +
                "count_value INT DEFAULT 0" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("时间戳和默认值转换结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证时间戳和默认值转换
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("timestamp_defaults"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证时间函数转换
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
        assertTrue(damengSql.contains("'pending'"), "应保留字符串默认值");
        assertTrue(damengSql.contains("DEFAULT 0"), "应保留数值默认值");
    }

    @Test
    @DisplayName("测试达梦特有核心特性")
    void testDamengSpecificCoreFeatures() {
        String mysqlSql = "CREATE TABLE dameng_core_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "oracle_number NUMBER(15,2), " +
                "oracle_varchar2 VARCHAR2(200), " +
                "mysql_text TEXT, " +
                "mysql_blob BLOB, " +
                "standard_decimal DECIMAL(10,2), " +
                "standard_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("达梦特有核心特性:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证达梦特有核心特性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("dameng_core_features"), "应保留表名");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        
        // 验证Oracle兼容类型支持
        assertTrue(damengSql.contains("NUMBER(15,2)"), "应支持Oracle NUMBER类型");
        assertTrue(damengSql.contains("VARCHAR2(200)"), "应支持Oracle VARCHAR2类型");
        
        // 验证MySQL类型转换
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        assertTrue(damengSql.contains("BLOB"), "应保留BLOB类型");
        
        // 验证标准类型支持
        assertTrue(damengSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
    }

    @Test
    @DisplayName("测试核心语句完整性")
    void testCoreStatementIntegrity() {
        String mysqlSql = "CREATE TABLE statement_integrity (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "name VARCHAR(100) NOT NULL UNIQUE, " +
                "description TEXT, " +
                "price DECIMAL(12,2) CHECK (price > 0), " +
                "category_id INT, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "INDEX idx_category (category_id), " +
                "FOREIGN KEY (category_id) REFERENCES categories(id)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String damengSql = generator.generate(createTable);

        System.out.println("核心语句完整性测试结果:");
        System.out.println(damengSql);

        // 根据达梦官方文档，验证核心语句完整性
        assertTrue(damengSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(damengSql.contains("statement_integrity"), "应保留表名");
        assertTrue(damengSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(damengSql.contains("IDENTITY"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(damengSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertTrue(damengSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(damengSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(damengSql.contains("CLOB") || damengSql.contains("TEXT"), 
                  "应转换TEXT为CLOB或保留TEXT");
        assertTrue(damengSql.contains("DECIMAL(12,2)"), "应保留DECIMAL类型");
        assertTrue(damengSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(damengSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证约束和索引处理
        assertTrue(damengSql.contains("CHECK") || damengSql.contains("price"), 
                  "应处理CHECK约束");
        assertTrue(damengSql.contains("idx_category") || damengSql.contains("category_id"), 
                  "应处理索引");
        assertTrue(damengSql.contains("FOREIGN KEY") || damengSql.contains("REFERENCES") || 
                  damengSql.contains("category_id"), "应处理外键");
        
        // 验证时间函数转换
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
                  "应转换时间函数");
    }
}
