package com.xylink.sqltranspiler.unit.dialects.shentong;

import com.xylink.sqltranspiler.shared.base.BaseConversionTest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 神通数据库16个SQL语句转换测试
 * 验证所有指定的SQL语句在神通数据库中的转换效果
 */
public class Shentong16SqlStatementsTest extends BaseConversionTest {

    @Test
    @DisplayName("1. ALTER TABLE ADD COLUMN - 神通验证")
    void testSql1() throws Exception {
        String sql = "alter table test.a add column user_name varchar(10) not null default '';";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertBasicConversionRequirements(shentongSql);
        assertTrue(shentongSql.contains("ALTER TABLE"), "神通应包含ALTER TABLE");
        assertTrue(shentongSql.contains("ADD COLUMN"), "神通应包含ADD COLUMN");
        assertTrue(shentongSql.contains("\"test\".\"a\""), "神通表名应使用双引号");
        assertTrue(shentongSql.contains("\"user_name\""), "神通列名应使用双引号");
    }

    @Test
    @DisplayName("2. ALTER TABLE MODIFY COLUMN - 神通验证")
    void testSql2() throws Exception {
        String sql = "alter table test.a modify column user_name varchar(20) not null default '10';";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertBasicConversionRequirements(shentongSql);
        assertTrue(shentongSql.contains("ALTER TABLE"), "神通应包含ALTER TABLE");
        assertTrue(shentongSql.contains("ALTER COLUMN") || shentongSql.contains("MODIFY COLUMN"), "神通应使用ALTER COLUMN或MODIFY COLUMN语法");
        assertTrue(shentongSql.contains("\"test\".\"a\""), "神通表名应使用双引号");
    }

    @Test
    @DisplayName("3. ALTER TABLE DROP COLUMN - 神通验证")
    void testSql3() throws Exception {
        String sql = "alter table test.a drop column user_name;";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertBasicConversionRequirements(shentongSql);
        assertTrue(shentongSql.contains("DROP COLUMN"), "神通应包含DROP COLUMN");
    }

    @Test
    @DisplayName("4. ALTER TABLE CHANGE COLUMN - 神通验证")
    void testSql4() throws Exception {
        String sql = "alter table test.a change column user_name user_name1 varchar(20) not null default '10';";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertBasicConversionRequirements(shentongSql);
        assertTrue(shentongSql.contains("ALTER TABLE"), "神通应包含ALTER TABLE");
    }

    @Test
    @DisplayName("5. ALTER TABLE MODIFY AUTO_INCREMENT - 神通验证")
    void testSql5() throws Exception {
        String sql = "alter table test.a modify column id int not null auto_increment;";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertBasicConversionRequirements(shentongSql);
        assertTrue(shentongSql.contains("ALTER TABLE"), "神通应包含ALTER TABLE");
        assertTrue(shentongSql.contains("\"id\""), "神通列名应使用双引号");
        assertTrue(shentongSql.contains("AUTO_INCREMENT") || shentongSql.contains("IDENTITY(1,1)"), "神通应支持AUTO_INCREMENT或IDENTITY(1,1)语法");
    }

    @Test
    @DisplayName("6. ALTER TABLE MODIFY 移除AUTO_INCREMENT - 神通验证")
    void testSql6() throws Exception {
        String sql = "alter table test.a modify column id int not null;";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertBasicConversionRequirements(shentongSql);
        assertTrue(shentongSql.contains("ALTER TABLE"), "神通应包含ALTER TABLE");
    }

    @Test
    @DisplayName("7. ALTER TABLE ADD PRIMARY KEY - 神通验证")
    void testSql7() throws Exception {
        String sql = "alter table test.a add primary key (id);";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertBasicConversionRequirements(shentongSql);
        assertTrue(shentongSql.contains("ADD PRIMARY KEY"), "神通应包含ADD PRIMARY KEY");
    }

    @Test
    @DisplayName("8. ALTER TABLE ADD UNIQUE - 神通验证")
    void testSql8() throws Exception {
        String sql = "alter table test.a add unique uniq_user_name(user_name);";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertBasicConversionRequirements(shentongSql);
        assertTrue(shentongSql.contains("ADD") && shentongSql.contains("UNIQUE"), "神通应包含ADD UNIQUE");
    }

    @Test
    @DisplayName("9. ALTER TABLE ADD INDEX - 神通验证")
    void testSql9() throws Exception {
        String sql = "alter table test.a add index idx_user_name (user_name);";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertBasicConversionRequirements(shentongSql);
        assertTrue(shentongSql.contains("ADD INDEX"), "神通应包含ADD INDEX");
    }

    @Test
    @DisplayName("10. ALTER TABLE DROP PRIMARY KEY - 神通验证")
    void testSql10() throws Exception {
        String sql = "alter table test.a drop primary key;";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertBasicConversionRequirements(shentongSql);
        assertTrue(shentongSql.contains("DROP PRIMARY KEY"), "神通应包含DROP PRIMARY KEY");
    }

    @Test
    @DisplayName("11-12. ALTER TABLE DROP INDEX - 神通验证")
    void testSql11And12() throws Exception {
        String sql1 = "alter table test.a drop index uniq_user_name;";
        String sql2 = "alter table test.a drop index idx_user_name;";
        
        String shentongSql1 = convertMySqlToShentong(sql1);
        assertBasicConversionRequirements(shentongSql1);
        assertTrue(shentongSql1.contains("DROP INDEX"), "神通应包含DROP INDEX");
        assertTrue(shentongSql1.contains("\"uniq_user_name\""), "神通索引名应使用双引号");
        
        String shentongSql2 = convertMySqlToShentong(sql2);
        assertBasicConversionRequirements(shentongSql2);
        assertTrue(shentongSql2.contains("DROP INDEX"), "神通应包含DROP INDEX");
        assertTrue(shentongSql2.contains("\"idx_user_name\""), "神通索引名应使用双引号");
    }

    @Test
    @DisplayName("13. MD5(UUID()) 函数 - 神通验证")
    void testSql13() throws Exception {
        String sql = "SELECT md5(uuid()) as hash_value;";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertNotNull(shentongSql, "神通转换结果不应为空");
        assertTrue(shentongSql.contains("SELECT"), "神通应包含SELECT");
    }

    @Test
    @DisplayName("14. SELECT type FROM 语法错误 - 神通验证")
    void testSql14() throws Exception {
        String sql = "select type from;";
        
        // 语法错误应该被适当处理，不应该导致程序崩溃
        try {
            String shentongSql = convertMySqlToShentong(sql);
            assertNotNull(shentongSql, "神通转换结果不应为空");
        } catch (Exception e) {
            assertNotNull(e.getMessage(), "异常应该有明确的错误信息");
        }
    }

    @Test
    @DisplayName("15. UNIX_TIMESTAMP(CURRENT_TIMESTAMP()) - 神通验证")
    void testSql15() throws Exception {
        String sql = "SELECT unix_timestamp(current_timestamp()) as timestamp_value;";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertNotNull(shentongSql, "神通转换结果不应为空");
        assertTrue(shentongSql.contains("SELECT"), "神通应包含SELECT");
    }

    @Test
    @DisplayName("16. FROM_UNIXTIME(1679564628) - 神通验证")
    void testSql16() throws Exception {
        String sql = "SELECT from_unixtime(1679564628) as datetime_value;";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertNotNull(shentongSql, "神通转换结果不应为空");
        assertTrue(shentongSql.contains("SELECT"), "神通应包含SELECT");
        assertTrue(shentongSql.contains("1679564628"), "神通应包含时间戳数值");
    }

    @Test
    @DisplayName("17. 复杂DELETE语句 - 神通验证")
    void testSql17() throws Exception {
        String sql = "DELETE FROM datareal.call_collection WHERE timestamp<unix_timestamp(DATE_ADD(now(),INTERVAL -3 day))*1000;";
        
        String shentongSql = convertMySqlToShentong(sql);
        assertNotNull(shentongSql, "神通转换结果不应为空");
        
        // 复杂DELETE语句可能转换失败，检查是否包含错误注释或正常转换
        if (shentongSql.contains("DELETE FROM")) {
            assertTrue(shentongSql.contains("WHERE"), "神通应包含WHERE子句");
            assertTrue(shentongSql.contains("1000"), "神通应包含乘数");
        } else {
            assertTrue(shentongSql.contains("--") || shentongSql.contains("CONVERSION ERROR"), 
                       "复杂语句转换失败时应包含错误注释");
        }
    }

    @Test
    @DisplayName("神通数据库特有转换验证")
    void testShentongSpecificConversions() throws Exception {
        String sql = """
            CREATE TABLE shentong_test (
                id INT AUTO_INCREMENT PRIMARY KEY,
                flag TINYINT(1) NOT NULL DEFAULT 0,
                medium_num MEDIUMINT,
                long_text LONGTEXT,
                birth_year YEAR,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;
        
        String shentongSql = convertMySqlToShentong(sql);
        assertBasicConversionRequirements(shentongSql);
        
        // 验证神通特有的转换
        assertTrue(shentongSql.contains("AUTO_INCREMENT") || shentongSql.contains("IDENTITY(1,1)"), "应支持AUTO_INCREMENT或IDENTITY(1,1)语法");
        assertTrue(shentongSql.contains("BIT"), "TINYINT(1)应转换为BIT");
        assertTrue(shentongSql.contains("INT"), "MEDIUMINT应转换为INT");
        assertTrue(shentongSql.contains("TEXT"), "LONGTEXT应转换为TEXT");
        assertTrue(shentongSql.contains("SMALLINT"), "YEAR应转换为SMALLINT");
        assertTrue(shentongSql.contains("CHARACTER SET UTF8"), "应使用CHARACTER SET UTF8");
        
        // 验证不包含MySQL特有语法（神通数据库支持AUTO_INCREMENT，所以不移除）
        assertFalse(shentongSql.contains("TINYINT(1)"), "不应包含TINYINT(1)");
        assertFalse(shentongSql.contains("MEDIUMINT"), "不应包含MEDIUMINT");
        assertFalse(shentongSql.contains("LONGTEXT"), "不应包含LONGTEXT");
        assertFalse(shentongSql.contains("YEAR"), "不应包含YEAR");
        assertFalse(shentongSql.contains("ENGINE="), "不应包含ENGINE");
        assertFalse(shentongSql.contains("utf8mb4"), "不应包含utf8mb4");
    }
}
