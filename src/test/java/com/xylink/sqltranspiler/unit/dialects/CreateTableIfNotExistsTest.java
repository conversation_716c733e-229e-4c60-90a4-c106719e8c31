package com.xylink.sqltranspiler.unit.dialects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * 测试CREATE TABLE IF NOT EXISTS语句的转换
 * 验证MySQL的IF NOT EXISTS子句在达梦和金仓数据库中的正确转换
 */
public class CreateTableIfNotExistsTest {

    private Transpiler transpiler;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }

    @Test
    void testCreateTableIfNotExistsToDameng() {
        String mysqlSql = """
            create table if not exists ainemo.t_config_data_temp
            (
                id          int primary key auto_increment,
                sub_type    int                  default null,
                series_id   int                  default null,
                config_code varchar(40) not null default '',
                platform    varchar(20) not null default '',
                type        varchar(10) not null default ''
            );
            """;

        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "dameng");

        assertTrue(result.failureCount() == 0, "转换应该成功");
        assertNotNull(result.translatedSql(), "转换后的SQL不应为空");

        String convertedSql = result.translatedSql();
        
        // 验证IF NOT EXISTS被保留
        assertTrue(convertedSql.contains("CREATE TABLE IF NOT EXISTS"), 
                   "达梦数据库应该保留IF NOT EXISTS子句");
        
        // 根据达梦官方文档，普通标识符不需要双引号
        // 验证表名正确转换
        assertTrue(convertedSql.contains("ainemo.t_config_data_temp"),
                   "表名应该正确转换为达梦格式（普通标识符不需要双引号）");
        
        // 验证AUTO_INCREMENT转换为IDENTITY
        assertTrue(convertedSql.contains("IDENTITY(1,1)"), 
                   "AUTO_INCREMENT应该转换为IDENTITY(1,1)");
        
        System.out.println("达梦转换结果:");
        System.out.println(convertedSql);
    }

    @Test
    void testCreateTableIfNotExistsToKingbase() {
        String mysqlSql = """
            create table if not exists ainemo.t_config_data_temp
            (
                id          int primary key auto_increment,
                sub_type    int                  default null,
                series_id   int                  default null,
                config_code varchar(40) not null default '',
                platform    varchar(20) not null default '',
                type        varchar(10) not null default ''
            );
            """;

        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "kingbase");

        assertTrue(result.failureCount() == 0, "转换应该成功");
        assertNotNull(result.translatedSql(), "转换后的SQL不应为空");

        String convertedSql = result.translatedSql();
        
        // 验证IF NOT EXISTS被保留
        assertTrue(convertedSql.contains("CREATE TABLE IF NOT EXISTS"), 
                   "金仓数据库应该保留IF NOT EXISTS子句");
        
        // 根据金仓数据库生成器的统一策略，所有标识符都使用双引号确保最大兼容性
        // 这符合PostgreSQL的标准，金仓数据库基于PostgreSQL
        // 验证表名正确转换
        assertTrue(convertedSql.contains("\"ainemo\".\"t_config_data_temp\""),
                   "表名应该正确转换为金仓格式（统一使用双引号策略）");
        
        // 验证AUTO_INCREMENT转换为SERIAL
        assertTrue(convertedSql.contains("SERIAL"), 
                   "AUTO_INCREMENT应该转换为SERIAL");
        
        System.out.println("金仓转换结果:");
        System.out.println(convertedSql);
    }

    @Test
    void testCreateTableWithoutIfNotExists() {
        String mysqlSql = """
            create table ainemo.t_config_data_temp
            (
                id          int primary key auto_increment,
                config_code varchar(40) not null default ''
            );
            """;

        // 测试达梦
        TranspilationResult damengResult = transpiler.transpile(mysqlSql, "mysql", "dameng");
        assertTrue(damengResult.failureCount() == 0);
        String damengSql = damengResult.translatedSql();
        assertFalse(damengSql.contains("IF NOT EXISTS"),
                    "没有IF NOT EXISTS的语句不应该添加此子句");
        assertTrue(damengSql.contains("CREATE TABLE ainemo.t_config_data_temp"),
                   "应该正确生成CREATE TABLE语句（普通标识符不需要双引号）");

        // 测试金仓
        TranspilationResult kingbaseResult = transpiler.transpile(mysqlSql, "mysql", "kingbase");
        assertTrue(kingbaseResult.failureCount() == 0);
        String kingbaseSql = kingbaseResult.translatedSql();
        assertFalse(kingbaseSql.contains("IF NOT EXISTS"),
                    "没有IF NOT EXISTS的语句不应该添加此子句");
        assertTrue(kingbaseSql.contains("CREATE TABLE \"ainemo\".\"t_config_data_temp\""),
                   "应该正确生成CREATE TABLE语句（金仓统一使用双引号策略）");
    }

    @Test
    void testMultipleCreateTableIfNotExists() {
        String mysqlSql = """
            create table if not exists test1 (id int);
            create table test2 (id int);
            create table if not exists test3 (id int);
            """;

        // 测试达梦
        TranspilationResult damengResult = transpiler.transpile(mysqlSql, "mysql", "dameng");
        assertTrue(damengResult.failureCount() == 0);
        String damengSql = damengResult.translatedSql();

        // 计算IF NOT EXISTS出现次数
        int ifNotExistsCount = damengSql.split("IF NOT EXISTS", -1).length - 1;
        assertEquals(2, ifNotExistsCount, "应该有2个IF NOT EXISTS子句");

        // 测试金仓
        TranspilationResult kingbaseResult = transpiler.transpile(mysqlSql, "mysql", "kingbase");
        assertTrue(kingbaseResult.failureCount() == 0);
        String kingbaseSql = kingbaseResult.translatedSql();

        // 计算IF NOT EXISTS出现次数
        ifNotExistsCount = kingbaseSql.split("IF NOT EXISTS", -1).length - 1;
        assertEquals(2, ifNotExistsCount, "应该有2个IF NOT EXISTS子句");
    }

    @Test
    void testDropTableIfExistsToDameng() {
        String mysqlSql = """
            drop table if exists ainemo.t_config_data_temp;
            """;

        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "dameng");

        assertTrue(result.failureCount() == 0, "转换应该成功");
        assertNotNull(result.translatedSql(), "转换后的SQL不应为空");

        String convertedSql = result.translatedSql();

        // 验证IF EXISTS被保留
        assertTrue(convertedSql.contains("DROP TABLE IF EXISTS"),
                   "达梦数据库应该保留IF EXISTS子句");

        // 根据达梦官方文档，普通标识符不需要双引号
        // 验证表名正确转换
        assertTrue(convertedSql.contains("ainemo.t_config_data_temp"),
                   "表名应该正确转换为达梦格式（普通标识符不需要双引号）");

        System.out.println("达梦DROP TABLE转换结果:");
        System.out.println(convertedSql);
    }

    @Test
    void testDropTableIfExistsToKingbase() {
        String mysqlSql = """
            drop table if exists ainemo.t_config_data_temp;
            """;

        TranspilationResult result = transpiler.transpile(mysqlSql, "mysql", "kingbase");

        assertTrue(result.failureCount() == 0, "转换应该成功");
        assertNotNull(result.translatedSql(), "转换后的SQL不应为空");

        String convertedSql = result.translatedSql();

        // 验证IF EXISTS被保留
        assertTrue(convertedSql.contains("DROP TABLE IF EXISTS"),
                   "金仓数据库应该保留IF EXISTS子句");

        // 根据金仓数据库生成器的统一策略，所有标识符都使用双引号确保最大兼容性
        // 验证表名正确转换
        assertTrue(convertedSql.contains("\"ainemo\".\"t_config_data_temp\""),
                   "表名应该正确转换为金仓格式（统一使用双引号策略）");

        System.out.println("金仓DROP TABLE转换结果:");
        System.out.println(convertedSql);
    }

    @Test
    void testDropTableWithoutIfExists() {
        String mysqlSql = """
            drop table ainemo.t_config_data_temp;
            """;

        // 测试达梦
        TranspilationResult damengResult = transpiler.transpile(mysqlSql, "mysql", "dameng");
        assertTrue(damengResult.failureCount() == 0);
        String damengSql = damengResult.translatedSql();
        assertFalse(damengSql.contains("IF EXISTS"),
                    "没有IF EXISTS的语句不应该添加此子句");
        assertTrue(damengSql.contains("DROP TABLE ainemo.t_config_data_temp"),
                   "应该正确生成DROP TABLE语句（普通标识符不需要双引号）");

        // 测试金仓
        TranspilationResult kingbaseResult = transpiler.transpile(mysqlSql, "mysql", "kingbase");
        assertTrue(kingbaseResult.failureCount() == 0);
        String kingbaseSql = kingbaseResult.translatedSql();
        assertFalse(kingbaseSql.contains("IF EXISTS"),
                    "没有IF EXISTS的语句不应该添加此子句");
        assertTrue(kingbaseSql.contains("DROP TABLE \"ainemo\".\"t_config_data_temp\""),
                   "应该正确生成DROP TABLE语句（金仓统一使用双引号策略）");
    }

    @Test
    void testMixedCreateAndDropStatements() {
        String mysqlSql = """
            create table if not exists test1 (id int);
            drop table if exists test2;
            create table test3 (id int);
            drop table test4;
            """;

        // 测试达梦
        TranspilationResult damengResult = transpiler.transpile(mysqlSql, "mysql", "dameng");
        assertTrue(damengResult.failureCount() == 0);
        String damengSql = damengResult.translatedSql();

        // 验证CREATE TABLE IF NOT EXISTS
        assertTrue(damengSql.contains("CREATE TABLE IF NOT EXISTS"),
                   "应该包含CREATE TABLE IF NOT EXISTS");
        // 验证DROP TABLE IF EXISTS
        assertTrue(damengSql.contains("DROP TABLE IF EXISTS"),
                   "应该包含DROP TABLE IF EXISTS");
        // 根据达梦官方文档，普通标识符不需要双引号
        // 验证普通CREATE TABLE
        assertTrue(damengSql.contains("CREATE TABLE test3"),
                   "应该包含普通CREATE TABLE（普通标识符不需要双引号）");
        // 验证普通DROP TABLE
        assertTrue(damengSql.contains("DROP TABLE test4"),
                   "应该包含普通DROP TABLE（普通标识符不需要双引号）");

        // 测试金仓
        TranspilationResult kingbaseResult = transpiler.transpile(mysqlSql, "mysql", "kingbase");
        assertTrue(kingbaseResult.failureCount() == 0);
        String kingbaseSql = kingbaseResult.translatedSql();

        // 验证CREATE TABLE IF NOT EXISTS
        assertTrue(kingbaseSql.contains("CREATE TABLE IF NOT EXISTS"),
                   "应该包含CREATE TABLE IF NOT EXISTS");
        // 验证DROP TABLE IF EXISTS
        assertTrue(kingbaseSql.contains("DROP TABLE IF EXISTS"),
                   "应该包含DROP TABLE IF EXISTS");
        // 根据金仓数据库生成器的统一策略，所有标识符都使用双引号确保最大兼容性
        // 验证普通CREATE TABLE
        assertTrue(kingbaseSql.contains("CREATE TABLE \"test3\""),
                   "应该包含普通CREATE TABLE（金仓统一使用双引号策略）");
        // 验证普通DROP TABLE
        assertTrue(kingbaseSql.contains("DROP TABLE \"test4\""),
                   "应该包含普通DROP TABLE（金仓统一使用双引号策略）");
    }
}
