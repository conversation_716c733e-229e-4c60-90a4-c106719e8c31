package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 金仓数据库数据类型转换测试
 * 基于金仓官方文档的测试驱动开发
 * 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 */
public class KingbaseDataTypeTest {

    private KingbaseGenerator generator;

    @BeforeEach
    public void setUp() {
        generator = new KingbaseGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试整数类型转换 - PostgreSQL兼容")
    public void testIntegerTypeConversion() {
        String sql = "CREATE TABLE test_integers (" +
                    "tiny_col TINYINT, " +
                    "small_col SMALLINT, " +
                    "int_col INT, " +
                    "big_col BIGINT" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"test_integers\""));
        assertTrue(result.contains("\"tiny_col\" TINYINT")); // TINYINT保持原样
        assertTrue(result.contains("\"small_col\" SMALLINT"));
        assertTrue(result.contains("\"int_col\" INT")); // INT保持原样
        assertTrue(result.contains("\"big_col\" BIGINT"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试布尔类型转换 - PostgreSQL兼容")
    public void testBooleanTypeConversion() {
        String sql = "CREATE TABLE test_boolean (" +
                    "is_active TINYINT(1), " +
                    "is_deleted BOOLEAN" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"test_boolean\""));
        assertTrue(result.contains("\"is_active\" BOOLEAN")); // TINYINT(1)转换为BOOLEAN
        assertTrue(result.contains("\"is_deleted\" BOOLEAN"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试字符串类型转换 - PostgreSQL兼容")
    public void testStringTypeConversion() {
        String sql = "CREATE TABLE test_strings (" +
                    "char_col CHAR(10), " +
                    "varchar_col VARCHAR(255), " +
                    "text_col TEXT" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"test_strings\""));
        assertTrue(result.contains("\"char_col\" CHAR(10)"));
        assertTrue(result.contains("\"varchar_col\" VARCHAR(255)"));
        assertTrue(result.contains("\"text_col\" TEXT"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试数值类型转换 - PostgreSQL兼容")
    public void testNumericTypeConversion() {
        String sql = "CREATE TABLE test_numeric (" +
                    "decimal_col DECIMAL(10,2), " +
                    "float_col FLOAT, " +
                    "double_col DOUBLE" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"test_numeric\""));
        assertTrue(result.contains("\"decimal_col\" DECIMAL(10,2)"));
        assertTrue(result.contains("\"float_col\" FLOAT")); // FLOAT保持原样
        assertTrue(result.contains("\"double_col\" DOUBLE PRECISION")); // DOUBLE转换为DOUBLE PRECISION
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试日期时间类型转换 - PostgreSQL兼容")
    public void testDateTimeTypeConversion() {
        String sql = "CREATE TABLE test_datetime (" +
                    "date_col DATE, " +
                    "time_col TIME, " +
                    "datetime_col DATETIME, " +
                    "timestamp_col TIMESTAMP" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"test_datetime\""));
        assertTrue(result.contains("\"date_col\" DATE"));
        assertTrue(result.contains("\"time_col\" TIME"));
        assertTrue(result.contains("\"datetime_col\" DATETIME")); // DATETIME保持原样
        assertTrue(result.contains("\"timestamp_col\" TIMESTAMP"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试AUTO_INCREMENT转换为SERIAL - PostgreSQL兼容")
    public void testAutoIncrementConversion() {
        String sql = "CREATE TABLE test_auto_increment (" +
                    "id INT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100)" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"test_auto_increment\""));
        assertTrue(result.contains("\"id\" SERIAL")); // AUTO_INCREMENT转换为SERIAL
        assertTrue(result.contains("PRIMARY KEY"));
        assertTrue(result.contains("\"name\" VARCHAR(100)"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试金仓数据库MySQL兼容特性")
    public void testKingbaseMySQLCompatibility() {
        // 根据金仓官方文档，金仓数据库支持MySQL兼容的数据类型
        String sql = "CREATE TABLE mysql_compat (" +
                    "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "name VARCHAR(100) NOT NULL, " +
                    "is_active TINYINT(1) DEFAULT 1, " +
                    "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"mysql_compat\""));
        assertTrue(result.contains("\"id\" BIGSERIAL")); // BIGINT AUTO_INCREMENT转换为BIGSERIAL
        assertTrue(result.contains("\"name\" VARCHAR(100) NOT NULL"));
        assertTrue(result.contains("\"is_active\" BOOLEAN")); // TINYINT(1)转换为BOOLEAN
        assertTrue(result.contains("\"created_at\" TIMESTAMP"));
        assertTrue(result.contains("PRIMARY KEY"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试金仓数据库官方文档示例")
    public void testKingbaseOfficialExample() {
        // 根据金仓官方文档的数据类型映射示例
        String sql = "CREATE TABLE employee_info (" +
                    "emp_id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                    "emp_name VARCHAR(100) NOT NULL, " +
                    "is_active BOOLEAN DEFAULT TRUE, " +
                    "salary DECIMAL(10,2), " +
                    "hire_date DATE" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);
        
        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"employee_info\""));
        assertTrue(result.contains("\"emp_id\" BIGSERIAL"));
        assertTrue(result.contains("\"emp_name\" VARCHAR(100) NOT NULL"));
        assertTrue(result.contains("\"is_active\" BOOLEAN"));
        assertTrue(result.contains("\"salary\" DECIMAL(10,2)"));
        assertTrue(result.contains("\"hire_date\" DATE"));
        assertTrue(result.contains("PRIMARY KEY"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试UNSIGNED类型处理")
    public void testUnsignedTypeHandling() {
        String sql = "CREATE TABLE test_unsigned (" +
                    "tinyint_unsigned TINYINT UNSIGNED, " +
                    "smallint_unsigned SMALLINT UNSIGNED, " +
                    "int_unsigned INT UNSIGNED, " +
                    "bigint_unsigned BIGINT UNSIGNED" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        System.out.println("UNSIGNED类型转换结果:");
        System.out.println(result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"test_unsigned\""));
        // 根据金仓官方文档表97第6行，金仓数据库支持UNSIGNED类型，应该保留
        assertTrue(result.contains("\"tinyint_unsigned\" TINYINT UNSIGNED"));
        assertTrue(result.contains("\"smallint_unsigned\" SMALLINT UNSIGNED"));
        assertTrue(result.contains("\"int_unsigned\" INT UNSIGNED"));
        assertTrue(result.contains("\"bigint_unsigned\" BIGINT UNSIGNED"));
        // 应该包含UNSIGNED关键字，符合金仓官方文档
        assertTrue(result.contains("UNSIGNED"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试JSON和空间数据类型支持")
    public void testJsonAndSpatialTypes() {
        String sql = "CREATE TABLE test_json_spatial (" +
                    "json_col JSON, " +
                    "geometry_col GEOMETRY, " +
                    "point_col POINT, " +
                    "linestring_col LINESTRING, " +
                    "polygon_col POLYGON" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        System.out.println("JSON和空间数据类型转换结果:");
        System.out.println(result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"test_json_spatial\""));
        assertTrue(result.contains("\"json_col\" JSON"));
        assertTrue(result.contains("\"geometry_col\" GEOMETRY"));
        assertTrue(result.contains("\"point_col\" POINT"));
        assertTrue(result.contains("\"linestring_col\" LINESTRING"));
        assertTrue(result.contains("\"polygon_col\" POLYGON"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试ENUM和SET类型支持")
    public void testEnumAndSetTypes() {
        String sql = "CREATE TABLE test_enum_set (" +
                    "status ENUM('active', 'inactive', 'pending'), " +
                    "permissions SET('read', 'write', 'execute')" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        System.out.println("ENUM和SET类型转换结果:");
        System.out.println(result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"test_enum_set\""));
        assertTrue(result.contains("\"status\" ENUM"));
        assertTrue(result.contains("\"permissions\" SET"));
        assertTrue(result.contains("'active'"));
        assertTrue(result.contains("'read'"));
        assertTrue(result.endsWith(";"));
    }

    @Test
    @DisplayName("测试二进制数据类型支持")
    public void testBinaryTypes() {
        String sql = "CREATE TABLE test_binary (" +
                    "binary_col BINARY(16), " +
                    "varbinary_col VARBINARY(255), " +
                    "tinyblob_col TINYBLOB, " +
                    "blob_col BLOB, " +
                    "mediumblob_col MEDIUMBLOB, " +
                    "longblob_col LONGBLOB" +
                    ");";
        CreateTable createTable = parseCreateTable(sql);
        String result = generator.generate(createTable);

        System.out.println("二进制数据类型转换结果:");
        System.out.println(result);

        assertTrue(result.contains("CREATE TABLE"));
        assertTrue(result.contains("\"test_binary\""));
        assertTrue(result.contains("\"binary_col\" BINARY(16)"));
        assertTrue(result.contains("\"varbinary_col\" VARBINARY(255)"));
        assertTrue(result.contains("\"tinyblob_col\" TINYBLOB"));
        assertTrue(result.contains("\"blob_col\" BLOB"));
        assertTrue(result.contains("\"mediumblob_col\" MEDIUMBLOB"));
        assertTrue(result.contains("\"longblob_col\" LONGBLOB"));
        assertTrue(result.endsWith(";"));
    }
}
