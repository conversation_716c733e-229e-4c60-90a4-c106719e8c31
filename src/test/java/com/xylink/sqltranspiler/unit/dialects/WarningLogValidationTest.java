package com.xylink.sqltranspiler.unit.dialects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;
import java.util.stream.Collectors;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.core.dialects.shentong.ShentongGenerator;

import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.read.ListAppender;

/**
 * 验证各数据库转译器在处理不支持功能时正确记录警告日志
 * 
 * 测试重点：
 * 1. 警告日志的格式和内容
 * 2. 日志级别的正确性
 * 3. 原始SQL和转换结果的记录
 * 4. 不同数据库的特定警告信息
 * 
 * 根据官方文档要求：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: 官方文档规范
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@DisplayName("警告日志验证测试")
public class WarningLogValidationTest {

    private Transpiler transpiler;
    private ListAppender<ILoggingEvent> logAppender;
    private Logger damengLogger;
    private Logger kingbaseLogger;
    private Logger shentongLogger;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
        
        // 设置日志捕获器
        logAppender = new ListAppender<>();
        logAppender.start();
        
        // 为各数据库的Generator添加日志捕获
        damengLogger = (Logger) LoggerFactory.getLogger(DamengGenerator.class);
        kingbaseLogger = (Logger) LoggerFactory.getLogger(KingbaseGenerator.class);
        shentongLogger = (Logger) LoggerFactory.getLogger(ShentongGenerator.class);
        
        damengLogger.addAppender(logAppender);
        kingbaseLogger.addAppender(logAppender);
        shentongLogger.addAppender(logAppender);
    }

    @Test
    @DisplayName("达梦数据库：验证UNSUPPORTED_FEATURE警告日志格式")
    void testDamengUnsupportedFeatureWarningFormat() {
        String inputSql = "CREATE TABLE test (\n" +
                "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n" +
                ");";

        logAppender.list.clear();
        transpiler.transpile(inputSql, "mysql", "dameng");

        // 查找ON UPDATE相关的警告日志
        List<ILoggingEvent> warnings = logAppender.list.stream()
                .filter(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("UNSUPPORTED_FEATURE"))
                .collect(Collectors.toList());

        assertFalse(warnings.isEmpty(), "应该有UNSUPPORTED_FEATURE警告日志");

        ILoggingEvent warning = warnings.get(0);
        String message = warning.getFormattedMessage();

        // 验证警告日志格式
        assertTrue(message.startsWith("UNSUPPORTED_FEATURE:"), 
                "警告日志应以UNSUPPORTED_FEATURE:开头");
        assertTrue(message.contains("ON UPDATE clause"), 
                "应包含具体的不支持功能描述");
        assertTrue(message.contains("DM database"), 
                "应明确指出是达梦数据库");
        assertTrue(message.contains("Original expression:"), 
                "应包含原始表达式");
        assertTrue(message.contains("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"), 
                "应包含完整的原始表达式");
        assertTrue(message.contains("Converting to SYSDATE only"), 
                "应说明转换结果");
    }

    @Test
    @DisplayName("金仓数据库：验证UNSUPPORTED_FEATURE警告日志格式")
    void testKingbaseUnsupportedFeatureWarningFormat() {
        String inputSql = "CREATE TABLE test (\n" +
                "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n" +
                ");";

        logAppender.list.clear();
        transpiler.transpile(inputSql, "mysql", "kingbase");

        // 查找ON UPDATE相关的警告日志
        List<ILoggingEvent> warnings = logAppender.list.stream()
                .filter(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("UNSUPPORTED_FEATURE"))
                .collect(Collectors.toList());

        assertFalse(warnings.isEmpty(), "应该有UNSUPPORTED_FEATURE警告日志");

        ILoggingEvent warning = warnings.get(0);
        String message = warning.getFormattedMessage();

        // 验证警告日志格式
        assertTrue(message.startsWith("UNSUPPORTED_FEATURE:"), 
                "警告日志应以UNSUPPORTED_FEATURE:开头");
        assertTrue(message.contains("ON UPDATE clause"), 
                "应包含具体的不支持功能描述");
        assertTrue(message.contains("KingbaseES"), 
                "应明确指出是金仓数据库");
        assertTrue(message.contains("Original expression:"), 
                "应包含原始表达式");
        assertTrue(message.contains("Converting to:"), 
                "应说明转换结果");
    }

    @Test
    @DisplayName("达梦数据库：验证SET语句警告日志的详细信息")
    void testDamengSetStatementWarningDetails() {
        String inputSql = "SET foreign_key_checks = 0;\n" +
                "SET names utf8mb4;\n" +
                "SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';";

        logAppender.list.clear();
        transpiler.transpile(inputSql, "mysql", "dameng");

        // 验证每个SET语句都有对应的警告
        List<ILoggingEvent> warnings = logAppender.list.stream()
                .filter(event -> event.getLevel().toString().equals("WARN"))
                .collect(Collectors.toList());

        assertEquals(3, warnings.size(), "应该有3个警告日志");

        // 验证SET FOREIGN_KEY_CHECKS警告
        boolean foundForeignKeyWarning = warnings.stream()
                .anyMatch(event -> {
                    String msg = event.getFormattedMessage();
                    return msg.contains("SET FOREIGN_KEY_CHECKS") &&
                           msg.contains("DM database") &&
                           msg.contains("Original SQL: SET foreign_key_checks = 0");
                });
        assertTrue(foundForeignKeyWarning, "应包含SET FOREIGN_KEY_CHECKS的详细警告");

        // 验证SET NAMES警告
        boolean foundNamesWarning = warnings.stream()
                .anyMatch(event -> {
                    String msg = event.getFormattedMessage();
                    return msg.contains("SET NAMES") &&
                           msg.contains("DM database") &&
                           msg.contains("Original SQL: SET names utf8mb4");
                });
        assertTrue(foundNamesWarning, "应包含SET NAMES的详细警告");

        // 验证SET SQL_MODE警告
        boolean foundSqlModeWarning = warnings.stream()
                .anyMatch(event -> {
                    String msg = event.getFormattedMessage();
                    return msg.contains("SET SQL_MODE") &&
                           msg.contains("DM database") &&
                           msg.contains("Original SQL: SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO'");
                });
        assertTrue(foundSqlModeWarning, "应包含SET SQL_MODE的详细警告");
    }

    @Test
    @DisplayName("神通数据库：验证支持的功能不产生警告")
    void testShentongSupportedFeaturesNoWarning() {
        String inputSql = "CREATE TABLE test (\n" +
                "    id INT AUTO_INCREMENT PRIMARY KEY,\n" +
                "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n" +
                ");";

        logAppender.list.clear();
        transpiler.transpile(inputSql, "mysql", "shentong");

        // 验证没有ON UPDATE相关的警告（神通数据库支持此功能）
        List<ILoggingEvent> onUpdateWarnings = logAppender.list.stream()
                .filter(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("ON UPDATE"))
                .collect(Collectors.toList());

        assertTrue(onUpdateWarnings.isEmpty(), 
                "神通数据库支持ON UPDATE语法，不应产生相关警告");

        // 验证没有AUTO_INCREMENT相关的警告（神通数据库支持此功能）
        List<ILoggingEvent> autoIncrementWarnings = logAppender.list.stream()
                .filter(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("AUTO_INCREMENT"))
                .collect(Collectors.toList());

        assertTrue(autoIncrementWarnings.isEmpty(), 
                "神通数据库支持AUTO_INCREMENT语法，不应产生相关警告");
    }

    @Test
    @DisplayName("验证警告日志的日志级别正确性")
    void testWarningLogLevels() {
        String inputSql = "SET foreign_key_checks = 0;\n" +
                "CREATE TABLE test (\n" +
                "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n" +
                ");";

        logAppender.list.clear();
        transpiler.transpile(inputSql, "mysql", "dameng");

        // 验证所有不支持功能的日志都是WARN级别
        List<ILoggingEvent> unsupportedFeatureLogs = logAppender.list.stream()
                .filter(event -> event.getFormattedMessage().contains("UNSUPPORTED_FEATURE"))
                .collect(Collectors.toList());

        assertFalse(unsupportedFeatureLogs.isEmpty(), "应该有不支持功能的日志");

        for (ILoggingEvent log : unsupportedFeatureLogs) {
            assertEquals("WARN", log.getLevel().toString(), 
                    "不支持功能的日志应该是WARN级别");
        }

        // 验证成功转换的日志不是WARN级别
        List<ILoggingEvent> successLogs = logAppender.list.stream()
                .filter(event -> event.getFormattedMessage().contains("CONVERSION_SUCCESS") ||
                        event.getFormattedMessage().contains("transpilation SUCCEEDED"))
                .collect(Collectors.toList());

        for (ILoggingEvent log : successLogs) {
            assertNotEquals("WARN", log.getLevel().toString(), 
                    "成功转换的日志不应该是WARN级别");
        }
    }

    @Test
    @DisplayName("验证复杂场景下的警告日志完整性")
    void testComplexScenarioWarningCompleteness() {
        String inputSql = "SET foreign_key_checks = 0;\n" +
                "SET names utf8mb4;\n" +
                "CREATE TABLE test1 (\n" +
                "    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n" +
                ");\n" +
                "CREATE TABLE test2 (\n" +
                "    modified_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n" +
                ");";

        logAppender.list.clear();
        transpiler.transpile(inputSql, "mysql", "dameng");

        // 统计各类警告
        List<ILoggingEvent> allWarnings = logAppender.list.stream()
                .filter(event -> event.getLevel().toString().equals("WARN"))
                .collect(Collectors.toList());

        // 应该有：2个SET语句警告 + 2个ON UPDATE警告 = 4个警告
        assertEquals(4, allWarnings.size(), "复杂场景应产生正确数量的警告");

        // 验证SET语句警告
        long setWarnings = allWarnings.stream()
                .filter(event -> event.getFormattedMessage().contains("SET") &&
                        (event.getFormattedMessage().contains("FOREIGN_KEY_CHECKS") ||
                         event.getFormattedMessage().contains("NAMES")))
                .count();
        assertEquals(2, setWarnings, "应有2个SET语句警告");

        // 验证ON UPDATE警告
        long onUpdateWarnings = allWarnings.stream()
                .filter(event -> event.getFormattedMessage().contains("ON UPDATE clause"))
                .count();
        assertEquals(2, onUpdateWarnings, "应有2个ON UPDATE警告");
    }

    @Test
    @DisplayName("验证警告日志中的特殊字符处理")
    void testWarningLogSpecialCharacterHandling() {
        String inputSql = "SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO';";

        logAppender.list.clear();
        transpiler.transpile(inputSql, "mysql", "dameng");

        List<ILoggingEvent> warnings = logAppender.list.stream()
                .filter(event -> event.getLevel().toString().equals("WARN") &&
                        event.getFormattedMessage().contains("SET SQL_MODE"))
                .collect(Collectors.toList());

        assertFalse(warnings.isEmpty(), "应该有SET SQL_MODE警告");

        String message = warnings.get(0).getFormattedMessage();
        assertTrue(message.contains("STRICT_TRANS_TABLES,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO"), 
                "警告日志应正确包含特殊字符和逗号");
    }
}
