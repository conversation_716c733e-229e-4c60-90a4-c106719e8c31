package com.xylink.sqltranspiler.unit.dialects.kingbase;

import com.xylink.sqltranspiler.core.dialects.kingbase.KingbaseGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 金仓数据库二进制数据测试
 * 
 * 根据金仓官方文档，测试MySQL二进制数据类型到金仓的转换
 * 参考：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 */
@DisplayName("金仓数据库二进制数据测试")
public class KingbaseBinaryDataTest {

    private KingbaseGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new KingbaseGenerator();
    }

    private CreateTable parseCreateTable(String sql) {
        return (CreateTable) MySqlHelper.parseStatement(sql);
    }

    @Test
    @DisplayName("测试基本二进制数据类型转换")
    void testBasicBinaryDataTypes() {
        String mysqlSql = "CREATE TABLE binary_basic (" +
                "id INT PRIMARY KEY, " +
                "binary_data BINARY(16), " +
                "varbinary_data VARBINARY(255), " +
                "bit_data BIT(8)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("基本二进制数据类型转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证二进制数据类型转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"binary_basic\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"binary_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"varbinary_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"bit_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("BINARY(16)"), "应保留BINARY类型");
        assertTrue(kingbaseSql.contains("VARBINARY(255)"), "应保留VARBINARY类型");
        assertTrue(kingbaseSql.contains("BIT(8)"), "应保留BIT类型");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试BLOB数据类型转换")
    void testBlobDataTypes() {
        String mysqlSql = "CREATE TABLE blob_types (" +
                "id INT PRIMARY KEY, " +
                "tiny_blob TINYBLOB, " +
                "blob_data BLOB, " +
                "medium_blob MEDIUMBLOB, " +
                "long_blob LONGBLOB" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("BLOB数据类型转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证BLOB类型转换
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"blob_types\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"tiny_blob\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"blob_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"medium_blob\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"long_blob\""), "列名应使用双引号");
        
        // 验证BLOB类型处理（金仓可能转换为BYTEA或保留BLOB）
        assertTrue(kingbaseSql.contains("TINYBLOB") || kingbaseSql.contains("BYTEA") || 
                  kingbaseSql.contains("BLOB"), "应转换或保留BLOB类型");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试二进制数据与约束的组合")
    void testBinaryDataWithConstraints() {
        String mysqlSql = "CREATE TABLE binary_constraints (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "file_hash BINARY(32) NOT NULL UNIQUE, " +
                "file_data LONGBLOB, " +
                "file_size BIGINT NOT NULL, " +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("二进制数据与约束组合转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证二进制数据与约束的组合
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"binary_constraints\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"file_hash\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"file_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"file_size\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"created_at\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("BINARY(32)"), "应保留BINARY类型");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(kingbaseSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试二进制数据与索引的组合")
    void testBinaryDataWithIndexes() {
        String mysqlSql = "CREATE TABLE binary_indexes (" +
                "id INT PRIMARY KEY, " +
                "checksum BINARY(16) NOT NULL, " +
                "content MEDIUMBLOB, " +
                "metadata VARBINARY(1024), " +
                "INDEX idx_checksum (checksum)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("二进制数据与索引组合转换结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证二进制数据与索引的组合
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"binary_indexes\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"checksum\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"content\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"metadata\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("BINARY(16)"), "应保留BINARY类型");
        assertTrue(kingbaseSql.contains("VARBINARY(1024)"), "应保留VARBINARY类型");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        
        // 验证索引处理（金仓可能在单独的语句中处理索引）
        assertTrue(kingbaseSql.contains("idx_checksum") || kingbaseSql.contains("checksum"), 
                  "应处理checksum索引");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试大型二进制数据处理")
    void testLargeBinaryData() {
        String mysqlSql = "CREATE TABLE large_binary (" +
                "id BIGINT AUTO_INCREMENT PRIMARY KEY, " +
                "document LONGBLOB, " +
                "thumbnail MEDIUMBLOB, " +
                "signature VARBINARY(512), " +
                "document_size BIGINT, " +
                "mime_type VARCHAR(100)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("大型二进制数据处理结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证大型二进制数据处理
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"large_binary\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("BIGSERIAL") || kingbaseSql.contains("BIGINT"), 
                  "应转换BIGINT AUTO_INCREMENT为BIGSERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"document\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"thumbnail\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"signature\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"document_size\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"mime_type\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("VARBINARY(512)"), "应保留VARBINARY类型");
        assertTrue(kingbaseSql.contains("VARCHAR(100)"), "应保留VARCHAR类型");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试二进制数据的默认值处理")
    void testBinaryDataDefaultValues() {
        String mysqlSql = "CREATE TABLE binary_defaults (" +
                "id INT PRIMARY KEY, " +
                "status_flag BIT(1) DEFAULT b'0', " +
                "empty_binary BINARY(8) DEFAULT 0x0000000000000000, " +
                "config_data VARBINARY(256)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("二进制数据默认值处理结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证二进制数据默认值处理
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"binary_defaults\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"status_flag\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"empty_binary\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"config_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("BIT(1)"), "应保留BIT类型");
        assertTrue(kingbaseSql.contains("BINARY(8)"), "应保留BINARY类型");
        assertTrue(kingbaseSql.contains("VARBINARY(256)"), "应保留VARBINARY类型");
        
        // 验证默认值处理（金仓可能有不同的二进制字面量语法）
        assertTrue(kingbaseSql.contains("DEFAULT") || kingbaseSql.contains("status_flag"), 
                  "应处理默认值");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试二进制数据在分区表中的应用")
    void testBinaryDataInPartitionedTable() {
        String mysqlSql = "CREATE TABLE binary_partitioned (" +
                "id BIGINT AUTO_INCREMENT, " +
                "partition_key DATE NOT NULL, " +
                "file_data LONGBLOB, " +
                "file_hash BINARY(32), " +
                "PRIMARY KEY (id, partition_key)" +
                ") PARTITION BY RANGE (YEAR(partition_key)) (" +
                "PARTITION p2020 VALUES LESS THAN (2021), " +
                "PARTITION p2021 VALUES LESS THAN (2022), " +
                "PARTITION p2022 VALUES LESS THAN (2023)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("分区表中二进制数据应用结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证分区表中的二进制数据应用
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"binary_partitioned\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("BIGSERIAL") || kingbaseSql.contains("BIGINT"), 
                  "应转换BIGINT AUTO_INCREMENT为BIGSERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"partition_key\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"file_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"file_hash\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("DATE"), "应保留DATE类型");
        assertTrue(kingbaseSql.contains("BINARY(32)"), "应保留BINARY类型");
        
        // 验证分区处理（金仓支持PostgreSQL兼容的分区）
        assertTrue(kingbaseSql.contains("PARTITION") || kingbaseSql.contains("binary_partitioned"), 
                  "应处理分区定义");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试金仓特有的二进制数据特性")
    void testKingbaseSpecificBinaryFeatures() {
        String mysqlSql = "CREATE TABLE kingbase_binary_features (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "binary_data LONGBLOB, " +
                "text_data LONGTEXT, " +
                "json_data JSON, " +
                "timestamp_data TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("金仓特有二进制数据特性:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证金仓特有的二进制数据特性
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"kingbase_binary_features\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"),
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"binary_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"text_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"json_data\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"timestamp_data\""), "列名应使用双引号");

        // 验证数据类型处理
        assertTrue(kingbaseSql.contains("LONGBLOB") || kingbaseSql.contains("BYTEA"),
                  "应处理大型二进制类型");
        assertTrue(kingbaseSql.contains("LONGTEXT") || kingbaseSql.contains("TEXT"),
                  "应处理大型文本类型");
        assertTrue(kingbaseSql.contains("JSON"), "应保留JSON类型");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试二进制数据的完整性约束")
    void testBinaryDataIntegrityConstraints() {
        String mysqlSql = "CREATE TABLE binary_integrity (" +
                "id INT AUTO_INCREMENT PRIMARY KEY, " +
                "file_content LONGBLOB NOT NULL, " +
                "checksum BINARY(32) NOT NULL UNIQUE, " +
                "file_size BIGINT NOT NULL CHECK (file_size > 0), " +
                "upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
                "CONSTRAINT chk_content_size CHECK (LENGTH(file_content) = file_size)" +
                ");";

        CreateTable createTable = parseCreateTable(mysqlSql);
        String kingbaseSql = generator.generate(createTable);

        System.out.println("二进制数据完整性约束结果:");
        System.out.println(kingbaseSql);

        // 根据金仓官方文档，验证二进制数据的完整性约束
        assertTrue(kingbaseSql.contains("CREATE TABLE"), "应保留CREATE TABLE");
        assertTrue(kingbaseSql.contains("\"binary_integrity\""), "表名应使用双引号");
        assertTrue(kingbaseSql.contains("SERIAL") || kingbaseSql.contains("AUTO_INCREMENT"), 
                  "应转换AUTO_INCREMENT为SERIAL");
        assertTrue(kingbaseSql.contains("PRIMARY KEY"), "应保留主键");
        assertTrue(kingbaseSql.contains("\"file_content\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"checksum\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"file_size\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("\"upload_time\""), "列名应使用双引号");
        assertTrue(kingbaseSql.contains("BINARY(32)"), "应保留BINARY类型");
        assertTrue(kingbaseSql.contains("BIGINT"), "应保留BIGINT类型");
        assertTrue(kingbaseSql.contains("NOT NULL"), "应保留NOT NULL约束");
        assertTrue(kingbaseSql.contains("UNIQUE"), "应保留UNIQUE约束");
        assertTrue(kingbaseSql.contains("TIMESTAMP"), "应保留TIMESTAMP类型");
        assertTrue(kingbaseSql.contains("DEFAULT"), "应保留默认值");
        
        // 验证约束处理（金仓可能在单独的语句中处理复杂约束）
        assertTrue(kingbaseSql.contains("CHECK") || kingbaseSql.contains("file_size"), 
                  "应处理CHECK约束");
        assertFalse(kingbaseSql.contains("`"), "不应包含反引号");
    }
}
