package com.xylink.sqltranspiler.unit.infrastructure.parser.error;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.infrastructure.parser.error.ErrorPatternRegistry;
import com.xylink.sqltranspiler.infrastructure.parser.error.MySqlSyntaxValidationResult;
import com.xylink.sqltranspiler.infrastructure.parser.error.MySqlSyntaxViolation;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorType;

/**
 * 强制性MySQL语法验证测试
 * 根据 .augment/rules/rule-db.md 要求，严格遵循MySQL官方规范
 * 
 * <AUTHOR> Transpiler Team
 */
public class ForcedMySqlSyntaxValidationTest {

    private static final Logger log = LoggerFactory.getLogger(ForcedMySqlSyntaxValidationTest.class);

    @Test
    @DisplayName("测试有效的MySQL语法应该通过验证")
    public void testValidMySqlSyntax() {
        // 测试标准的MySQL语法
        String validSql = "SELECT id, name FROM users WHERE age > 18 ORDER BY name LIMIT 10;";

        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(validSql);

        // 调试输出
        log.info("测试SQL: {}", validSql);
        log.info("验证结果: valid={}, violations={}", result.isValid(), result.getViolationCount());
        if (result.hasViolations()) {
            for (MySqlSyntaxViolation violation : result.getViolations()) {
                log.warn("意外的违规: {} - {}", violation.getPatternId(), violation.getFriendlyMessage());
            }
        }

        assertTrue(result.isValid(), "标准MySQL语法应该通过验证");
        assertFalse(result.hasViolations(), "不应该有语法违规");
        assertEquals(0, result.getViolationCount(), "违规数量应该为0");

        log.info("有效MySQL语法验证通过: {}", validSql);
    }

    @Test
    @DisplayName("测试PostgreSQL的random()函数应该被拒绝")
    public void testPostgreSqlRandomFunction() {
        String invalidSql = "SELECT random() FROM users;";
        
        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(invalidSql);
        
        assertFalse(result.isValid(), "包含random()函数的SQL应该被拒绝");
        assertTrue(result.hasViolations(), "应该有语法违规");
        assertEquals(1, result.getViolationCount(), "应该有1个违规");
        
        MySqlSyntaxViolation violation = result.getFirstViolation();
        assertNotNull(violation, "应该有违规记录");
        assertEquals("non_mysql_random_function", violation.getPatternId(), "应该匹配random函数模式");
        assertEquals(SqlErrorType.POSTGRESQL_SYNTAX_ERROR, violation.getErrorType(), "应该是PostgreSQL语法错误");
        assertTrue(violation.getFriendlyMessage().contains("不支持 random() 函数"), "错误消息应该明确指出不支持random函数");
        assertTrue(violation.getDetailedMessage().contains("MySQL 8.4官方文档"), "应该引用MySQL官方文档");
        assertTrue(violation.getSuggestion().contains("RAND()"), "应该建议使用RAND函数");
        
        log.info("PostgreSQL random()函数被正确拒绝: {}", violation.getFriendlyMessage());
    }

    @Test
    @DisplayName("测试PostgreSQL的类型转换语法应该被拒绝")
    public void testPostgreSqlTypeCast() {
        String invalidSql = "SELECT id::text FROM users;";
        
        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(invalidSql);
        
        assertFalse(result.isValid(), "包含::类型转换的SQL应该被拒绝");
        assertTrue(result.hasViolations(), "应该有语法违规");
        
        MySqlSyntaxViolation violation = result.getFirstViolation();
        assertEquals("non_mysql_type_cast", violation.getPatternId(), "应该匹配类型转换模式");
        assertEquals(SqlErrorType.POSTGRESQL_SYNTAX_ERROR, violation.getErrorType(), "应该是PostgreSQL语法错误");
        assertTrue(violation.getFriendlyMessage().contains("不支持 '::' 类型转换语法"), "错误消息应该明确指出不支持::语法");
        assertTrue(violation.getSuggestion().contains("CAST"), "应该建议使用CAST函数");
        
        log.info("PostgreSQL类型转换语法被正确拒绝: {}", violation.getFriendlyMessage());
    }

    @Test
    @DisplayName("测试PostgreSQL的generate_series函数应该被拒绝")
    public void testPostgreSqlGenerateSeries() {
        String invalidSql = "SELECT * FROM generate_series(1, 10);";
        
        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(invalidSql);
        
        assertFalse(result.isValid(), "包含generate_series函数的SQL应该被拒绝");
        assertTrue(result.hasViolations(), "应该有语法违规");
        
        MySqlSyntaxViolation violation = result.getFirstViolation();
        assertEquals("non_mysql_generate_series", violation.getPatternId(), "应该匹配generate_series模式");
        assertEquals(SqlErrorType.POSTGRESQL_SYNTAX_ERROR, violation.getErrorType(), "应该是PostgreSQL语法错误");
        assertTrue(violation.getFriendlyMessage().contains("不支持 generate_series() 函数"), "错误消息应该明确指出不支持generate_series函数");
        
        log.info("PostgreSQL generate_series函数被正确拒绝: {}", violation.getFriendlyMessage());
    }

    @Test
    @DisplayName("测试PostgreSQL的ILIKE操作符应该被拒绝")
    public void testPostgreSqlILike() {
        String invalidSql = "SELECT * FROM users WHERE name ILIKE '%john%';";
        
        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(invalidSql);
        
        assertFalse(result.isValid(), "包含ILIKE操作符的SQL应该被拒绝");
        assertTrue(result.hasViolations(), "应该有语法违规");
        
        MySqlSyntaxViolation violation = result.getFirstViolation();
        assertEquals("non_mysql_ilike", violation.getPatternId(), "应该匹配ILIKE模式");
        assertEquals(SqlErrorType.POSTGRESQL_SYNTAX_ERROR, violation.getErrorType(), "应该是PostgreSQL语法错误");
        assertTrue(violation.getFriendlyMessage().contains("不支持 ILIKE 操作符"), "错误消息应该明确指出不支持ILIKE操作符");
        
        log.info("PostgreSQL ILIKE操作符被正确拒绝: {}", violation.getFriendlyMessage());
    }

    @Test
    @DisplayName("测试PostgreSQL的RETURNING子句应该被拒绝")
    public void testPostgreSqlReturning() {
        String invalidSql = "INSERT INTO users (name) VALUES ('John') RETURNING id;";
        
        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(invalidSql);
        
        assertFalse(result.isValid(), "包含RETURNING子句的SQL应该被拒绝");
        assertTrue(result.hasViolations(), "应该有语法违规");
        
        MySqlSyntaxViolation violation = result.getFirstViolation();
        assertEquals("non_mysql_returning", violation.getPatternId(), "应该匹配RETURNING模式");
        assertEquals(SqlErrorType.POSTGRESQL_SYNTAX_ERROR, violation.getErrorType(), "应该是PostgreSQL语法错误");
        assertTrue(violation.getFriendlyMessage().contains("不支持 RETURNING 子句"), "错误消息应该明确指出不支持RETURNING子句");
        
        log.info("PostgreSQL RETURNING子句被正确拒绝: {}", violation.getFriendlyMessage());
    }

    @Test
    @DisplayName("测试多个违规的复杂SQL")
    public void testMultipleViolations() {
        String invalidSql = "SELECT random()::text FROM generate_series(1, 10) WHERE name ILIKE '%test%' RETURNING id;";
        
        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(invalidSql);
        
        assertFalse(result.isValid(), "包含多个非MySQL语法的SQL应该被拒绝");
        assertTrue(result.hasViolations(), "应该有语法违规");
        assertTrue(result.getViolationCount() >= 1, "应该检测到至少1个违规");
        assertTrue(result.getViolationCount() <= 5, "违规数量应该合理（不超过5个）");
        
        log.info("检测到 {} 个语法违规", result.getViolationCount());
        for (MySqlSyntaxViolation violation : result.getViolations()) {
            log.info("违规: {} - {}", violation.getPatternId(), violation.getFriendlyMessage());
        }
        
        // 验证错误报告
        String errorReport = result.generateErrorReport();
        assertNotNull(errorReport, "应该生成错误报告");
        assertTrue(errorReport.contains("MySQL语法验证失败"), "错误报告应该包含失败信息");
        
        log.info("错误报告:\n{}", errorReport);
    }
}
