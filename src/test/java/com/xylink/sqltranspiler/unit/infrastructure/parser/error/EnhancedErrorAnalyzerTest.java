package com.xylink.sqltranspiler.unit.infrastructure.parser.error;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.infrastructure.parser.error.EnhancedErrorAnalyzer;
import com.xylink.sqltranspiler.infrastructure.parser.error.ErrorPatternRegistry;
import com.xylink.sqltranspiler.infrastructure.parser.error.ErrorSeverity;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorInfo;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorType;

/**
 * 增强错误分析器测试
 */
@DisplayName("增强错误分析器测试")
public class EnhancedErrorAnalyzerTest {
    
    private static final Logger log = LoggerFactory.getLogger(EnhancedErrorAnalyzerTest.class);
    
    @Test
    @DisplayName("测试 ON UPDATE NOW() 错误检测和修复")
    public void testOnUpdateNowError() {
        String sql = "ALTER TABLE test ADD COLUMN update_time DATETIME DEFAULT NOW() NULL ON UPDATE NOW()";
        
        // 检测已知语法问题
        assertTrue(EnhancedErrorAnalyzer.hasKnownSyntaxIssues(sql), "应该检测到 ON UPDATE NOW() 语法问题");
        
        // 自动修复
        String fixedSql = EnhancedErrorAnalyzer.autoFixKnownIssues(sql);
        assertFalse(fixedSql.contains("ON UPDATE NOW()"), "修复后不应包含 ON UPDATE NOW()");
        assertTrue(fixedSql.contains("ON UPDATE CURRENT_TIMESTAMP"), "修复后应包含 ON UPDATE CURRENT_TIMESTAMP");
        assertTrue(fixedSql.contains("DEFAULT CURRENT_TIMESTAMP"), "修复后应包含 DEFAULT CURRENT_TIMESTAMP");
        
        log.info("原始SQL: {}", sql);
        log.info("修复后SQL: {}", fixedSql);
    }
    
    @Test
    @DisplayName("测试错误分析功能")
    public void testErrorAnalysis() {
        String sql = "ALTER TABLE test ADD COLUMN update_time DATETIME DEFAULT NOW() NULL ON UPDATE NOW()";
        String errorMessage = "no viable alternative at input 'ON UPDATE NOW()'";
        int line = 1;
        int position = 70;
        
        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, errorMessage, line, position);
        
        assertNotNull(errorInfo, "错误信息不应为空");
        assertEquals(SqlErrorType.INVALID_ON_UPDATE_SYNTAX, errorInfo.getErrorType(), "应该识别为 ON UPDATE 语法错误");
        assertNotNull(errorInfo.getFriendlyMessage(), "应该有友好的错误信息");
        assertNotNull(errorInfo.getSuggestion(), "应该有修复建议");
        assertTrue(errorInfo.isAutoFixable(), "应该可以自动修复");
        assertNotNull(errorInfo.getSuggestedFix(), "应该有修复后的SQL");
        
        log.info("错误分析结果:");
        log.info("错误类型: {}", errorInfo.getErrorType());
        log.info("友好信息: {}", errorInfo.getFriendlyMessage());
        log.info("详细信息: {}", errorInfo.getDetailedMessage());
        log.info("修复建议: {}", errorInfo.getSuggestion());
        log.info("修复后SQL: {}", errorInfo.getSuggestedFix());
    }
    
    @Test
    @DisplayName("测试格式化错误信息")
    public void testFormattedErrorMessage() {
        String sql = "ALTER TABLE test ADD COLUMN update_time DATETIME ON UPDATE NOW()";
        String errorMessage = "no viable alternative at input 'NOW()'";
        
        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, errorMessage, 1, 60);
        
        String formattedMessage = errorInfo.getFormattedMessage();
        assertNotNull(formattedMessage, "格式化信息不应为空");
        assertTrue(formattedMessage.contains("【SQL语法错误】"), "应该包含错误标题");
        assertTrue(formattedMessage.contains("位置:"), "应该包含位置信息");
        assertTrue(formattedMessage.contains("建议:"), "应该包含建议");
        
        String shortMessage = errorInfo.getShortMessage();
        assertNotNull(shortMessage, "简短信息不应为空");
        assertTrue(shortMessage.contains("[第1行第60列]"), "应该包含位置信息");
        
        log.info("格式化错误信息:");
        log.info("{}", formattedMessage);
        log.info("简短信息: {}", shortMessage);
    }
    
    @Test
    @DisplayName("测试无语法问题的SQL")
    public void testValidSql() {
        String sql = "CREATE TABLE test (id INT PRIMARY KEY, name VARCHAR(255))";
        
        assertFalse(EnhancedErrorAnalyzer.hasKnownSyntaxIssues(sql), "有效SQL不应检测到语法问题");
        
        String fixedSql = EnhancedErrorAnalyzer.autoFixKnownIssues(sql);
        assertEquals(sql, fixedSql, "有效SQL修复后应该保持不变");
    }
    
    @Test
    @DisplayName("测试通用语法错误分析")
    public void testGenericSyntaxError() {
        String sql = "CREATE TABLE test (id INT PRIMARY KEY, name VARCHAR(255)";
        String errorMessage = "missing ')' at '<EOF>'";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, errorMessage, 1, 50);

        assertNotNull(errorInfo, "错误信息不应为空");
        assertNotNull(errorInfo.getFriendlyMessage(), "应该有友好的错误信息");
        assertEquals(ErrorSeverity.ERROR, errorInfo.getSeverity(), "应该是错误级别");

        log.info("通用错误分析: {}", errorInfo.getShortMessage());
    }

    @Test
    @DisplayName("测试错误模式注册表系统")
    public void testErrorPatternRegistry() {
        // 测试MySQL特定错误
        String mysqlSql = "ALTER TABLE test ADD COLUMN update_time DATETIME ON UPDATE NOW()";
        assertTrue(ErrorPatternRegistry.hasKnownSyntaxIssues(mysqlSql), "应该检测到MySQL语法问题");

        String fixedSql = ErrorPatternRegistry.autoFixKnownIssues(mysqlSql);
        assertFalse(fixedSql.contains("ON UPDATE NOW()"), "修复后不应包含 ON UPDATE NOW()");
        assertTrue(fixedSql.contains("ON UPDATE CURRENT_TIMESTAMP"), "修复后应包含 ON UPDATE CURRENT_TIMESTAMP");

        log.info("注册表测试 - 原始SQL: {}", mysqlSql);
        log.info("注册表测试 - 修复后SQL: {}", fixedSql);

        // 测试无问题的SQL
        String validSql = "CREATE TABLE test (id INT PRIMARY KEY)";
        assertFalse(ErrorPatternRegistry.hasKnownSyntaxIssues(validSql), "有效SQL不应检测到问题");
        assertEquals(validSql, ErrorPatternRegistry.autoFixKnownIssues(validSql), "有效SQL修复后应保持不变");
    }

    @Test
    @DisplayName("测试多种错误类型的综合处理")
    public void testComprehensiveErrorHandling() {
        // 测试各种错误类型
        String[] testCases = {
            "ALTER TABLE test ADD COLUMN time DATETIME ON UPDATE NOW()",  // MySQL特定错误
            "CREATE TABLE test (id INT PRIMARY KEY, name VARCHAR(255)",   // 缺少括号
            "INSERT INTO test VALUES ('unclosed string",                  // 未闭合字符串
            "SELECT * FROM nonexistent_table",                           // 无效表引用
            "SELECT unknown_column FROM test"                            // 无效列引用
        };

        String[] errorMessages = {
            "no viable alternative at input 'ON UPDATE NOW()'",
            "missing ')' at '<EOF>'",
            "unterminated string literal",
            "table 'nonexistent_table' doesn't exist",
            "unknown column 'unknown_column'"
        };

        for (int i = 0; i < testCases.length; i++) {
            String sql = testCases[i];
            String errorMessage = errorMessages[i];

            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, errorMessage, 1, 10);

            assertNotNull(errorInfo, "错误信息不应为空 - 测试用例 " + (i + 1));
            assertNotNull(errorInfo.getFriendlyMessage(), "应该有友好的错误信息 - 测试用例 " + (i + 1));
            assertNotNull(errorInfo.getSuggestion(), "应该有修复建议 - 测试用例 " + (i + 1));

            log.info("测试用例 {}: SQL={}", i + 1, sql);
            log.info("测试用例 {}: 错误类型={}", i + 1, errorInfo.getErrorType());
            log.info("测试用例 {}: 友好信息={}", i + 1, errorInfo.getFriendlyMessage());
            log.info("测试用例 {}: 建议={}", i + 1, errorInfo.getSuggestion());
            log.info("---");
        }
    }
}
