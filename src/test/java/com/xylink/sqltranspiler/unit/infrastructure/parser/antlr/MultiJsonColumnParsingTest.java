package com.xylink.sqltranspiler.unit.infrastructure.parser.antlr;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 多JSON列解析问题调试测试
 * 
 * 根据数据库规则：严格禁止简化测试用例SQL，必须使用真实的MySQL语法
 * 目的：验证ANTLR语法对多个JSON列的解析能力
 */
public class MultiJsonColumnParsingTest {
    
    private static final Logger log = LoggerFactory.getLogger(MultiJsonColumnParsingTest.class);
    
    @Test
    @DisplayName("单个JSON列解析测试")
    public void testSingleJsonColumn() {
        String sql = "CREATE TABLE test (id INT PRIMARY KEY, json_data JSON);";
        
        log.info("测试单个JSON列解析: {}", sql);
        
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "单个JSON列应该能够正常解析");
            log.info("✅ 单个JSON列解析成功");
        } catch (Exception e) {
            log.error("❌ 单个JSON列解析失败: {}", e.getMessage());
            fail("单个JSON列解析不应失败: " + e.getMessage());
        }
    }
    
    @Test
    @DisplayName("两个JSON列解析测试")
    public void testTwoJsonColumns() {
        String sql = "CREATE TABLE test (id INT PRIMARY KEY, json_data JSON, json_array JSON);";

        log.info("测试两个JSON列解析: {}", sql);

        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "两个JSON列应该能够正常解析");
            log.info("✅ 两个JSON列解析成功");
        } catch (Exception e) {
            log.error("❌ 两个JSON列解析失败: {}", e.getMessage());
            log.error("错误位置分析：{}", sql);
            log.error("这是ANTLR语法问题：JSON类型解析存在歧义");
            fail("两个JSON列解析失败，需要修正ANTLR语法: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("对比测试：两个TEXT列解析")
    public void testTwoTextColumns() {
        String sql = "CREATE TABLE test (id INT PRIMARY KEY, text_data TEXT, text_array TEXT);";

        log.info("对比测试两个TEXT列解析: {}", sql);

        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "两个TEXT列应该能够正常解析");
            log.info("✅ 两个TEXT列解析成功");
        } catch (Exception e) {
            log.error("❌ 两个TEXT列解析失败: {}", e.getMessage());
            fail("两个TEXT列解析不应失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("对比测试：两个BOOLEAN列解析")
    public void testTwoBooleanColumns() {
        String sql = "CREATE TABLE test (id INT PRIMARY KEY, bool_data BOOLEAN, bool_array BOOLEAN);";

        log.info("对比测试两个BOOLEAN列解析: {}", sql);

        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "两个BOOLEAN列应该能够正常解析");
            log.info("✅ 两个BOOLEAN列解析成功");
        } catch (Exception e) {
            log.error("❌ 两个BOOLEAN列解析失败: {}", e.getMessage());
            fail("两个BOOLEAN列解析不应失败: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("多个JSON列解析测试")
    public void testMultipleJsonColumns() {
        String sql = "CREATE TABLE test (id INT PRIMARY KEY, json_data JSON, json_array JSON, json_object JSON);";

        log.info("测试多个JSON列解析: {}", sql);

        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "多个JSON列应该能够正常解析");
            log.info("✅ 多个JSON列解析成功");
        } catch (Exception e) {
            log.error("❌ 多个JSON列解析失败: {}", e.getMessage());
            log.error("这是ANTLR语法问题：JSON类型解析存在歧义");
            // 根据数据库规则，不简化测试，而是记录问题
            fail("多个JSON列解析失败，需要修正ANTLR语法: " + e.getMessage());
        }
    }
    
    @Test
    @DisplayName("复杂窗口函数解析测试")
    public void testComplexWindowFunctions() {
        String sql = """
            SELECT 
                name,
                salary,
                ROW_NUMBER() OVER (ORDER BY salary DESC) as row_num,
                RANK() OVER (ORDER BY salary DESC) as rank_val,
                DENSE_RANK() OVER (ORDER BY salary DESC) as dense_rank_val
            FROM employees;
            """;
        
        log.info("测试复杂窗口函数解析: {}", sql);
        
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "复杂窗口函数应该能够正常解析");
            log.info("✅ 复杂窗口函数解析成功");
        } catch (Exception e) {
            log.error("❌ 复杂窗口函数解析失败: {}", e.getMessage());
            log.error("这是ANTLR语法问题：多个OVER子句连续解析可能存在冲突");
            fail("复杂窗口函数解析失败，需要修正ANTLR语法: " + e.getMessage());
        }
    }
    
    @Test
    @DisplayName("复杂日期函数解析测试")
    public void testComplexDateFunctions() {
        String sql = """
            SELECT 
                DATE_ADD(NOW(), INTERVAL 1 DAY) as date_add,
                DATE_SUB(NOW(), INTERVAL 1 MONTH) as date_sub,
                DATE_FORMAT(NOW(), '%Y-%m-%d') as formatted_date
            FROM dual;
            """;
        
        log.info("测试复杂日期函数解析: {}", sql);
        
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "复杂日期函数应该能够正常解析");
            log.info("✅ 复杂日期函数解析成功");
        } catch (Exception e) {
            log.error("❌ 复杂日期函数解析失败: {}", e.getMessage());
            log.error("这是ANTLR语法问题：INTERVAL表达式或DATE_FORMAT函数解析存在问题");
            fail("复杂日期函数解析失败，需要修正ANTLR语法: " + e.getMessage());
        }
    }
    
    @Test
    @DisplayName("复杂JSON函数解析测试")
    public void testComplexJsonFunctions() {
        String sql = """
            SELECT 
                JSON_EXTRACT(json_col, '$.name') as extracted_name,
                JSON_OBJECT('name', 'John', 'age', 30) as json_object,
                JSON_ARRAY('a', 'b', 'c') as json_array
            FROM test_table;
            """;
        
        log.info("测试复杂JSON函数解析: {}", sql);
        
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            assertNotNull(statement, "复杂JSON函数应该能够正常解析");
            log.info("✅ 复杂JSON函数解析成功");
        } catch (Exception e) {
            log.error("❌ 复杂JSON函数解析失败: {}", e.getMessage());
            log.error("这是ANTLR语法问题：JSON函数名识别或参数解析存在问题");
            fail("复杂JSON函数解析失败，需要修正ANTLR语法: " + e.getMessage());
        }
    }
}
