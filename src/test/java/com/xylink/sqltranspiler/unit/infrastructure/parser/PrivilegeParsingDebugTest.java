package com.xylink.sqltranspiler.unit.infrastructure.parser;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.privilege.Grant;
import com.xylink.sqltranspiler.core.ast.privilege.Revoke;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 权限管理语句解析调试测试
 */
@DisplayName("权限管理语句解析调试测试")
public class PrivilegeParsingDebugTest {

    @Test
    @DisplayName("调试GRANT语句解析")
    public void debugGrantParsing() {
        String sql = "GRANT SELECT, INSERT ON test_table TO user1";
        
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            System.out.println("Parsed statement type: " + statement.getClass().getSimpleName());
            System.out.println("Statement: " + statement);
            
            if (statement instanceof Grant) {
                Grant grant = (Grant) statement;
                System.out.println("Grant privileges: " + grant.getPrivileges());
                System.out.println("Grant object type: " + grant.getObjectType());
                System.out.println("Grant privilege level: " + grant.getPrivilegeLevel());
                System.out.println("Grant users: " + grant.getUsers());
                System.out.println("Grant original SQL: " + grant.getOriginalSql());
            } else {
                System.out.println("Statement is not a Grant instance, it's: " + statement.getClass().getName());
            }
            
            assertNotNull(statement);
            assertTrue(statement instanceof Grant, "Should parse as Grant statement");
            
        } catch (Exception e) {
            System.out.println("Parsing failed with exception: " + e.getMessage());
            e.printStackTrace();
            fail("Failed to parse GRANT statement: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("调试REVOKE语句解析")
    public void debugRevokeParsing() {
        String sql = "REVOKE SELECT, INSERT ON test_table FROM user1";
        
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            System.out.println("Parsed statement type: " + statement.getClass().getSimpleName());
            System.out.println("Statement: " + statement);
            
            if (statement instanceof Revoke) {
                Revoke revoke = (Revoke) statement;
                System.out.println("Revoke privileges: " + revoke.getPrivileges());
                System.out.println("Revoke object type: " + revoke.getObjectType());
                System.out.println("Revoke privilege level: " + revoke.getPrivilegeLevel());
                System.out.println("Revoke users: " + revoke.getUsers());
                System.out.println("Revoke original SQL: " + revoke.getOriginalSql());
            } else {
                System.out.println("Statement is not a Revoke instance, it's: " + statement.getClass().getName());
            }
            
            assertNotNull(statement);
            assertTrue(statement instanceof Revoke, "Should parse as Revoke statement");
            
        } catch (Exception e) {
            System.out.println("Parsing failed with exception: " + e.getMessage());
            e.printStackTrace();
            fail("Failed to parse REVOKE statement: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("调试GRANT ALL PRIVILEGES语句解析")
    public void debugGrantAllPrivilegesParsing() {
        String sql = "GRANT ALL PRIVILEGES ON testdb.* TO user2";

        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            System.out.println("Parsed statement type: " + statement.getClass().getSimpleName());
            System.out.println("Statement: " + statement);

            assertNotNull(statement);
            assertTrue(statement instanceof Grant, "Should parse as Grant statement");

        } catch (Exception e) {
            System.out.println("Parsing failed with exception: " + e.getMessage());
            e.printStackTrace();
            fail("Failed to parse GRANT ALL PRIVILEGES statement: " + e.getMessage());
        }
    }

    @Test
    @DisplayName("调试REVOKE ALL PRIVILEGES语句解析")
    public void debugRevokeAllPrivilegesParsing() {
        String sql = "REVOKE ALL PRIVILEGES, GRANT OPTION FROM user3";
        
        try {
            Statement statement = MySqlHelper.parseStatement(sql);
            System.out.println("Parsed statement type: " + statement.getClass().getSimpleName());
            System.out.println("Statement: " + statement);
            
            assertNotNull(statement);
            assertTrue(statement instanceof Revoke, "Should parse as Revoke statement");
            
        } catch (Exception e) {
            System.out.println("Parsing failed with exception: " + e.getMessage());
            e.printStackTrace();
            fail("Failed to parse REVOKE ALL PRIVILEGES statement: " + e.getMessage());
        }
    }
}
