package com.xylink.sqltranspiler.unit.infrastructure.parser.error;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.infrastructure.parser.error.EnhancedErrorAnalyzer;
import com.xylink.sqltranspiler.infrastructure.parser.error.ErrorPatternRegistry;
import com.xylink.sqltranspiler.infrastructure.parser.error.MySqlSyntaxValidationResult;
import com.xylink.sqltranspiler.infrastructure.parser.error.MySqlSyntaxViolation;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorInfo;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorType;

import lombok.extern.slf4j.Slf4j;

/**
 * MySQL语法验证测试
 * 测试项目能否正确识别不符合MySQL规范的语法并给出明确提示
 */
@Slf4j
@DisplayName("MySQL语法验证测试")
public class MySqlSyntaxValidationTest {

    @Test
    @DisplayName("测试PostgreSQL类型转换语法检测")
    public void testPostgreSqlTypeCastDetection() {
        // 测试用例：只包含PostgreSQL类型转换语法的SQL
        String sql = "SELECT id::text, created_at::timestamp FROM users";

        // 模拟ANTLR解析错误消息 - 使用通用错误消息，让系统通过SQL内容匹配
        String errorMessage = "syntax error";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, errorMessage, 1, 10);

        assertNotNull(errorInfo, "错误信息不应为空");

        // 调试输出
        log.info("错误类型: {}", errorInfo.getErrorType());
        log.info("友好消息: {}", errorInfo.getFriendlyMessage());
        log.info("详细消息: {}", errorInfo.getDetailedMessage());
        log.info("修复建议: {}", errorInfo.getSuggestion());

        assertEquals(SqlErrorType.POSTGRESQL_SYNTAX_ERROR, errorInfo.getErrorType(), "应该识别为PostgreSQL语法错误");
        assertTrue(errorInfo.getFriendlyMessage().contains("不支持 '::' 类型转换语法"), "应该明确指出不支持::语法");
        assertTrue(errorInfo.getDetailedMessage().contains("MySQL 8.4官方文档"), "应该引用MySQL官方文档");
        assertTrue(errorInfo.getSuggestion().contains("CAST"), "应该建议使用CAST函数");

        log.info("类型转换语法错误检测: {}", errorInfo.getFriendlyMessage());
        log.info("详细信息: {}", errorInfo.getDetailedMessage());
        log.info("修复建议: {}", errorInfo.getSuggestion());
    }

    @Test
    @DisplayName("测试random()函数检测")
    public void testRandomFunctionDetection() {
        // 测试用例：包含random()函数的SQL
        String sql = "SELECT random() FROM test";

        // 模拟ANTLR解析错误消息 - 使用通用错误消息，让系统通过SQL内容匹配
        String errorMessage = "syntax error";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, errorMessage, 1, 7);

        assertNotNull(errorInfo, "错误信息不应为空");

        // 调试输出
        log.info("错误类型: {}", errorInfo.getErrorType());
        log.info("友好消息: {}", errorInfo.getFriendlyMessage());
        log.info("详细消息: {}", errorInfo.getDetailedMessage());
        log.info("修复建议: {}", errorInfo.getSuggestion());

        assertEquals(SqlErrorType.POSTGRESQL_SYNTAX_ERROR, errorInfo.getErrorType(), "应该识别为PostgreSQL语法错误");
        assertTrue(errorInfo.getFriendlyMessage().contains("不支持 random() 函数"), "应该明确指出不支持random()函数");
        assertTrue(errorInfo.getDetailedMessage().contains("MySQL 8.4官方文档"), "应该引用MySQL官方文档");
        assertTrue(errorInfo.getSuggestion().contains("RAND()"), "应该建议使用RAND()函数");

        log.info("random()函数错误检测: {}", errorInfo.getFriendlyMessage());
        log.info("详细信息: {}", errorInfo.getDetailedMessage());
        log.info("修复建议: {}", errorInfo.getSuggestion());
    }

    @Test
    @DisplayName("测试clock_timestamp()函数检测")
    public void testClockTimestampFunctionDetection() {
        // 测试用例：包含clock_timestamp()函数的SQL
        String sql = "SELECT clock_timestamp() FROM test";

        // 模拟ANTLR解析错误消息 - 使用通用错误消息，让系统通过SQL内容匹配
        String errorMessage = "syntax error";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, errorMessage, 1, 7);

        assertNotNull(errorInfo, "错误信息不应为空");
        assertEquals(SqlErrorType.POSTGRESQL_SYNTAX_ERROR, errorInfo.getErrorType(), "应该识别为PostgreSQL语法错误");
        assertTrue(errorInfo.getFriendlyMessage().contains("不支持 clock_timestamp() 函数"), "应该明确指出不支持clock_timestamp()函数");
        assertTrue(errorInfo.getDetailedMessage().contains("MySQL 8.4官方文档"), "应该引用MySQL官方文档");
        assertTrue(errorInfo.getSuggestion().contains("NOW()"), "应该建议使用NOW()函数");

        log.info("clock_timestamp()函数错误检测: {}", errorInfo.getFriendlyMessage());
        log.info("详细信息: {}", errorInfo.getDetailedMessage());
        log.info("修复建议: {}", errorInfo.getSuggestion());
    }

    @Test
    @DisplayName("测试完整的问题SQL语句")
    public void testCompleteProblematicSql() {
        // 用户提供的问题SQL语句
        String sql = "INSERT INTO ainemo.t_en_role_res (id, role_id, res_id, create_time) " +
                    "SELECT lower(md5(random()::text || clock_timestamp()::text)), t.role_id, " +
                    "'95958fe34aa32087022583c4bcfaf95c', NOW() " +
                    "FROM ainemo.t_en_role_res t " +
                    "WHERE t.res_id = 'ff8080815ac59dd5015ac63d1b7e0058' " +
                    "AND t.role_id NOT IN ( 'ff8080815ac59dd5015ac63d1b7e002c', 'ff8080815ac59dd5015ac63d1b7e002e' )";
        
        log.info("测试完整SQL语句的错误检测:");
        log.info("SQL: {}", sql);
        
        // 测试random()函数检测
        SqlErrorInfo randomError = EnhancedErrorAnalyzer.analyzeSqlError(sql, "no viable alternative at input 'random()'", 1, 50);
        if (randomError.getErrorType() == SqlErrorType.POSTGRESQL_SYNTAX_ERROR) {
            log.info("✓ 检测到random()函数错误: {}", randomError.getFriendlyMessage());
        }
        
        // 测试::text类型转换检测
        SqlErrorInfo typeCastError = EnhancedErrorAnalyzer.analyzeSqlError(sql, "no viable alternative at input '::text'", 1, 60);
        if (typeCastError.getErrorType() == SqlErrorType.POSTGRESQL_SYNTAX_ERROR) {
            log.info("✓ 检测到::text类型转换错误: {}", typeCastError.getFriendlyMessage());
        }
        
        // 测试clock_timestamp()函数检测
        SqlErrorInfo clockError = EnhancedErrorAnalyzer.analyzeSqlError(sql, "no viable alternative at input 'clock_timestamp()'", 1, 70);
        if (clockError.getErrorType() == SqlErrorType.POSTGRESQL_SYNTAX_ERROR) {
            log.info("✓ 检测到clock_timestamp()函数错误: {}", clockError.getFriendlyMessage());
        }
        
        log.info("所有非MySQL语法都能被正确检测并给出明确的错误提示");
    }

    @Test
    @DisplayName("测试MySQL标准语法不会被误报")
    public void testValidMySqlSyntaxNotReported() {
        // 测试用例：标准的MySQL语法（避免使用dual表，因为它会被识别为Oracle语法）
        String validSql = "INSERT INTO test (id, name, created_at) " +
                         "SELECT CONCAT(MD5(RAND()), UUID()), 'test', NOW() " +
                         "FROM users LIMIT 1";

        // 使用新的MySQL语法验证API
        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(validSql);

        // 标准MySQL语法应该通过验证
        assertTrue(result.isValid(), "标准MySQL语法应该通过验证");
        assertEquals(0, result.getViolationCount(), "不应该有任何违规");

        log.info("有效MySQL语法测试通过，无违规记录");
    }

    @Test
    @DisplayName("测试PostgreSQL的string_agg()函数检测")
    void testPostgreSqlStringAggFunction() {
        String sql = "SELECT string_agg(name, ',') FROM users;";

        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(sql);

        assertFalse(result.isValid(), "应该检测到string_agg函数错误");
        assertTrue(result.getViolationCount() >= 1, "应该至少有1个错误");

        // 检查是否包含string_agg相关的错误
        boolean foundStringAggError = result.getViolations().stream()
            .anyMatch(violation -> violation.getPatternId().equals("non_mysql_string_agg"));
        assertTrue(foundStringAggError, "应该检测到string_agg函数错误");

        MySqlSyntaxViolation stringAggViolation = result.getViolations().stream()
            .filter(violation -> violation.getPatternId().equals("non_mysql_string_agg"))
            .findFirst().orElse(null);
        assertNotNull(stringAggViolation, "应该有string_agg违规记录");
        assertTrue(stringAggViolation.getFriendlyMessage().contains("string_agg"), "错误消息应该包含string_agg");
        assertTrue(stringAggViolation.getSuggestion().contains("GROUP_CONCAT"), "建议应该包含GROUP_CONCAT");

        log.info("string_agg()函数错误检测: {}", stringAggViolation.getFriendlyMessage());
    }

    @Test
    @DisplayName("测试PostgreSQL的unnest()函数检测")
    void testPostgreSqlUnnestFunction() {
        String sql = "SELECT unnest(ARRAY[1,2,3]);";

        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(sql);

        assertFalse(result.isValid(), "应该检测到unnest函数错误");
        assertTrue(result.getViolationCount() >= 1, "应该至少有1个错误");

        // 检查是否包含unnest相关的错误
        boolean foundUnnestError = result.getViolations().stream()
            .anyMatch(violation -> violation.getPatternId().equals("non_mysql_unnest"));
        assertTrue(foundUnnestError, "应该检测到unnest函数错误");

        MySqlSyntaxViolation unnestViolation = result.getViolations().stream()
            .filter(violation -> violation.getPatternId().equals("non_mysql_unnest"))
            .findFirst().orElse(null);
        assertNotNull(unnestViolation, "应该有unnest违规记录");
        assertTrue(unnestViolation.getFriendlyMessage().contains("unnest"), "错误消息应该包含unnest");
        assertTrue(unnestViolation.getSuggestion().contains("JSON_TABLE"), "建议应该包含JSON_TABLE");

        log.info("unnest()函数错误检测: {}", unnestViolation.getFriendlyMessage());
    }

    @Test
    @DisplayName("测试PostgreSQL的||字符串连接操作符检测")
    void testPostgreSqlStringConcatOperator() {
        String sql = "SELECT first_name || ' ' || last_name FROM users;";

        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(sql);

        assertFalse(result.isValid(), "应该检测到||操作符错误");
        assertTrue(result.getViolationCount() >= 1, "应该至少有1个错误");

        boolean foundConcatError = result.getViolations().stream()
            .anyMatch(violation -> violation.getPatternId().equals("non_mysql_string_concat_operator"));
        assertTrue(foundConcatError, "应该检测到字符串连接操作符错误");

        log.info("||操作符错误检测通过，错误数量: {}", result.getViolationCount());
    }

    @Test
    @DisplayName("测试SQL Server的GETDATE()函数检测")
    void testSqlServerGetDateFunction() {
        String sql = "SELECT GETDATE() as current_time;";

        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(sql);

        assertFalse(result.isValid(), "应该检测到GETDATE函数错误");
        assertTrue(result.getViolationCount() >= 1, "应该至少有1个错误");

        // 检查是否包含GETDATE相关的错误
        boolean foundGetDateError = result.getViolations().stream()
            .anyMatch(violation -> violation.getPatternId().equals("non_mysql_getdate"));
        assertTrue(foundGetDateError, "应该检测到GETDATE函数错误");

        MySqlSyntaxViolation getDateViolation = result.getViolations().stream()
            .filter(violation -> violation.getPatternId().equals("non_mysql_getdate"))
            .findFirst().orElse(null);
        assertNotNull(getDateViolation, "应该有GETDATE违规记录");
        assertTrue(getDateViolation.getFriendlyMessage().contains("GETDATE"), "错误消息应该包含GETDATE");
        assertTrue(getDateViolation.getSuggestion().contains("NOW"), "建议应该包含NOW");

        log.info("GETDATE()函数错误检测: {}", getDateViolation.getFriendlyMessage());
    }

    @Test
    @DisplayName("测试Oracle的SYSDATE函数检测（不带括号）")
    void testOracleSysdateFunction() {
        String sql = "SELECT SYSDATE FROM dual;";

        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(sql);

        assertFalse(result.isValid(), "应该检测到SYSDATE和DUAL错误");
        assertTrue(result.getViolationCount() >= 1, "应该至少有1个错误");

        boolean foundSysdateError = result.getViolations().stream()
            .anyMatch(violation -> violation.getPatternId().equals("non_mysql_sysdate_no_parens"));
        boolean foundDualError = result.getViolations().stream()
            .anyMatch(violation -> violation.getPatternId().equals("non_mysql_dual_table"));

        assertTrue(foundSysdateError || foundDualError, "应该检测到Oracle特有语法错误");

        log.info("Oracle语法错误检测通过，错误数量: {}", result.getViolationCount());
    }

    @Test
    @DisplayName("测试基于ANTLR的严格MySQL语法校验")
    void testAntlrStrictSyntaxValidation() {
        // 测试包含RANDOM函数的SQL（ANTLR语法文件中包含但MySQL官方不支持）
        String sql = "SELECT RANDOM() FROM users;";

        MySqlSyntaxValidationResult result = ErrorPatternRegistry.validateMySqlSyntax(sql);

        assertFalse(result.isValid(), "应该检测到RANDOM函数错误");
        assertTrue(result.getViolationCount() >= 1, "应该至少有1个错误");

        // 检查是否包含基于函数白名单的检测
        boolean foundFunctionError = result.getViolations().stream()
            .anyMatch(violation -> violation.getFriendlyMessage().contains("RANDOM") ||
                                 violation.getFriendlyMessage().contains("random"));
        assertTrue(foundFunctionError, "应该检测到RANDOM函数相关错误");

        log.info("ANTLR严格语法校验测试通过，错误数量: {}", result.getViolationCount());

        // 打印所有检测到的错误
        for (MySqlSyntaxViolation violation : result.getViolations()) {
            log.info("检测到错误: {} - {}", violation.getPatternId(), violation.getFriendlyMessage());
        }
    }

    @Test
    @DisplayName("测试MySQL官方函数白名单校验")
    void testMySqlOfficialFunctionWhitelist() {
        // 测试MySQL官方支持的函数（应该通过）
        String validSql = "SELECT RAND(), NOW(), CONCAT('Hello', 'World') FROM users;";

        MySqlSyntaxValidationResult validResult = ErrorPatternRegistry.validateMySqlSyntax(validSql);

        assertTrue(validResult.isValid(), "MySQL官方函数应该通过校验");
        assertEquals(0, validResult.getViolationCount(), "不应该有任何违规");

        log.info("MySQL官方函数白名单校验通过");

        // 测试非MySQL函数（应该被拒绝）
        String invalidSql = "SELECT random(), string_agg(name, ','), getdate() FROM users;";

        MySqlSyntaxValidationResult invalidResult = ErrorPatternRegistry.validateMySqlSyntax(invalidSql);

        assertFalse(invalidResult.isValid(), "非MySQL函数应该被拒绝");
        assertTrue(invalidResult.getViolationCount() >= 1, "应该检测到多个错误");

        log.info("非MySQL函数检测通过，错误数量: {}", invalidResult.getViolationCount());
    }
}
