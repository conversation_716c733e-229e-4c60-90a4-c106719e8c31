package com.xylink.sqltranspiler.unit.infrastructure.parser.error;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.infrastructure.parser.error.EnhancedErrorAnalyzer;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorInfo;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorType;

/**
 * 神通数据库特定错误检测测试
 * 基于神通数据库官方文档的限制和不支持的功能
 */
@DisplayName("神通数据库错误检测测试")
public class ShentongErrorDetectionTest {

    private static final Logger log = LoggerFactory.getLogger(ShentongErrorDetectionTest.class);

    @Test
    @DisplayName("检测神通数据库不支持的MySQL自增步长设置")
    void testAutoIncrementStepDetection() {
        String sql = "SET @@AUTO_INCREMENT_INCREMENT = 2; SET @@AUTO_INCREMENT_OFFSET = 1;";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库自增步长检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.SYNTAX_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库不支持：MySQL的自增列步长和偏移量设置"));
    }

    @Test
    @DisplayName("检测神通数据库不支持的XML namespace功能")
    void testXmlNamespaceDetection() {
        String sql = "SELECT EXTRACTVALUE(xml_col, '/root/item', 'namespace') FROM test_table;";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库XML namespace检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.FUNCTION_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库不支持：XML函数的namespace参数"));
    }

    @Test
    @DisplayName("检测神通数据库不支持的临时表分区功能")
    void testTempTablePartitionDetection() {
        String sql = "CREATE TEMPORARY TABLE temp_test (id INT, name VARCHAR(50)) PARTITION BY RANGE(id);";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库临时表分区检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.SYNTAX_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库不支持：临时表的分区、外键约束等功能"));
    }

    @Test
    @DisplayName("检测神通数据库不支持的临时表外键约束")
    void testTempTableForeignKeyDetection() {
        String sql = "CREATE TEMP TABLE temp_test (id INT, parent_id INT, FOREIGN KEY (parent_id) REFERENCES parent_table(id));";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库临时表外键检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.SYNTAX_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库不支持：临时表的分区、外键约束等功能"));
    }

    @Test
    @DisplayName("检测神通数据库不支持的LOB缓存功能")
    void testLobCacheDetection() {
        String sql = "CREATE TABLE test_table (id INT, content CLOB CACHE);";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库LOB缓存检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.SYNTAX_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库暂不支持：LOB缓存功能"));
    }

    @Test
    @DisplayName("检测神通数据库不支持的HASH分区合并操作")
    void testHashPartitionMergeDetection() {
        String sql = "ALTER TABLE test_table MERGE PARTITION p1 HASH;";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库HASH分区合并检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.SYNTAX_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库不支持：HASH和LIST分区的合并/分裂操作"));
    }

    @Test
    @DisplayName("检测神通数据库不支持的HASH索引算法")
    void testHashIndexDetection() {
        String sql = "CREATE INDEX idx_test ON test_table (id) USING HASH;";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库HASH索引检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.SYNTAX_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库不支持：HASH和RTREE索引算法"));
    }

    @Test
    @DisplayName("检测神通数据库不支持的PREPARE存储过程")
    void testPrepareStoredProcedureDetection() {
        String sql = "PREPARE stmt FROM 'CALL get_user_data(?)';";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库PREPARE存储过程检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.FUNCTION_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库不支持：PREPARE语句执行返回结果集的存储过程"));
    }

    @Test
    @DisplayName("检测神通数据库不支持的游标ABSOLUTE定位")
    void testCursorAbsoluteDetection() {
        String sql = "FETCH ABSOLUTE 10 FROM cursor_name;";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库游标ABSOLUTE检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.SYNTAX_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库暂不支持：游标的ABSOLUTE定位功能"));
    }

    @Test
    @DisplayName("检测神通数据库不支持的空间数据类型")
    void testSpatialDataTypeDetection() {
        String sql = "CREATE TABLE geo_table (id INT, location GEOMETRY, area POLYGON);";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库空间数据类型检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.DATA_TYPE_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库不支持：空间数据类型"));
    }

    @Test
    @DisplayName("检测神通数据库不支持的SELECT FOR UPDATE高级选项")
    void testSelectForUpdateAdvancedOptionsDetection() {
        String sql = "SELECT * FROM test_table WHERE id = 1 FOR UPDATE SKIP LOCKED;";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库SELECT FOR UPDATE检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.SYNTAX_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库不支持：SELECT FOR UPDATE的SKIP LOCKED和NOWAIT选项"));
    }

    @Test
    @DisplayName("检测神通数据库层次查询环检测限制")
    void testHierarchicalQueryCycleDetection() {
        String sql = "SELECT * FROM employees CONNECT BY manager_id = employee_id;";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库层次查询环检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.SYNTAX_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库注意：层次查询中没有PRIOR关键字时不会进行环检测"));
    }

    @Test
    @DisplayName("检测神通数据库不支持的BULK COLLECT字符串索引")
    void testBulkCollectStringIndexDetection() {
        String sql = "BULK COLLECT INTO string_array(VARCHAR2);";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库BULK COLLECT检测 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        assertNotNull(errorInfo, "错误信息不应为空");
        // 检查是否检测到神通数据库特定的错误
        assertTrue(errorInfo.getErrorType() == SqlErrorType.SYNTAX_COMPATIBILITY ||
                  errorInfo.getFriendlyMessage().contains("神通数据库不支持：对字符串类型键值的索引表使用BULK COLLECT"));
    }

    @Test
    @DisplayName("验证神通数据库支持的功能不会被误报")
    void testSupportedFeaturesNotDetected() {
        // 神通数据库支持的基本功能
        String sql = "CREATE TABLE test_table (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100)); " +
                    "INSERT INTO test_table (name) VALUES ('test'); " +
                    "SELECT * FROM test_table WHERE id = 1;";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库支持功能验证 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        // 不应该检测到神通数据库特定的错误
        assertFalse(errorInfo.getFriendlyMessage() != null && (
            errorInfo.getFriendlyMessage().contains("神通数据库不支持") ||
            errorInfo.getFriendlyMessage().contains("神通数据库暂不支持")
        ));
    }

    @Test
    @DisplayName("验证神通数据库支持的层次查询不会被误报")
    void testSupportedHierarchicalQueryNotDetected() {
        // 神通数据库支持的带PRIOR关键字的层次查询
        String sql = "SELECT * FROM employees CONNECT BY PRIOR employee_id = manager_id;";

        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "test error", 1, 1);

        log.info("神通数据库支持的层次查询验证 - SQL: {}", sql);
        log.info("  错误类型: {}", errorInfo.getErrorType());
        log.info("  友好信息: {}", errorInfo.getFriendlyMessage());

        // 不应该检测到层次查询环检测的警告
        assertFalse(errorInfo.getFriendlyMessage() != null &&
            errorInfo.getFriendlyMessage().contains("层次查询中没有PRIOR关键字时不会进行环检测"));
    }
}
