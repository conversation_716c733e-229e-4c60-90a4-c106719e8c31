package com.xylink.sqltranspiler.unit.infrastructure.parser.error;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.infrastructure.parser.error.EnhancedErrorAnalyzer;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorInfo;
import com.xylink.sqltranspiler.infrastructure.parser.error.SqlErrorType;

import lombok.extern.slf4j.Slf4j;

/**
 * 类型转换语法检测测试
 */
@Slf4j
@DisplayName("类型转换语法检测测试")
public class TypeCastDetectionTest {

    @Test
    @DisplayName("测试单独的类型转换语法检测")
    public void testTypeCastOnly() {
        // 只包含类型转换语法的SQL，不包含其他非MySQL语法
        String sql = "SELECT value::text FROM test";
        
        SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "syntax error", 1, 10);
        
        assertNotNull(errorInfo, "错误信息不应为空");
        log.info("错误类型: {}", errorInfo.getErrorType());
        log.info("友好信息: {}", errorInfo.getFriendlyMessage());
        log.info("详细信息: {}", errorInfo.getDetailedMessage());
        log.info("修复建议: {}", errorInfo.getSuggestion());
        
        assertEquals(SqlErrorType.POSTGRESQL_SYNTAX_ERROR, errorInfo.getErrorType(), "应该识别为PostgreSQL语法错误");
        assertTrue(errorInfo.getFriendlyMessage().contains("::"), "应该包含::相关信息");
    }

    @Test
    @DisplayName("测试多个类型转换语法")
    public void testMultipleTypeCasts() {
        String[] testCases = {
            "SELECT id::text FROM test",
            "SELECT created_at::timestamp FROM test", 
            "SELECT count::int FROM test",
            "SELECT price::decimal FROM test"
        };
        
        for (String sql : testCases) {
            log.info("测试SQL: {}", sql);
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(sql, "syntax error", 1, 10);
            
            log.info("  错误类型: {}", errorInfo.getErrorType());
            log.info("  友好信息: {}", errorInfo.getFriendlyMessage());
            
            if (errorInfo.getErrorType() == SqlErrorType.POSTGRESQL_SYNTAX_ERROR && 
                errorInfo.getFriendlyMessage().contains("::")) {
                log.info("  ✓ 正确检测到类型转换语法错误");
            } else {
                log.warn("  ✗ 未能正确检测到类型转换语法错误");
            }
        }
    }
}
