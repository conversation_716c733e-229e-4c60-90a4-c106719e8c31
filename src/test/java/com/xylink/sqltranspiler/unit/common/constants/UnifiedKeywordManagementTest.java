package com.xylink.sqltranspiler.unit.common.constants;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Set;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.common.constants.ReservedWords;
import com.xylink.sqltranspiler.common.constants.SqlKeywords;

/**
 * 统一关键字管理系统测试
 * 
 * 验证ReservedWords和SqlKeywords类的统一管理功能
 * 确保各个数据库方言使用统一的关键字定义，避免重复维护
 */
@DisplayName("统一关键字管理系统测试")
public class UnifiedKeywordManagementTest {

    @Test
    @DisplayName("测试保留字统一管理")
    public void testReservedWordsUnifiedManagement() {
        // 测试各数据库的保留字管理
        assertTrue(ReservedWords.isReservedWord("SELECT", "mysql"));
        assertTrue(ReservedWords.isReservedWord("SELECT", "dameng"));
        assertTrue(ReservedWords.isReservedWord("SELECT", "kingbase"));
        assertTrue(ReservedWords.isReservedWord("SELECT", "shentong"));
        
        // 测试达梦特有的保留字
        assertTrue(ReservedWords.isReservedWord("TYPE", "dameng"));
        assertTrue(ReservedWords.isReservedWord("CLASS", "dameng"));
        assertTrue(ReservedWords.isReservedWord("DOMAIN", "dameng"));
        
        // 测试非保留字
        assertFalse(ReservedWords.isReservedWord("user_name", "mysql"));
        assertFalse(ReservedWords.isReservedWord("custom_field", "dameng"));
    }

    @Test
    @DisplayName("测试SQL关键字统一管理")
    public void testSqlKeywordsUnifiedManagement() {
        // 测试基础SQL关键字
        assertTrue(SqlKeywords.isSqlKeyword("SELECT"));
        assertTrue(SqlKeywords.isSqlKeyword("FROM"));
        assertTrue(SqlKeywords.isSqlKeyword("WHERE"));
        assertTrue(SqlKeywords.isSqlKeyword("ORDER"));
        assertTrue(SqlKeywords.isSqlKeyword("BY"));
        
        // 测试窗口函数关键字
        assertTrue(SqlKeywords.isWindowFunctionKeyword("OVER"));
        assertTrue(SqlKeywords.isWindowFunctionKeyword("PARTITION"));
        assertTrue(SqlKeywords.isWindowFunctionKeyword("ROW_NUMBER"));
        assertTrue(SqlKeywords.isWindowFunctionKeyword("RANK"));
        
        // 测试CTE关键字
        assertTrue(SqlKeywords.isCteKeyword("WITH"));
        assertTrue(SqlKeywords.isCteKeyword("RECURSIVE"));
        
        // 测试聚合函数关键字
        assertTrue(SqlKeywords.isAggregateFunctionKeyword("COUNT"));
        assertTrue(SqlKeywords.isAggregateFunctionKeyword("SUM"));
        assertTrue(SqlKeywords.isAggregateFunctionKeyword("AVG"));
        
        // 测试日期时间函数关键字
        assertTrue(SqlKeywords.isDatetimeFunctionKeyword("NOW"));
        assertTrue(SqlKeywords.isDatetimeFunctionKeyword("SYSDATE"));
        assertTrue(SqlKeywords.isDatetimeFunctionKeyword("DATE_ADD"));
        
        // 测试非关键字
        assertFalse(SqlKeywords.isSqlKeyword("user_name"));
        assertFalse(SqlKeywords.isSqlKeyword("custom_field"));
    }

    @Test
    @DisplayName("测试关键字集合的完整性")
    public void testKeywordSetsCompleteness() {
        // 测试各个关键字集合不为空
        assertFalse(SqlKeywords.getBasicKeywords().isEmpty());
        assertFalse(SqlKeywords.getWindowFunctionKeywords().isEmpty());
        assertFalse(SqlKeywords.getCteKeywords().isEmpty());
        assertFalse(SqlKeywords.getAggregateFunctionKeywords().isEmpty());
        assertFalse(SqlKeywords.getDatetimeFunctionKeywords().isEmpty());
        
        // 测试所有关键字集合包含各个子集合
        Set<String> allKeywords = SqlKeywords.getAllKeywords();
        assertTrue(allKeywords.containsAll(SqlKeywords.getBasicKeywords()));
        assertTrue(allKeywords.containsAll(SqlKeywords.getWindowFunctionKeywords()));
        assertTrue(allKeywords.containsAll(SqlKeywords.getCteKeywords()));
        assertTrue(allKeywords.containsAll(SqlKeywords.getAggregateFunctionKeywords()));
        assertTrue(allKeywords.containsAll(SqlKeywords.getDatetimeFunctionKeywords()));
    }

    @Test
    @DisplayName("测试保留字和关键字的区别")
    public void testDifferenceBetweenReservedWordsAndKeywords() {
        // 保留字主要用于标识符引用判断
        // SQL关键字主要用于SQL格式化和语法分析
        
        // 某些词既是保留字也是关键字
        assertTrue(ReservedWords.isReservedWord("SELECT", "mysql"));
        assertTrue(SqlKeywords.isSqlKeyword("SELECT"));
        
        // 某些词是关键字但不一定是所有数据库的保留字
        assertTrue(SqlKeywords.isSqlKeyword("OVER"));
        // OVER可能不是所有数据库的保留字，但是SQL语法关键字
        
        // 验证关键字数量合理
        assertTrue(SqlKeywords.getAllKeywords().size() > 50);
        assertTrue(SqlKeywords.getAllKeywords().size() < 500);
    }

    @Test
    @DisplayName("测试大小写不敏感")
    public void testCaseInsensitive() {
        // 测试保留字大小写不敏感
        assertTrue(ReservedWords.isReservedWord("select", "mysql"));
        assertTrue(ReservedWords.isReservedWord("SELECT", "mysql"));
        assertTrue(ReservedWords.isReservedWord("Select", "mysql"));
        
        // 测试SQL关键字大小写不敏感
        assertTrue(SqlKeywords.isSqlKeyword("select"));
        assertTrue(SqlKeywords.isSqlKeyword("SELECT"));
        assertTrue(SqlKeywords.isSqlKeyword("Select"));
    }

    @Test
    @DisplayName("测试空值和无效输入处理")
    public void testNullAndInvalidInputHandling() {
        // 测试保留字空值处理
        assertFalse(ReservedWords.isReservedWord(null, "mysql"));
        assertFalse(ReservedWords.isReservedWord("", "mysql"));
        assertFalse(ReservedWords.isReservedWord("SELECT", null));
        assertFalse(ReservedWords.isReservedWord("SELECT", ""));
        
        // 测试SQL关键字空值处理
        assertFalse(SqlKeywords.isSqlKeyword(null));
        assertFalse(SqlKeywords.isSqlKeyword(""));
        
        // 测试无效数据库类型
        assertFalse(ReservedWords.isReservedWord("SELECT", "invalid_db"));
    }

    @Test
    @DisplayName("测试各数据库保留字数量合理性")
    public void testReservedWordCountsReasonable() {
        var counts = ReservedWords.getReservedWordCounts();
        
        // 验证各数据库都有保留字定义
        assertTrue(counts.containsKey("MySQL"));
        assertTrue(counts.containsKey("Dameng"));
        assertTrue(counts.containsKey("Kingbase"));
        assertTrue(counts.containsKey("Shentong"));
        assertTrue(counts.containsKey("Common"));
        
        // 验证保留字数量在合理范围内
        for (String db : counts.keySet()) {
            int count = counts.get(db);
            assertTrue(count > 10, db + " should have more than 10 reserved words");
            assertTrue(count < 1000, db + " should have less than 1000 reserved words");
        }
        
        // MySQL作为源数据库，保留字数量应该比较多
        assertTrue(counts.get("MySQL") > 50);
    }

    @Test
    @DisplayName("测试达梦数据库特有保留字")
    public void testDamengSpecificReservedWords() {
        // 基于达梦官方文档的特有保留字
        // https://eco.dameng.com/document/dm/zh-cn/faq/faq-errorcode.html
        
        String[] damengSpecificWords = {"TYPE", "CLASS", "DOMAIN", "VERIFY", "REFERENCES", "OFFSET"};
        
        for (String word : damengSpecificWords) {
            assertTrue(ReservedWords.isReservedWord(word, "dameng"), 
                      "达梦数据库应该将 " + word + " 识别为保留字");
        }
    }

    @Test
    @DisplayName("测试关键字集合的不可修改性")
    public void testKeywordSetsImmutability() {
        // 测试返回的集合是不可修改的
        Set<String> basicKeywords = SqlKeywords.getBasicKeywords();
        assertThrows(UnsupportedOperationException.class, () -> {
            basicKeywords.add("NEW_KEYWORD");
        });
        
        Set<String> allKeywords = SqlKeywords.getAllKeywords();
        assertThrows(UnsupportedOperationException.class, () -> {
            allKeywords.clear();
        });
        
        Set<String> mysqlReservedWords = ReservedWords.getReservedWords("mysql");
        assertThrows(UnsupportedOperationException.class, () -> {
            mysqlReservedWords.add("NEW_RESERVED_WORD");
        });
    }

    @Test
    @DisplayName("测试统一管理的好处验证")
    public void testUnifiedManagementBenefits() {
        // 验证统一管理避免了重复定义
        // 通过检查关键字定义的一致性来验证
        
        // 基础SQL关键字应该在所有数据库中都被识别为保留字
        String[] basicSqlKeywords = {"SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE"};
        
        for (String keyword : basicSqlKeywords) {
            assertTrue(SqlKeywords.isSqlKeyword(keyword), 
                      keyword + " 应该被识别为SQL关键字");
            
            // 这些基础关键字在所有数据库中都应该是保留字
            assertTrue(ReservedWords.isReservedWord(keyword, "mysql"), 
                      keyword + " 应该是MySQL保留字");
            assertTrue(ReservedWords.isReservedWord(keyword, "dameng"), 
                      keyword + " 应该是达梦保留字");
            assertTrue(ReservedWords.isReservedWord(keyword, "kingbase"), 
                      keyword + " 应该是金仓保留字");
            assertTrue(ReservedWords.isReservedWord(keyword, "shentong"), 
                      keyword + " 应该是神通保留字");
        }
    }
}
