package com.xylink.sqltranspiler.unit.core.validation;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.DefaultStatement;
import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.dml.InsertTable;
import com.xylink.sqltranspiler.core.ast.dml.UpdateTable;
import com.xylink.sqltranspiler.core.ast.dml.ValuesClause;
import com.xylink.sqltranspiler.core.ast.table.ColumnDefType;
import com.xylink.sqltranspiler.core.ast.table.ColumnRel;
import com.xylink.sqltranspiler.core.model.enums.StatementType;
import com.xylink.sqltranspiler.core.model.enums.TableType;
import com.xylink.sqltranspiler.core.validation.BatchValidator;
import com.xylink.sqltranspiler.core.validation.StagedVarcharValidator;
import com.xylink.sqltranspiler.core.validation.TableSchema;
import com.xylink.sqltranspiler.core.validation.TableSchemaBuilder;
import com.xylink.sqltranspiler.core.validation.ValidationResult;
import com.xylink.sqltranspiler.core.validation.ValidationSummary;
import com.xylink.sqltranspiler.core.validation.VarcharConflict;
import com.xylink.sqltranspiler.core.validation.VarcharValidationConfig;

/**
 * 分阶段VARCHAR验证器测试
 */
class StagedVarcharValidatorTest {
    
    private StagedVarcharValidator validator;
    
    @BeforeEach
    void setUp() {
        validator = new StagedVarcharValidator();
    }
    
    @Test
    @DisplayName("默认配置应该是禁用状态")
    void testDefaultConfigDisabled() {
        assertFalse(validator.isEnabled(), "默认应该是禁用状态");
        
        List<Statement> statements = createTestStatements();
        ValidationSummary summary = validator.validate(statements);
        
        assertFalse(summary.isEnabled(), "验证摘要应该显示禁用状态");
        assertEquals(0, summary.getTotalValidations(), "禁用时不应该进行验证");
        assertEquals(0, summary.getConflictCount(), "禁用时不应该有冲突");
    }
    
    @Test
    @DisplayName("启用配置后应该能够进行验证")
    void testEnabledConfig() {
        // 启用验证器
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        validator.configure(config);
        
        assertTrue(validator.isEnabled(), "配置后应该是启用状态");
        
        List<Statement> statements = createTestStatements();
        ValidationSummary summary = validator.validate(statements);
        
        assertTrue(summary.isEnabled(), "验证摘要应该显示启用状态");
        assertNotNull(summary, "应该返回验证摘要");
        assertTrue(summary.getValidationTimeMs() >= 0, "验证时间应该非负");
    }
    
    @Test
    @DisplayName("空语句列表应该返回空摘要")
    void testEmptyStatements() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        validator.configure(config);
        
        ValidationSummary summary = validator.validate(new ArrayList<>());
        
        assertTrue(summary.isEnabled());
        assertEquals(0, summary.getTotalValidations());
        assertEquals(0, summary.getConflictCount());
        assertFalse(summary.hasConflicts());
    }
    
    @Test
    @DisplayName("null语句列表应该返回空摘要")
    void testNullStatements() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        validator.configure(config);
        
        ValidationSummary summary = validator.validate(null);
        
        assertTrue(summary.isEnabled());
        assertEquals(0, summary.getTotalValidations());
        assertEquals(0, summary.getConflictCount());
    }
    
    @Test
    @DisplayName("验证CREATE TABLE语句的schema收集")
    void testCreateTableSchemaCollection() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        validator.configure(config);
        
        List<Statement> statements = createTestStatements();
        ValidationSummary summary = validator.validate(statements);
        
        // 验证基本信息
        assertTrue(summary.isEnabled());
        assertNotNull(summary);
        
        // 打印摘要信息用于调试
        System.out.println("Validation Summary: " + summary.getBriefReport());
    }
    
    @Test
    @DisplayName("验证单个CREATE TABLE语句")
    void testSingleCreateTable() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        validator.configure(config);
        
        CreateTable createTable = createTestCreateTable();
        TableSchema schema = validator.validateCreateTable(createTable);
        
        assertNotNull(schema, "应该返回表结构");
        assertEquals("test_table", schema.getTableName());
        assertTrue(schema.hasVarcharColumns(), "应该包含VARCHAR列");
        assertEquals(2, schema.getVarcharColumnCount(), "应该有2个VARCHAR列");
        
        // 验证具体列
        assertTrue(schema.hasVarcharColumn("name"));
        assertTrue(schema.hasVarcharColumn("description"));
        
        TableSchema.VarcharColumn nameColumn = schema.getVarcharColumn("name");
        assertNotNull(nameColumn);
        assertEquals("name", nameColumn.getColumnName());
        assertEquals(50, nameColumn.getMaxLength());
        
        TableSchema.VarcharColumn descColumn = schema.getVarcharColumn("description");
        assertNotNull(descColumn);
        assertEquals("description", descColumn.getColumnName());
        assertEquals(200, descColumn.getMaxLength());
    }
    
    @Test
    @DisplayName("配置不同的冲突处理策略")
    void testDifferentConflictStrategies() {
        // 测试LOG_ONLY策略
        VarcharValidationConfig logOnlyConfig = VarcharValidationConfig.enabledConfig();
        logOnlyConfig.setStrategy(VarcharValidationConfig.ConflictHandlingStrategy.LOG_ONLY);
        validator.configure(logOnlyConfig);

        assertEquals(VarcharValidationConfig.ConflictHandlingStrategy.LOG_ONLY,
                    validator.getConfig().getStrategy());

        // 测试FAIL_FAST策略
        VarcharValidationConfig failFastConfig = VarcharValidationConfig.enabledConfig();
        failFastConfig.setStrategy(VarcharValidationConfig.ConflictHandlingStrategy.FAIL_FAST);
        validator.configure(failFastConfig);

        assertEquals(VarcharValidationConfig.ConflictHandlingStrategy.FAIL_FAST,
                    validator.getConfig().getStrategy());
    }

    @Test
    @DisplayName("验证单个INSERT语句的VARCHAR长度冲突")
    void testSingleInsertStatementValidation() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        validator.configure(config);

        // 创建表结构
        Map<String, TableSchema> schemas = createTestSchemas();

        // 创建正常的INSERT语句（无冲突）
        InsertTable normalInsert = createNormalInsertStatement();
        ValidationResult normalResult = validator.validateSingleStatement(normalInsert, schemas);
        assertNull(normalResult, "正常INSERT语句不应该有冲突");

        // 创建有冲突的INSERT语句
        InsertTable conflictInsert = createConflictInsertStatement();
        ValidationResult conflictResult = validator.validateSingleStatement(conflictInsert, schemas);
        assertNotNull(conflictResult, "有冲突的INSERT语句应该返回验证结果");
        assertTrue(conflictResult.hasConflicts(), "应该检测到冲突");
        assertEquals("test_table", conflictResult.getTableName());
        assertTrue(conflictResult.getConflictCount() > 0, "应该有至少一个冲突");

        // 验证冲突详情
        List<VarcharConflict> conflicts = conflictResult.getConflicts();
        VarcharConflict firstConflict = conflicts.get(0);
        assertEquals("test_table", firstConflict.getTableName());
        assertEquals("name", firstConflict.getColumnName());
        assertEquals(50, firstConflict.getDefinedLength());
        assertTrue(firstConflict.getActualLength() > 50, "实际长度应该超过定义长度");
    }

    @Test
    @DisplayName("验证单个UPDATE语句的VARCHAR长度冲突")
    void testSingleUpdateStatementValidation() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        validator.configure(config);

        // 创建表结构
        Map<String, TableSchema> schemas = createTestSchemas();

        // 创建正常的UPDATE语句（无冲突）
        UpdateTable normalUpdate = createNormalUpdateStatement();
        ValidationResult normalResult = validator.validateSingleStatement(normalUpdate, schemas);
        assertNull(normalResult, "正常UPDATE语句不应该有冲突");

        // 创建有冲突的UPDATE语句
        UpdateTable conflictUpdate = createConflictUpdateStatement();
        ValidationResult conflictResult = validator.validateSingleStatement(conflictUpdate, schemas);
        assertNotNull(conflictResult, "有冲突的UPDATE语句应该返回验证结果");
        assertTrue(conflictResult.hasConflicts(), "应该检测到冲突");
        assertEquals("test_table", conflictResult.getTableName());

        // 验证冲突详情
        List<VarcharConflict> conflicts = conflictResult.getConflicts();
        VarcharConflict firstConflict = conflicts.get(0);
        assertEquals("test_table", firstConflict.getTableName());
        assertEquals("description", firstConflict.getColumnName());
        assertEquals(200, firstConflict.getDefinedLength());
        assertTrue(firstConflict.getActualLength() > 200, "实际长度应该超过定义长度");
    }

    @Test
    @DisplayName("验证不支持的语句类型")
    void testUnsupportedStatementTypes() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        validator.configure(config);

        Map<String, TableSchema> schemas = createTestSchemas();

        // 测试CREATE TABLE语句（不需要验证）
        CreateTable createTable = createTestCreateTable();
        ValidationResult result = validator.validateSingleStatement(createTable, schemas);
        assertNull(result, "CREATE TABLE语句不需要VARCHAR长度验证");

        // 测试其他类型的语句
        Statement otherStatement = new DefaultStatement(StatementType.SELECT);
        ValidationResult otherResult = validator.validateSingleStatement(otherStatement, schemas);
        assertNull(otherResult, "其他类型语句不需要VARCHAR长度验证");
    }

    @Test
    @DisplayName("验证禁用状态下的单个语句验证")
    void testSingleStatementValidationWhenDisabled() {
        // 使用默认配置（禁用状态）
        assertFalse(validator.isEnabled(), "默认应该是禁用状态");

        Map<String, TableSchema> schemas = createTestSchemas();
        InsertTable insertStatement = createConflictInsertStatement();

        ValidationResult result = validator.validateSingleStatement(insertStatement, schemas);
        assertNull(result, "禁用状态下不应该进行验证");
    }

    @Test
    @DisplayName("验证BatchValidator与单个语句验证的集成")
    void testBatchValidatorIntegration() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        validator.configure(config);

        Map<String, TableSchema> schemas = createTestSchemas();
        BatchValidator batchValidator = new BatchValidator(schemas, config);

        // 验证正常语句
        InsertTable normalInsert = createNormalInsertStatement();
        batchValidator.validateStatement(normalInsert);

        // 验证有冲突的语句
        InsertTable conflictInsert = createConflictInsertStatement();
        batchValidator.validateStatement(conflictInsert);

        UpdateTable conflictUpdate = createConflictUpdateStatement();
        batchValidator.validateStatement(conflictUpdate);

        // 获取验证摘要
        ValidationSummary summary = batchValidator.getSummary(100);

        assertTrue(summary.isEnabled(), "验证应该是启用状态");
        assertEquals(3, summary.getTotalValidations(), "应该验证了3个语句");
        assertEquals(2, summary.getConflictCount(), "应该有2个冲突");

        // 验证结果详情
        List<ValidationResult> results = batchValidator.getResults();
        assertEquals(2, results.size(), "应该有2个验证结果（只有冲突的语句会产生结果）");

        // 验证行号信息
        ValidationResult firstResult = results.get(0);
        assertEquals(2, firstResult.getStatementLineNumber(), "第一个冲突语句应该在第2行");

        ValidationResult secondResult = results.get(1);
        assertEquals(3, secondResult.getStatementLineNumber(), "第二个冲突语句应该在第3行");

        System.out.println("BatchValidator验证摘要: " + summary.getBriefReport());
    }
    
    /**
     * 创建测试用的语句列表
     */
    private List<Statement> createTestStatements() {
        List<Statement> statements = new ArrayList<>();
        statements.add(createTestCreateTable());
        return statements;
    }
    
    /**
     * 创建测试用的CREATE TABLE语句
     */
    private CreateTable createTestCreateTable() {
        TableId tableId = new TableId("test_table");
        
        List<ColumnRel> columns = new ArrayList<>();
        
        // 添加VARCHAR列
        ColumnRel nameColumn = new ColumnRel("name", "VARCHAR", "Name column", ColumnDefType.COMPUTED);
        nameColumn.setColumnLength(50);
        columns.add(nameColumn);
        
        ColumnRel descColumn = new ColumnRel("description", "VARCHAR", "Description column", ColumnDefType.COMPUTED);
        descColumn.setColumnLength(200);
        columns.add(descColumn);
        
        // 添加非VARCHAR列
        ColumnRel idColumn = new ColumnRel("id", "INT", "ID column", ColumnDefType.COMPUTED);
        columns.add(idColumn);
        
        return new CreateTable(
            tableId,
            TableType.MYSQL,
            "Test table",
            null, // lifeCycle
            null, // partitionColumnRels
            columns,
            null, // properties
            null, // fileFormat
            false, // ifNotExists
            false, // external
            false, // temporary
            null, // location
            null, // querySql
            new ArrayList<>() // partitionColumnNames
        );
    }

    /**
     * 创建测试用的表结构映射
     */
    private Map<String, TableSchema> createTestSchemas() {
        Map<String, TableSchema> schemas = new HashMap<>();

        // 创建test_table的schema
        TableSchemaBuilder builder = new TableSchemaBuilder("test_table");
        builder.addVarcharColumn("name", 50)
               .addVarcharColumn("description", 200);

        schemas.put("test_table", builder.build());
        return schemas;
    }

    /**
     * 创建正常的INSERT语句（无冲突）
     */
    private InsertTable createNormalInsertStatement() {
        TableId tableId = new TableId("test_table");
        List<String> columns = Arrays.asList("name", "description");

        // 创建VALUES子句，数据长度在限制范围内
        List<List<String>> rows = new ArrayList<>();
        rows.add(Arrays.asList("'Short Name'", "'Short description'"));
        ValuesClause valuesClause = new ValuesClause(rows);

        InsertTable insertTable = new InsertTable(tableId, columns, valuesClause);
        insertTable.setSql("INSERT INTO test_table (name, description) VALUES ('Short Name', 'Short description')");

        return insertTable;
    }

    /**
     * 创建有冲突的INSERT语句
     */
    private InsertTable createConflictInsertStatement() {
        TableId tableId = new TableId("test_table");
        List<String> columns = Arrays.asList("name", "description");

        // 创建VALUES子句，name字段超过50字符限制
        String longName = "This is a very long name that exceeds the 50 character limit for the name column";
        List<List<String>> rows = new ArrayList<>();
        rows.add(Arrays.asList("'" + longName + "'", "'Normal description'"));
        ValuesClause valuesClause = new ValuesClause(rows);

        InsertTable insertTable = new InsertTable(tableId, columns, valuesClause);
        insertTable.setSql("INSERT INTO test_table (name, description) VALUES ('" + longName + "', 'Normal description')");

        return insertTable;
    }

    /**
     * 创建正常的UPDATE语句（无冲突）
     */
    private UpdateTable createNormalUpdateStatement() {
        TableId tableId = new TableId("test_table");

        List<UpdateTable.SetClause> setClauses = new ArrayList<>();
        setClauses.add(new UpdateTable.SetClause("name", "'Updated Name'"));
        setClauses.add(new UpdateTable.SetClause("description", "'Updated description'"));

        UpdateTable updateTable = new UpdateTable(tableId, setClauses, "id = 1");
        updateTable.setSql("UPDATE test_table SET name = 'Updated Name', description = 'Updated description' WHERE id = 1");

        return updateTable;
    }

    /**
     * 创建有冲突的UPDATE语句
     */
    private UpdateTable createConflictUpdateStatement() {
        TableId tableId = new TableId("test_table");

        // 创建description字段超过200字符限制的SET子句
        String longDescription = "This is a very long description that exceeds the 200 character limit for the description column. " +
                               "It contains a lot of text to make sure it goes over the limit and triggers a VARCHAR length conflict. " +
                               "This should definitely be longer than 200 characters to test the validation properly.";

        List<UpdateTable.SetClause> setClauses = new ArrayList<>();
        setClauses.add(new UpdateTable.SetClause("description", "'" + longDescription + "'"));

        UpdateTable updateTable = new UpdateTable(tableId, setClauses, "id = 1");
        updateTable.setSql("UPDATE test_table SET description = '" + longDescription + "' WHERE id = 1");

        return updateTable;
    }
}
