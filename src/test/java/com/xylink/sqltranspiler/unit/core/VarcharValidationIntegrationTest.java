package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.validation.EnhancedTranspilationResult;
import com.xylink.sqltranspiler.core.validation.EnhancedTranspiler;
import com.xylink.sqltranspiler.core.validation.ValidationSummary;
import com.xylink.sqltranspiler.core.validation.VarcharValidationConfig;

/**
 * VARCHAR验证功能集成测试
 * 
 * 测试增强转换器的VARCHAR长度验证功能
 */
class VarcharValidationIntegrationTest {
    
    private EnhancedTranspiler transpiler;
    
    @BeforeEach
    void setUp() {
        transpiler = new EnhancedTranspiler();
    }
    
    @Test
    @DisplayName("基础使用 - 禁用验证（默认行为）")
    void testBasicUsageDisabled() {
        String sql = """
            CREATE TABLE users (
                id INT PRIMARY KEY,
                name VARCHAR(50),
                email VARCHAR(100)
            );
            """;
        
        EnhancedTranspilationResult result = transpiler.transpileEnhanced(sql, "mysql", "dameng");
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertFalse(result.getValidationSummary().isEnabled(), "VARCHAR验证应该默认禁用");
        assertNotNull(result.getTranslatedSql(), "应该有转换结果");
        assertFalse(result.getTranslatedSql().isEmpty(), "转换结果不应为空");
    }
    
    @Test
    @DisplayName("启用验证 - LOG_ONLY策略")
    void testEnabledValidationLogOnly() {
        // 配置VARCHAR验证
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        config.setStrategy(VarcharValidationConfig.ConflictHandlingStrategy.LOG_ONLY);
        config.setDetailedReporting(true);
        transpiler.configureVarcharValidation(config);
        
        String sql = """
            CREATE TABLE products (
                id INT PRIMARY KEY,
                name VARCHAR(50),
                description VARCHAR(200)
            );
            
            CREATE TABLE orders (
                id INT PRIMARY KEY,
                product_id INT,
                customer_name VARCHAR(100)
            );
            """;
        
        EnhancedTranspilationResult result = transpiler.transpileEnhanced(sql, "mysql", "dameng");
        
        assertTrue(result.isSuccess(), "转换应该成功");
        assertTrue(result.getValidationSummary().isEnabled(), "VARCHAR验证应该启用");
        assertNotNull(result.getValidationSummary().getBriefReport(), "应该有验证统计");
        assertNotNull(result.getDetailedReport(), "应该有详细报告");
    }
    
    @Test
    @DisplayName("详细配置测试")
    void testDetailedConfiguration() {
        // 创建详细配置
        VarcharValidationConfig config = new VarcharValidationConfig();
        config.setEnabled(true);
        config.setStrategy(VarcharValidationConfig.ConflictHandlingStrategy.LOG_ONLY);
        config.setDetailedReporting(true);
        config.setMaxReportedConflicts(50);
        config.setEnablePerformanceMetrics(true);
        
        transpiler.configureVarcharValidation(config);
        
        String sql = """
            CREATE TABLE test_table (
                short_field VARCHAR(10),
                medium_field VARCHAR(100),
                long_field VARCHAR(500)
            );
            """;
        
        EnhancedTranspilationResult result = transpiler.transpileEnhanced(sql, "mysql", "dameng");
        
        ValidationSummary summary = result.getValidationSummary();
        assertTrue(summary.isEnabled(), "验证应该启用");
        assertTrue(summary.getTotalValidations() >= 0, "总验证数应该非负");
        assertTrue(summary.getConflictCount() >= 0, "冲突数应该非负");
        assertTrue(summary.getValidationTimeMs() >= 0, "验证时间应该非负");
        assertTrue(summary.getAffectedTableCount() >= 0, "受影响表数应该非负");
    }
    
    @Test
    @DisplayName("兼容性使用（与原始转换器API兼容）")
    void testCompatibilityUsage() {
        String sql = """
            CREATE TABLE legacy_table (
                id INT PRIMARY KEY,
                data VARCHAR(255)
            );
            """;
        
        // 使用兼容性API（不启用VARCHAR验证）
        var result = transpiler.transpile(sql, "mysql", "dameng");
        
        assertTrue(result.successCount() >= 0, "成功数应该非负");
        assertTrue(result.failureCount() >= 0, "失败数应该非负");
        assertNotNull(result.issues(), "问题列表不应为null");
        assertNotNull(result.translatedSql(), "转换结果不应为null");
    }
    
    @Test
    @DisplayName("LOG_ONLY策略测试")
    void testLogOnlyStrategy() {
        String sql = """
            CREATE TABLE test (
                id INT PRIMARY KEY,
                short_name VARCHAR(5)
            );
            """;
        
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        config.setStrategy(VarcharValidationConfig.ConflictHandlingStrategy.LOG_ONLY);
        transpiler.configureVarcharValidation(config);
        
        assertDoesNotThrow(() -> {
            EnhancedTranspilationResult result = transpiler.transpileEnhanced(sql, "mysql", "dameng");
            assertTrue(result.isSuccess(), "LOG_ONLY策略应该不会导致转换失败");
        });
    }
    
    @Test
    @DisplayName("TRUNCATE_WITH_WARNING策略测试")
    void testTruncateWithWarningStrategy() {
        String sql = """
            CREATE TABLE test (
                id INT PRIMARY KEY,
                short_name VARCHAR(5)
            );
            """;
        
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        config.setStrategy(VarcharValidationConfig.ConflictHandlingStrategy.TRUNCATE_WITH_WARNING);
        transpiler.configureVarcharValidation(config);
        
        assertDoesNotThrow(() -> {
            EnhancedTranspilationResult result = transpiler.transpileEnhanced(sql, "mysql", "dameng");
            // TRUNCATE_WITH_WARNING策略可能成功也可能失败，取决于具体实现
            assertNotNull(result, "结果不应为null");
        });
    }
    
    @Test
    @DisplayName("FAIL_FAST策略测试")
    void testFailFastStrategy() {
        String sql = """
            CREATE TABLE test (
                id INT PRIMARY KEY,
                short_name VARCHAR(5)
            );
            """;
        
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        config.setStrategy(VarcharValidationConfig.ConflictHandlingStrategy.FAIL_FAST);
        transpiler.configureVarcharValidation(config);
        
        // FAIL_FAST策略可能抛出异常或返回失败结果
        assertDoesNotThrow(() -> {
            try {
                EnhancedTranspilationResult result = transpiler.transpileEnhanced(sql, "mysql", "dameng");
                // 如果没有抛出异常，检查结果
                assertNotNull(result, "结果不应为null");
            } catch (Exception e) {
                // FAIL_FAST策略可能抛出异常，这是预期行为
                assertNotNull(e.getMessage(), "异常消息不应为null");
            }
        });
    }
    
    @Test
    @DisplayName("配置对象测试")
    void testConfigurationObject() {
        VarcharValidationConfig config = new VarcharValidationConfig();
        
        // 测试默认值
        assertFalse(config.isEnabled(), "默认应该禁用");
        assertEquals(VarcharValidationConfig.ConflictHandlingStrategy.LOG_ONLY,
                    config.getStrategy(), "默认策略应该是LOG_ONLY");
        assertTrue(config.isDetailedReporting(), "默认启用详细报告");
        assertEquals(100, config.getMaxReportedConflicts(), "默认最大报告冲突数应该是100");
        assertFalse(config.isEnablePerformanceMetrics(), "默认不启用性能指标");
        
        // 测试设置值
        config.setEnabled(true);
        config.setStrategy(VarcharValidationConfig.ConflictHandlingStrategy.FAIL_FAST);
        config.setDetailedReporting(true);
        config.setMaxReportedConflicts(100);
        config.setEnablePerformanceMetrics(true);
        
        assertTrue(config.isEnabled(), "应该启用");
        assertEquals(VarcharValidationConfig.ConflictHandlingStrategy.FAIL_FAST, 
                    config.getStrategy(), "策略应该是FAIL_FAST");
        assertTrue(config.isDetailedReporting(), "应该启用详细报告");
        assertEquals(100, config.getMaxReportedConflicts(), "最大报告冲突数应该是100");
        assertTrue(config.isEnablePerformanceMetrics(), "应该启用性能指标");
    }
    
    @Test
    @DisplayName("启用配置工厂方法测试")
    void testEnabledConfigFactory() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        
        assertTrue(config.isEnabled(), "工厂方法创建的配置应该启用");
        assertNotNull(config.getStrategy(), "策略不应为null");
    }
    
    @Test
    @DisplayName("验证摘要信息测试")
    void testValidationSummary() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        config.setEnablePerformanceMetrics(true);
        transpiler.configureVarcharValidation(config);
        
        String sql = """
            CREATE TABLE test_summary (
                id INT PRIMARY KEY,
                name VARCHAR(100),
                description VARCHAR(500)
            );
            """;
        
        EnhancedTranspilationResult result = transpiler.transpileEnhanced(sql, "mysql", "dameng");
        ValidationSummary summary = result.getValidationSummary();
        
        assertNotNull(summary, "验证摘要不应为null");
        assertTrue(summary.isEnabled(), "验证应该启用");
        assertNotNull(summary.getBriefReport(), "简要报告不应为null");
        assertTrue(summary.getTotalValidations() >= 0, "总验证数应该非负");
        assertTrue(summary.getValidationTimeMs() >= 0, "验证时间应该非负");
    }
    
    @Test
    @DisplayName("空SQL处理测试")
    void testEmptySqlHandling() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        transpiler.configureVarcharValidation(config);
        
        // 测试空字符串
        EnhancedTranspilationResult emptyResult = transpiler.transpileEnhanced("", "mysql", "dameng");
        assertNotNull(emptyResult, "空SQL结果不应为null");
        
        // 测试只有空白字符的SQL
        EnhancedTranspilationResult whitespaceResult = transpiler.transpileEnhanced("   \n\t  ", "mysql", "dameng");
        assertNotNull(whitespaceResult, "空白SQL结果不应为null");
    }
    
    @Test
    @DisplayName("多表SQL验证测试")
    void testMultiTableSqlValidation() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        config.setDetailedReporting(true);
        transpiler.configureVarcharValidation(config);
        
        String sql = """
            CREATE TABLE users (
                id INT PRIMARY KEY,
                username VARCHAR(50),
                email VARCHAR(100)
            );
            
            CREATE TABLE posts (
                id INT PRIMARY KEY,
                user_id INT,
                title VARCHAR(200),
                content TEXT
            );
            
            CREATE TABLE comments (
                id INT PRIMARY KEY,
                post_id INT,
                author_name VARCHAR(100),
                comment_text VARCHAR(1000)
            );
            """;
        
        EnhancedTranspilationResult result = transpiler.transpileEnhanced(sql, "mysql", "dameng");
        
        assertTrue(result.isSuccess(), "多表SQL转换应该成功");
        ValidationSummary summary = result.getValidationSummary();
        assertTrue(summary.getAffectedTableCount() >= 0, "受影响表数应该非负");
        // 注意：实际的表数量取决于具体实现，这里只验证基本功能
    }
}
