package com.xylink.sqltranspiler.unit.core.validation;

import com.xylink.sqltranspiler.core.validation.StrictSqlValidator;
import com.xylink.sqltranspiler.core.validation.StrictValidationResult;

/**
 * 严格SQL验证器演示程序
 *
 * 用于测试和演示基于官方文档的严格SQL验证功能
 */
public class StrictValidatorDemo {
    
    public static void main(String[] args) {
        StrictSqlValidator validator = new StrictSqlValidator();
        
        System.out.println("=".repeat(80));
        System.out.println("严格SQL验证器演示 - 基于官方文档规范");
        System.out.println("=".repeat(80));
        System.out.println();
        
        // 测试1：MySQL典型问题 - bigint默认值使用引号
        testBigintDefaultWithQuotes(validator);
        
        // 测试2：MySQL典型问题 - ON UPDATE NOW()语法错误
        testOnUpdateNowSyntax(validator);
        
        // 测试3：AUTO_INCREMENT验证
        testAutoIncrementValidation(validator);
        
        // 测试4：字符串默认值验证
        testStringDefaultValidation(validator);
        
        // 测试5：正确的SQL
        testValidSql(validator);
        
        System.out.println("=".repeat(80));
        System.out.println("演示完成");
        System.out.println("=".repeat(80));
    }
    
    private static void testBigintDefaultWithQuotes(StrictSqlValidator validator) {
        System.out.println("测试1：MySQL典型问题 - bigint默认值使用引号");
        System.out.println("-".repeat(50));
        
        String sql = """
            CREATE TABLE contact1.t_en_department_extend (
                id varchar(64) not null,
                dep_id bigint(20) not null,
                field_code varchar(64) not null,
                field_value varchar(256) not null,
                create_time bigint(20) default '0',
                update_time bigint(20) default '0',
                primary key (id)
            );
            """;
        
        System.out.println("SQL:");
        System.out.println(sql);
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        System.out.println("验证结果:");
        System.out.println(result.getConsoleOutput());
        System.out.println();
    }
    
    private static void testOnUpdateNowSyntax(StrictSqlValidator validator) {
        System.out.println("测试2：MySQL典型问题 - ON UPDATE NOW()语法错误");
        System.out.println("-".repeat(50));
        
        String sql = """
            CREATE TABLE test_table (
                id int primary key,
                update_time datetime default now() on update now()
            );
            """;
        
        System.out.println("SQL:");
        System.out.println(sql);
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        System.out.println("验证结果:");
        System.out.println(result.getConsoleOutput());
        System.out.println();
    }
    
    private static void testAutoIncrementValidation(StrictSqlValidator validator) {
        System.out.println("测试3：AUTO_INCREMENT验证");
        System.out.println("-".repeat(50));
        
        String sql = """
            CREATE TABLE test_auto_increment (
                id int auto_increment primary key,
                name varchar(100),
                invalid_auto varchar(50) auto_increment
            );
            """;
        
        System.out.println("SQL:");
        System.out.println(sql);
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        System.out.println("验证结果:");
        System.out.println(result.getConsoleOutput());
        System.out.println();
    }
    
    private static void testStringDefaultValidation(StrictSqlValidator validator) {
        System.out.println("测试4：字符串默认值验证");
        System.out.println("-".repeat(50));
        
        String sql = """
            CREATE TABLE test_string_defaults (
                id int,
                name varchar(100) default test_value,
                description text default 'valid default',
                status varchar(20) default 'active'
            );
            """;
        
        System.out.println("SQL:");
        System.out.println(sql);
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        System.out.println("验证结果:");
        System.out.println(result.getConsoleOutput());
        System.out.println();
    }
    
    private static void testValidSql(StrictSqlValidator validator) {
        System.out.println("测试5：正确的SQL");
        System.out.println("-".repeat(50));
        
        String sql = """
            CREATE TABLE test_valid (
                id int auto_increment primary key,
                count_field bigint default 100,
                price decimal(10,2) default 0.00,
                status tinyint(1) default 1,
                name varchar(100) default 'default_name',
                created_at timestamp default current_timestamp,
                updated_at timestamp default current_timestamp on update current_timestamp
            );
            """;
        
        System.out.println("SQL:");
        System.out.println(sql);
        
        StrictValidationResult result = validator.validate(sql, "mysql", "dameng");
        
        System.out.println("验证结果:");
        System.out.println(result.getConsoleOutput());
        System.out.println();
    }
}
