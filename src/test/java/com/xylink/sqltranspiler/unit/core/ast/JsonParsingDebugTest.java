package com.xylink.sqltranspiler.unit.core.ast;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * JSON类型解析调试测试
 * 根据数据库规则：严格禁止简化测试用例SQL，必须修正解析器以支持JSON类型
 */
public class JsonParsingDebugTest {

    private static final Logger log = LoggerFactory.getLogger(JsonParsingDebugTest.class);

    @Test
    void debugJsonTypeParsing() {
        // 测试最简单的JSON类型解析
        String simpleJsonSql = "CREATE TABLE test (id INT, data JSON);";
        
        log.info("测试简单JSON类型解析: {}", simpleJsonSql);
        
        try {
            Object statement = MySqlHelper.parseStatement(simpleJsonSql);
            log.info("✅ 简单JSON类型解析成功: {}", statement.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("❌ 简单JSON类型解析失败: {}", e.getMessage());
            e.printStackTrace();
        }

        // 测试带反引号的JSON类型解析
        String backtickJsonSql = "CREATE TABLE `test` (`id` INT, `data` JSON);";

        log.info("测试带反引号JSON类型解析: {}", backtickJsonSql);

        try {
            Object statement = MySqlHelper.parseStatement(backtickJsonSql);
            log.info("✅ 带反引号JSON类型解析成功: {}", statement.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("❌ 带反引号JSON类型解析失败: {}", e.getMessage());
            e.printStackTrace();
        }

        // 测试多个JSON列的解析
        String multiJsonSql = "CREATE TABLE test (id INT PRIMARY KEY, json_data JSON, json_array JSON, json_object JSON);";

        log.info("测试多个JSON列解析: {}", multiJsonSql);

        try {
            Object statement = MySqlHelper.parseStatement(multiJsonSql);
            log.info("✅ 多个JSON列解析成功: {}", statement.getClass().getSimpleName());
        } catch (Exception e) {
            log.error("❌ 多个JSON列解析失败: {}", e.getMessage());
            e.printStackTrace();
        }
    }
}
