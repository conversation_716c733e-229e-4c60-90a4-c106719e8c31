package com.xylink.sqltranspiler.unit.core.validation;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.validation.VarcharValidationConfig;

/**
 * VARCHAR验证配置测试
 */
class VarcharValidationConfigTest {
    
    @Test
    @DisplayName("默认配置应该是禁用状态")
    void testDefaultConfig() {
        VarcharValidationConfig config = VarcharValidationConfig.defaultConfig();
        
        assertFalse(config.isEnabled(), "默认配置应该是禁用的");
        assertEquals(VarcharValidationConfig.ConflictHandlingStrategy.LOG_ONLY, 
                    config.getStrategy(), "默认策略应该是LOG_ONLY");
        assertTrue(config.isDetailedReporting(), "默认应该启用详细报告");
        assertEquals(100, config.getMaxReportedConflicts(), "默认最大报告冲突数应该是100");
        assertFalse(config.isEnablePerformanceMetrics(), "默认应该禁用性能指标");
    }
    
    @Test
    @DisplayName("启用配置应该正确设置")
    void testEnabledConfig() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        
        assertTrue(config.isEnabled(), "启用配置应该是启用的");
        assertEquals(VarcharValidationConfig.ConflictHandlingStrategy.LOG_ONLY, 
                    config.getStrategy(), "默认策略应该是LOG_ONLY");
    }
    
    @Test
    @DisplayName("配置属性设置和获取")
    void testConfigProperties() {
        VarcharValidationConfig config = new VarcharValidationConfig();
        
        // 测试enabled属性
        config.setEnabled(true);
        assertTrue(config.isEnabled());
        
        // 测试strategy属性
        config.setStrategy(VarcharValidationConfig.ConflictHandlingStrategy.FAIL_FAST);
        assertEquals(VarcharValidationConfig.ConflictHandlingStrategy.FAIL_FAST, config.getStrategy());
        
        // 测试detailedReporting属性
        config.setDetailedReporting(false);
        assertFalse(config.isDetailedReporting());
        
        // 测试maxReportedConflicts属性
        config.setMaxReportedConflicts(50);
        assertEquals(50, config.getMaxReportedConflicts());
        
        // 测试enablePerformanceMetrics属性
        config.setEnablePerformanceMetrics(true);
        assertTrue(config.isEnablePerformanceMetrics());
    }
    
    @Test
    @DisplayName("toString方法应该包含所有重要信息")
    void testToString() {
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        config.setStrategy(VarcharValidationConfig.ConflictHandlingStrategy.FAIL_FAST);
        config.setDetailedReporting(false);
        config.setMaxReportedConflicts(200);
        config.setEnablePerformanceMetrics(true);
        
        String str = config.toString();
        
        assertTrue(str.contains("enabled=true"));
        assertTrue(str.contains("strategy=FAIL_FAST"));
        assertTrue(str.contains("detailedReporting=false"));
        assertTrue(str.contains("maxReportedConflicts=200"));
        assertTrue(str.contains("enablePerformanceMetrics=true"));
    }
    
    @Test
    @DisplayName("冲突处理策略枚举测试")
    void testConflictHandlingStrategy() {
        // 测试所有策略值
        VarcharValidationConfig.ConflictHandlingStrategy[] strategies = 
            VarcharValidationConfig.ConflictHandlingStrategy.values();
        
        assertEquals(3, strategies.length, "应该有3种策略");
        
        // 验证策略名称
        assertEquals("FAIL_FAST", VarcharValidationConfig.ConflictHandlingStrategy.FAIL_FAST.name());
        assertEquals("TRUNCATE_WITH_WARNING", VarcharValidationConfig.ConflictHandlingStrategy.TRUNCATE_WITH_WARNING.name());
        assertEquals("LOG_ONLY", VarcharValidationConfig.ConflictHandlingStrategy.LOG_ONLY.name());
    }
}
