package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.dml.InsertTable;
import com.xylink.sqltranspiler.core.ast.dml.UpdateTable;
import com.xylink.sqltranspiler.core.ast.dml.ValuesClause;
import com.xylink.sqltranspiler.core.validation.StagedVarcharValidator;
import com.xylink.sqltranspiler.core.validation.TableSchema;
import com.xylink.sqltranspiler.core.validation.TableSchemaBuilder;
import com.xylink.sqltranspiler.core.validation.ValidationResult;
import com.xylink.sqltranspiler.core.validation.VarcharValidationConfig;

/**
 * 单个语句VARCHAR验证测试
 * 
 * 测试StagedVarcharValidator的单个语句验证功能
 */
class SingleStatementValidationTest {
    
    private StagedVarcharValidator validator;
    private Map<String, TableSchema> schemas;
    
    @BeforeEach
    void setUp() {
        validator = new StagedVarcharValidator();
        VarcharValidationConfig config = VarcharValidationConfig.enabledConfig();
        config.setDetailedReporting(true);
        validator.configure(config);
        
        schemas = createTestSchemas();
    }
    
    /**
     * 创建测试用的表结构
     */
    private Map<String, TableSchema> createTestSchemas() {
        Map<String, TableSchema> testSchemas = new HashMap<>();
        
        // 创建user_table的schema
        TableSchemaBuilder builder = new TableSchemaBuilder("user_table");
        builder.addVarcharColumn("username", 50)
               .addVarcharColumn("email", 100)
               .addVarcharColumn("description", 200);
        
        testSchemas.put("user_table", builder.build());
        return testSchemas;
    }
    
    @Test
    @DisplayName("正常的INSERT语句验证通过")
    void testNormalInsertStatementValidation() {
        InsertTable insertTable = createNormalInsertStatement();
        
        ValidationResult result = validator.validateSingleStatement(insertTable, schemas);
        
        assertNull(result, "正常的INSERT语句应该验证通过，返回null");
    }
    
    @Test
    @DisplayName("有冲突的INSERT语句验证失败")
    void testConflictInsertStatementValidation() {
        InsertTable insertTable = createConflictInsertStatement();
        
        ValidationResult result = validator.validateSingleStatement(insertTable, schemas);
        
        assertNotNull(result, "有冲突的INSERT语句应该验证失败");
        assertFalse(result.getConflicts().isEmpty(), "应该包含冲突信息");
        
        // 检查冲突详情
        boolean foundUsernameConflict = result.getConflicts().stream()
                .anyMatch(conflict -> conflict.getDescription().contains("username"));
        assertTrue(foundUsernameConflict, "应该检测到username字段的长度冲突");
    }
    
    @Test
    @DisplayName("正常的UPDATE语句验证通过")
    void testNormalUpdateStatementValidation() {
        UpdateTable updateTable = createNormalUpdateStatement();
        
        ValidationResult result = validator.validateSingleStatement(updateTable, schemas);
        
        assertNull(result, "正常的UPDATE语句应该验证通过，返回null");
    }
    
    @Test
    @DisplayName("有冲突的UPDATE语句验证失败")
    void testConflictUpdateStatementValidation() {
        UpdateTable updateTable = createConflictUpdateStatement();
        
        ValidationResult result = validator.validateSingleStatement(updateTable, schemas);
        
        assertNotNull(result, "有冲突的UPDATE语句应该验证失败");
        assertFalse(result.getConflicts().isEmpty(), "应该包含冲突信息");
        
        // 检查冲突详情
        boolean foundDescriptionConflict = result.getConflicts().stream()
                .anyMatch(conflict -> conflict.getDescription().contains("description"));
        assertTrue(foundDescriptionConflict, "应该检测到description字段的长度冲突");
    }
    
    @Test
    @DisplayName("空表结构处理")
    void testEmptySchemaHandling() {
        InsertTable insertTable = createNormalInsertStatement();
        Map<String, TableSchema> emptySchemas = new HashMap<>();

        // 没有表结构信息时，应该无法验证，可能返回null或特定结果
        // 具体行为取决于实现
        assertDoesNotThrow(() -> validator.validateSingleStatement(insertTable, emptySchemas));
        assertNotNull(emptySchemas, "空的schemas映射应该被正确处理");
    }
    
    @Test
    @DisplayName("null语句处理")
    void testNullStatementHandling() {
        // null语句应该被正确处理，不应该抛出异常
        assertDoesNotThrow(() -> validator.validateSingleStatement(null, schemas));
    }
    
    @Test
    @DisplayName("表结构信息验证")
    void testTableSchemaInformation() {
        TableSchema userTableSchema = schemas.get("user_table");
        
        assertNotNull(userTableSchema, "user_table的schema应该存在");
        
        Map<String, TableSchema.VarcharColumn> varcharColumns = userTableSchema.getVarcharColumns();
        assertEquals(3, varcharColumns.size(), "应该有3个VARCHAR列");
        
        assertTrue(varcharColumns.containsKey("username"), "应该包含username列");
        assertTrue(varcharColumns.containsKey("email"), "应该包含email列");
        assertTrue(varcharColumns.containsKey("description"), "应该包含description列");
        
        assertEquals(50, varcharColumns.get("username").getMaxLength(), "username列长度应该是50");
        assertEquals(100, varcharColumns.get("email").getMaxLength(), "email列长度应该是100");
        assertEquals(200, varcharColumns.get("description").getMaxLength(), "description列长度应该是200");
    }
    
    /**
     * 创建正常的INSERT语句
     */
    private InsertTable createNormalInsertStatement() {
        TableId tableId = new TableId("user_table");
        List<String> columns = Arrays.asList("username", "email", "description");
        
        List<List<String>> rows = new ArrayList<>();
        rows.add(Arrays.asList("'john_doe'", "'<EMAIL>'", "'A normal user'"));
        ValuesClause valuesClause = new ValuesClause(rows);
        
        InsertTable insertTable = new InsertTable(tableId, columns, valuesClause);
        insertTable.setSql("INSERT INTO user_table (username, email, description) VALUES ('john_doe', '<EMAIL>', 'A normal user')");
        
        return insertTable;
    }
    
    /**
     * 创建有冲突的INSERT语句
     */
    private InsertTable createConflictInsertStatement() {
        TableId tableId = new TableId("user_table");
        List<String> columns = Arrays.asList("username", "email", "description");
        
        // username超过50字符限制
        String longUsername = "this_is_a_very_long_username_that_exceeds_the_50_character_limit_for_username_column";
        
        List<List<String>> rows = new ArrayList<>();
        rows.add(Arrays.asList("'" + longUsername + "'", "'<EMAIL>'", "'A user with long username'"));
        ValuesClause valuesClause = new ValuesClause(rows);
        
        InsertTable insertTable = new InsertTable(tableId, columns, valuesClause);
        insertTable.setSql("INSERT INTO user_table (username, email, description) VALUES ('" + longUsername + "', '<EMAIL>', 'A user with long username')");
        
        return insertTable;
    }
    
    /**
     * 创建正常的UPDATE语句
     */
    private UpdateTable createNormalUpdateStatement() {
        TableId tableId = new TableId("user_table");
        
        List<UpdateTable.SetClause> setClauses = new ArrayList<>();
        setClauses.add(new UpdateTable.SetClause("email", "'<EMAIL>'"));
        setClauses.add(new UpdateTable.SetClause("description", "'Updated description'"));
        
        UpdateTable updateTable = new UpdateTable(tableId, setClauses, "username = 'john_doe'");
        updateTable.setSql("UPDATE user_table SET email = '<EMAIL>', description = 'Updated description' WHERE username = 'john_doe'");
        
        return updateTable;
    }
    
    /**
     * 创建有冲突的UPDATE语句
     */
    private UpdateTable createConflictUpdateStatement() {
        TableId tableId = new TableId("user_table");
        
        // description超过200字符限制
        String longDescription = "This is a very long description that definitely exceeds the 200 character limit for the description column. " +
                               "It contains a lot of text to make sure it goes over the limit and triggers a VARCHAR length conflict during validation. " +
                               "This should be more than 200 characters.";
        
        List<UpdateTable.SetClause> setClauses = new ArrayList<>();
        setClauses.add(new UpdateTable.SetClause("description", "'" + longDescription + "'"));
        
        UpdateTable updateTable = new UpdateTable(tableId, setClauses, "username = 'john_doe'");
        updateTable.setSql("UPDATE user_table SET description = '" + longDescription + "' WHERE username = 'john_doe'");
        
        return updateTable;
    }
    
    @Test
    @DisplayName("验证器配置测试")
    void testValidatorConfiguration() {
        StagedVarcharValidator testValidator = new StagedVarcharValidator();
        
        // 测试默认配置
        VarcharValidationConfig defaultConfig = new VarcharValidationConfig();
        testValidator.configure(defaultConfig);
        
        // 测试启用配置
        VarcharValidationConfig enabledConfig = VarcharValidationConfig.enabledConfig();
        enabledConfig.setDetailedReporting(true);
        enabledConfig.setMaxReportedConflicts(100);
        testValidator.configure(enabledConfig);
        
        // 配置应该被正确应用
        assertNotNull(testValidator, "验证器应该被正确配置");
    }
    
    @Test
    @DisplayName("多行INSERT语句验证")
    void testMultiRowInsertValidation() {
        TableId tableId = new TableId("user_table");
        List<String> columns = Arrays.asList("username", "email", "description");
        
        List<List<String>> rows = new ArrayList<>();
        // 第一行正常
        rows.add(Arrays.asList("'user1'", "'<EMAIL>'", "'Normal user 1'"));
        // 第二行有冲突
        String longUsername = "this_is_a_very_long_username_that_exceeds_the_50_character_limit";
        rows.add(Arrays.asList("'" + longUsername + "'", "'<EMAIL>'", "'User with long username'"));
        
        ValuesClause valuesClause = new ValuesClause(rows);
        InsertTable insertTable = new InsertTable(tableId, columns, valuesClause);
        insertTable.setSql("INSERT INTO user_table (username, email, description) VALUES ('user1', '<EMAIL>', 'Normal user 1'), ('" + longUsername + "', '<EMAIL>', 'User with long username')");
        
        ValidationResult result = validator.validateSingleStatement(insertTable, schemas);
        
        if (result != null) {
            assertFalse(result.getConflicts().isEmpty(), "应该检测到第二行的冲突");
        }
    }
}
