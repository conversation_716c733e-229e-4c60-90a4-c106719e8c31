package com.xylink.sqltranspiler.unit.core.parser;

import static org.junit.jupiter.api.Assertions.*;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.create.CreateTable;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;

/**
 * SQL解析器根本性缺陷修复测试
 * 
 * 基于审查报告发现的"only parser one sql, sql count: 0"错误
 * 严格验证SQL解析器的基本功能，确保高级特性能够正确解析
 * 
 * 官方文档依据：
 * - MySQL 8.4: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 */
@DisplayName("SQL解析器根本性缺陷修复测试")
public class SqlParserFundamentalTest {

    @BeforeEach
    void setUp() {
        // 确保每个测试都有干净的环境
    }

    @Test
    @DisplayName("验证基础SQL解析功能 - 解决根本性缺陷")
    void testBasicSqlParsing() {
        // 测试基础SELECT语句解析
        String basicSql = "SELECT * FROM users;";
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(basicSql);
        
        assertNotNull(statements, "解析结果不应为null");
        assertFalse(statements.isEmpty(), "解析结果不应为空 - 修复'sql count: 0'错误");
        assertEquals(1, statements.size(), "应该解析出1个语句");
        assertTrue(statements.get(0) instanceof QueryStmt, "应该解析为QueryStmt类型");
        
        System.out.println("✅ 基础SQL解析测试通过: " + statements.size() + " 个语句");
    }

    @Test
    @DisplayName("验证CREATE TABLE语句解析")
    void testCreateTableParsing() {
        String createTableSql = """
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(255) UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(createTableSql);
        
        assertNotNull(statements, "CREATE TABLE解析结果不应为null");
        assertFalse(statements.isEmpty(), "CREATE TABLE解析结果不应为空");
        assertEquals(1, statements.size(), "应该解析出1个CREATE TABLE语句");
        assertTrue(statements.get(0) instanceof CreateTable, "应该解析为CreateTable类型");
        
        CreateTable createTable = (CreateTable) statements.get(0);
        assertNotNull(createTable.getTableId(), "表ID不应为null");
        assertFalse(createTable.getColumnRels().isEmpty(), "列定义不应为空");

        System.out.println("✅ CREATE TABLE解析测试通过: 表ID=" + createTable.getTableId() +
                          ", 列数=" + createTable.getColumnRels().size());
    }

    @Test
    @DisplayName("验证窗口函数SQL解析 - 关键高级特性")
    void testWindowFunctionParsing() {
        // 基于达梦数据库官方文档，窗口函数是重要特性
        String windowSql = """
            SELECT 
                id, 
                name,
                ROW_NUMBER() OVER (PARTITION BY dept_id ORDER BY salary DESC) as rn,
                RANK() OVER (PARTITION BY dept_id ORDER BY salary DESC) as rank_num
            FROM employees;
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(windowSql);
        
        assertNotNull(statements, "窗口函数SQL解析结果不应为null");
        assertFalse(statements.isEmpty(), "窗口函数SQL解析结果不应为空 - 这是关键高级特性");
        assertEquals(1, statements.size(), "应该解析出1个窗口函数语句");
        assertTrue(statements.get(0) instanceof QueryStmt, "应该解析为QueryStmt类型");
        
        // 验证原始SQL被正确保存
        QueryStmt queryStmt = (QueryStmt) statements.get(0);
        String originalSql = queryStmt.getSql();
        assertNotNull(originalSql, "原始SQL不应为null");
        assertTrue(originalSql.contains("PARTITION BY"), "原始SQL应包含PARTITION BY");
        assertTrue(originalSql.contains("ORDER BY"), "原始SQL应包含ORDER BY");
        assertTrue(originalSql.contains("ROW_NUMBER()"), "原始SQL应包含ROW_NUMBER()");
        
        System.out.println("✅ 窗口函数解析测试通过");
        System.out.println("原始SQL长度: " + originalSql.length());
    }

    @Test
    @DisplayName("验证CTE递归查询SQL解析 - 关键高级特性")
    void testCTERecursiveParsing() {
        // 基于达梦数据库官方文档，CTE递归查询是重要特性
        String cteSql = """
            WITH RECURSIVE emp_hierarchy AS (
                SELECT id, name, manager_id, 1 as level 
                FROM employees 
                WHERE manager_id IS NULL
                UNION ALL
                SELECT e.id, e.name, e.manager_id, eh.level + 1 
                FROM employees e 
                JOIN emp_hierarchy eh ON e.manager_id = eh.id
            )
            SELECT * FROM emp_hierarchy ORDER BY level, name;
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(cteSql);
        
        assertNotNull(statements, "CTE递归查询解析结果不应为null");
        assertFalse(statements.isEmpty(), "CTE递归查询解析结果不应为空 - 这是关键高级特性");
        assertEquals(1, statements.size(), "应该解析出1个CTE递归查询语句");
        assertTrue(statements.get(0) instanceof QueryStmt, "应该解析为QueryStmt类型");
        
        // 验证原始SQL被正确保存
        QueryStmt queryStmt = (QueryStmt) statements.get(0);
        String originalSql = queryStmt.getSql();
        assertNotNull(originalSql, "原始SQL不应为null");
        assertTrue(originalSql.contains("WITH RECURSIVE"), "原始SQL应包含WITH RECURSIVE");
        assertTrue(originalSql.contains("UNION ALL"), "原始SQL应包含UNION ALL");
        
        System.out.println("✅ CTE递归查询解析测试通过");
        System.out.println("原始SQL长度: " + originalSql.length());
    }

    @Test
    @DisplayName("验证多语句解析功能")
    void testMultiStatementParsing() {
        String multiSql = """
            CREATE TABLE users (id INT PRIMARY KEY, name VARCHAR(100));
            INSERT INTO users (id, name) VALUES (1, 'John');
            SELECT * FROM users WHERE id = 1;
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(multiSql);
        
        assertNotNull(statements, "多语句解析结果不应为null");
        assertFalse(statements.isEmpty(), "多语句解析结果不应为空");
        assertEquals(3, statements.size(), "应该解析出3个语句");
        
        // 验证每个语句的类型
        assertTrue(statements.get(0) instanceof CreateTable, "第1个语句应为CreateTable");
        // 注意：INSERT和SELECT可能被解析为不同的Statement子类型
        assertNotNull(statements.get(1), "第2个语句不应为null");
        assertNotNull(statements.get(2), "第3个语句不应为null");
        
        System.out.println("✅ 多语句解析测试通过: " + statements.size() + " 个语句");
    }

    @Test
    @DisplayName("验证复杂SQL结构解析")
    void testComplexSqlParsing() {
        // 测试包含多种高级特性的复杂SQL
        String complexSql = """
            WITH sales_summary AS (
                SELECT 
                    region,
                    SUM(amount) as total_sales,
                    ROW_NUMBER() OVER (ORDER BY SUM(amount) DESC) as rank_num
                FROM sales 
                WHERE sale_date >= '2024-01-01'
                GROUP BY region
            )
            SELECT 
                region,
                total_sales,
                rank_num,
                CASE 
                    WHEN rank_num <= 3 THEN 'Top 3'
                    ELSE 'Others'
                END as category
            FROM sales_summary
            ORDER BY rank_num;
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(complexSql);
        
        assertNotNull(statements, "复杂SQL解析结果不应为null");
        assertFalse(statements.isEmpty(), "复杂SQL解析结果不应为空 - 这是综合高级特性测试");
        assertEquals(1, statements.size(), "应该解析出1个复杂查询语句");
        
        QueryStmt queryStmt = (QueryStmt) statements.get(0);
        String originalSql = queryStmt.getSql();
        assertNotNull(originalSql, "原始SQL不应为null");
        assertTrue(originalSql.contains("WITH"), "原始SQL应包含WITH");
        assertTrue(originalSql.contains("ROW_NUMBER()"), "原始SQL应包含ROW_NUMBER()");
        assertTrue(originalSql.contains("CASE"), "原始SQL应包含CASE");
        
        System.out.println("✅ 复杂SQL解析测试通过");
        System.out.println("原始SQL包含关键字: WITH, ROW_NUMBER(), CASE");
    }

    @Test
    @DisplayName("验证解析器错误处理")
    void testParserErrorHandling() {
        // 测试语法错误的SQL
        String invalidSql = "SELECT * FROM;"; // 缺少表名
        
        try {
            List<Statement> statements = MySqlHelper.parseMultiStatement(invalidSql);
            // 如果解析器能处理错误，应该返回空列表或抛出异常
            if (statements != null) {
                System.out.println("解析器返回结果: " + statements.size() + " 个语句");
            }
        } catch (Exception e) {
            // 预期的异常处理
            System.out.println("✅ 解析器正确处理了语法错误: " + e.getMessage());
        }
    }
}
