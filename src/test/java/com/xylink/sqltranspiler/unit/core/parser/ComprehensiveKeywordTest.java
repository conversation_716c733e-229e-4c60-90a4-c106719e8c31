package com.xylink.sqltranspiler.unit.core.parser;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.fail;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.infrastructure.parser.PreprocessingResult;
import com.xylink.sqltranspiler.infrastructure.parser.Preprocessor;

/**
 * 全面测试预处理器对各种SQL语句中保留关键字的处理
 * 包括表名和字段名的保留关键字处理
 */
@DisplayName("全面的保留关键字预处理测试")
public class ComprehensiveKeywordTest {

    @BeforeEach
    void setUp() {
        // 测试前的准备工作
    }

    /**
     * 测试CREATE TABLE语句中的保留关键字
     */
    @Test
    @DisplayName("CREATE TABLE - 表名和字段名保留关键字")
    void testCreateTableKeywords() {
        // 测试表名是保留关键字
        String sql1 = "CREATE TABLE `order` (id INT, `name` VARCHAR(100));";
        assertPreprocessingAndParsing(sql1, "CREATE TABLE with reserved table name");

        // 测试字段名是保留关键字
        String sql2 = "CREATE TABLE users (id INT, `name` VARCHAR(100), `order` INT, `level` VARCHAR(50));";
        assertPreprocessingAndParsing(sql2, "CREATE TABLE with reserved column names");

        // 测试表名和字段名都是保留关键字
        String sql3 = "CREATE TABLE `group` (`name` VARCHAR(100), `order` INT, `level` VARCHAR(50));";
        assertPreprocessingAndParsing(sql3, "CREATE TABLE with both reserved table and column names");
    }

    /**
     * 测试INSERT语句中的保留关键字
     */
    @Test
    @DisplayName("INSERT - 表名和字段名保留关键字")
    void testInsertKeywords() {
        // 测试表名是保留关键字（无反引号，需要预处理器自动添加）
        String sql1 = "INSERT INTO order (id, product_name) VALUES (1, 'Product A');";
        assertPreprocessingAndParsing(sql1, "INSERT with reserved table name (auto-quote)");

        // 测试字段名是保留关键字（无反引号，需要预处理器自动添加）
        String sql2 = "INSERT INTO users (name, email, level) VALUES ('John', '<EMAIL>', 'admin');";
        assertPreprocessingAndParsing(sql2, "INSERT with reserved column names (auto-quote)");

        // 测试表名和字段名都是保留关键字（无反引号，需要预处理器自动添加）
        String sql3 = "INSERT INTO group (name, order) VALUES ('Admin Group', 1);";
        assertPreprocessingAndParsing(sql3, "INSERT with both reserved table and column names (auto-quote)");

        // 测试多行INSERT（无反引号，需要预处理器自动添加）
        String sql4 = "INSERT INTO users (name, email) VALUES ('John', '<EMAIL>'), ('Jane', '<EMAIL>');";
        assertPreprocessingAndParsing(sql4, "Multi-row INSERT with reserved column names (auto-quote)");

        // 测试已有反引号的情况（预处理器不应重复处理）
        String sql5 = "INSERT INTO `order` (`name`, `level`) VALUES ('Test', 'admin');";
        assertPreprocessingAndParsing(sql5, "INSERT with existing backticks (should not double-quote)");
    }

    /**
     * 测试SELECT语句中的保留关键字
     */
    @Test
    @DisplayName("SELECT - 表名和字段名保留关键字")
    void testSelectKeywords() {
        // 测试表名是保留关键字
        String sql1 = "SELECT id, product_name FROM `order`;";
        assertPreprocessingAndParsing(sql1, "SELECT with reserved table name");

        // 测试字段名是保留关键字
        String sql2 = "SELECT `name`, `email`, `level` FROM users;";
        assertPreprocessingAndParsing(sql2, "SELECT with reserved column names");

        // 测试表名和字段名都是保留关键字
        String sql3 = "SELECT `name`, `order` FROM `group`;";
        assertPreprocessingAndParsing(sql3, "SELECT with both reserved table and column names");

        // 测试带WHERE条件的SELECT
        String sql4 = "SELECT `name` FROM users WHERE `level` = 'admin';";
        assertPreprocessingAndParsing(sql4, "SELECT with WHERE clause using reserved column names");
    }

    /**
     * 测试UPDATE语句中的保留关键字
     */
    @Test
    @DisplayName("UPDATE - 表名和字段名保留关键字")
    void testUpdateKeywords() {
        // 测试表名是保留关键字
        String sql1 = "UPDATE `order` SET product_name = 'New Product' WHERE id = 1;";
        assertPreprocessingAndParsing(sql1, "UPDATE with reserved table name");

        // 测试字段名是保留关键字
        String sql2 = "UPDATE users SET `name` = 'John Doe', `level` = 'admin' WHERE id = 1;";
        assertPreprocessingAndParsing(sql2, "UPDATE with reserved column names");

        // 测试表名和字段名都是保留关键字
        String sql3 = "UPDATE `group` SET `name` = 'New Group', `order` = 2 WHERE id = 1;";
        assertPreprocessingAndParsing(sql3, "UPDATE with both reserved table and column names");
    }

    /**
     * 测试DELETE语句中的保留关键字
     */
    @Test
    @DisplayName("DELETE - 表名和字段名保留关键字")
    void testDeleteKeywords() {
        // 测试表名是保留关键字
        String sql1 = "DELETE FROM `order` WHERE id = 1;";
        assertPreprocessingAndParsing(sql1, "DELETE with reserved table name");

        // 测试字段名是保留关键字
        String sql2 = "DELETE FROM users WHERE `name` = 'John' AND `level` = 'admin';";
        assertPreprocessingAndParsing(sql2, "DELETE with reserved column names");

        // 测试表名和字段名都是保留关键字
        String sql3 = "DELETE FROM `group` WHERE `name` = 'Old Group' AND `order` > 5;";
        assertPreprocessingAndParsing(sql3, "DELETE with both reserved table and column names");
    }

    /**
     * 测试ALTER TABLE语句中的保留关键字
     */
    @Test
    @DisplayName("ALTER TABLE - 表名和字段名保留关键字")
    void testAlterTableKeywords() {
        // 测试表名是保留关键字（无反引号，需要预处理器自动添加）
        String sql1 = "ALTER TABLE order ADD COLUMN description VARCHAR(100);";
        assertPreprocessingAndParsing(sql1, "ALTER TABLE with reserved table name (auto-quote)");

        // 测试字段名是保留关键字（无反引号，需要预处理器自动添加）
        String sql2 = "ALTER TABLE users ADD COLUMN level VARCHAR(50);";
        assertPreprocessingAndParsing(sql2, "ALTER TABLE with reserved column name (auto-quote)");

        // 测试表名和字段名都是保留关键字（无反引号，需要预处理器自动添加）
        String sql3 = "ALTER TABLE group ADD COLUMN order INT;";
        assertPreprocessingAndParsing(sql3, "ALTER TABLE with both reserved table and column names (auto-quote)");

        // 测试已有反引号的情况（预处理器不应重复处理）
        String sql4 = "ALTER TABLE `order` ADD COLUMN `level` VARCHAR(50);";
        assertPreprocessingAndParsing(sql4, "ALTER TABLE with existing backticks (should not double-quote)");
    }

    /**
     * 辅助方法：验证预处理和解析
     */
    private void assertPreprocessingAndParsing(String sql, String testDescription) {
        System.out.println("\n=== " + testDescription + " ===");
        System.out.println("Original SQL: " + sql);

        // 1. 预处理
        PreprocessingResult preprocessResult = Preprocessor.preprocess(sql);
        System.out.println("Preprocessed SQL: " + preprocessResult.cleanedSql());
        
        if (!preprocessResult.logs().isEmpty()) {
            System.out.println("Preprocessing logs:");
            for (String log : preprocessResult.logs()) {
                System.out.println("  " + log);
            }
        }

        // 2. 解析
        try {
            List<Statement> statements = MySqlHelper.parseMultiStatement(preprocessResult.cleanedSql());
            assertNotNull(statements, "Statements should not be null");
            assertFalse(statements.isEmpty(), "Should parse at least one statement");
            System.out.println("✅ Parse successful: " + statements.size() + " statement(s)");
        } catch (Exception e) {
            System.out.println("❌ Parse failed: " + e.getMessage());
            fail("Failed to parse SQL after preprocessing: " + e.getMessage());
        }
    }
}
