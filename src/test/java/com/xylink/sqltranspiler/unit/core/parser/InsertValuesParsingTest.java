package com.xylink.sqltranspiler.unit.core.parser;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.dml.InsertTable;
import com.xylink.sqltranspiler.core.ast.dml.ValuesClause;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.infrastructure.parser.Preprocessor;
import com.xylink.sqltranspiler.infrastructure.parser.PreprocessingResult;

/**
 * 测试INSERT语句VALUES部分的解析和转换
 * 
 * 这个测试类验证了我们修复的INSERT语句解析问题：
 * - MySqlAntlr4Visitor 正确解析 insertStatementValue
 * - ValuesClause 正确表示多行数据
 * - DamengGenerator 正确生成完整的INSERT语句
 */
@DisplayName("INSERT语句VALUES解析测试")
public class InsertValuesParsingTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("测试单行VALUES解析")
    void testSingleRowValues() {
        String sql = "INSERT INTO users (name, email) VALUES ('John Doe', '<EMAIL>');";

        // 使用预处理器处理保留关键字
        PreprocessingResult preprocessResult = Preprocessor.preprocess(sql);
        List<Statement> statements = MySqlHelper.parseMultiStatement(preprocessResult.cleanedSql());
        assertEquals(1, statements.size());
        
        InsertTable insertTable = (InsertTable) statements.get(0);
        assertEquals("users", insertTable.getTableId().getTableName());
        
        // 验证列名解析
        assertNotNull(insertTable.getColumns());
        assertEquals(2, insertTable.getColumns().size());
        assertEquals("name", insertTable.getColumns().get(0));
        assertEquals("email", insertTable.getColumns().get(1));
        
        // 验证VALUES解析
        assertNotNull(insertTable.getValuesClause());
        ValuesClause valuesClause = insertTable.getValuesClause();
        assertEquals(1, valuesClause.getRows().size());
        
        List<String> firstRow = valuesClause.getRows().get(0);
        assertEquals(2, firstRow.size());
        assertEquals("'John Doe'", firstRow.get(0));
        assertEquals("'<EMAIL>'", firstRow.get(1));
        
        // 验证生成的SQL
        String result = generator.generate(insertTable);
        assertTrue(result.contains("INSERT INTO") &&
                   (result.toUpperCase().contains("USERS") || result.contains("users")));
        assertTrue((result.toUpperCase().contains("NAME") || result.contains("name")) &&
                   (result.toUpperCase().contains("EMAIL") || result.contains("email")));
        assertTrue(result.contains("VALUES") && result.contains("'John Doe'") && result.contains("'<EMAIL>'"));
    }

    @Test
    @DisplayName("测试多行VALUES解析")
    void testMultiRowValues() {
        String sql = "INSERT INTO users (username, email) VALUES " +
                     "('John Doe', '<EMAIL>'), " +
                     "('Jane Smith', '<EMAIL>'), " +
                     "('Bob Wilson', '<EMAIL>');";
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());

        InsertTable insertTable = (InsertTable) statements.get(0);
        
        // 验证VALUES解析
        assertNotNull(insertTable.getValuesClause());
        ValuesClause valuesClause = insertTable.getValuesClause();
        assertEquals(3, valuesClause.getRows().size());
        
        // 验证第一行
        List<String> row1 = valuesClause.getRows().get(0);
        assertEquals("'John Doe'", row1.get(0));
        assertEquals("'<EMAIL>'", row1.get(1));
        
        // 验证第二行
        List<String> row2 = valuesClause.getRows().get(1);
        assertEquals("'Jane Smith'", row2.get(0));
        assertEquals("'<EMAIL>'", row2.get(1));
        
        // 验证第三行
        List<String> row3 = valuesClause.getRows().get(2);
        assertEquals("'Bob Wilson'", row3.get(0));
        assertEquals("'<EMAIL>'", row3.get(1));
        
        // 验证生成的SQL包含所有行
        String result = generator.generate(insertTable);
        assertTrue(result.contains("('John Doe', '<EMAIL>')"));
        assertTrue(result.contains("('Jane Smith', '<EMAIL>')"));
        assertTrue(result.contains("('Bob Wilson', '<EMAIL>')"));
    }

    @Test
    @DisplayName("测试不同数据类型的VALUES")
    void testDifferentDataTypesInValues() {
        String sql = "INSERT INTO products (id, name, price, active, created_at) VALUES " +
                     "(1, 'Product A', 99.99, TRUE, '2023-01-01'), " +
                     "(2, 'Product B', 149.50, FALSE, '2023-01-02');";

        // 使用预处理器处理保留关键字
        PreprocessingResult preprocessResult = Preprocessor.preprocess(sql);
        List<Statement> statements = MySqlHelper.parseMultiStatement(preprocessResult.cleanedSql());
        assertEquals(1, statements.size());

        InsertTable insertTable = (InsertTable) statements.get(0);
        ValuesClause valuesClause = insertTable.getValuesClause();
        
        assertEquals(2, valuesClause.getRows().size());
        
        // 验证第一行的不同数据类型
        List<String> row1 = valuesClause.getRows().get(0);
        assertEquals(5, row1.size());
        assertEquals("1", row1.get(0));           // 数字
        assertEquals("'Product A'", row1.get(1)); // 字符串
        assertEquals("99.99", row1.get(2));       // 小数
        assertEquals("TRUE", row1.get(3));        // 布尔值
        assertEquals("'2023-01-01'", row1.get(4)); // 日期字符串
        
        String result = generator.generate(insertTable);
        // 检查是否包含正确的值，注意TRUE/FALSE会被转换为1/0
        assertTrue(result.contains("'Product A'"), "Should contain Product A: " + result);
        assertTrue(result.contains("99.99"), "Should contain 99.99: " + result);
        assertTrue(result.contains("'2023-01-01'"), "Should contain date: " + result);
        // TRUE被转换为1，所以检查是否有布尔值转换
        assertTrue(result.contains(", 1,") || result.contains("TRUE"), "Should contain boolean value: " + result);
    }

    @Test
    @DisplayName("测试NULL值和函数调用")
    void testNullValuesAndFunctions() {
        String sql = "INSERT INTO events (message, priority, created_at, user_id) VALUES " +
                     "('System started', 'INFO', NOW(), NULL), " +
                     "('Error occurred', 'ERROR', CURRENT_TIMESTAMP, 123);";

        // 使用预处理器处理保留关键字
        PreprocessingResult preprocessResult = Preprocessor.preprocess(sql);
        List<Statement> statements = MySqlHelper.parseMultiStatement(preprocessResult.cleanedSql());
        assertEquals(1, statements.size());

        InsertTable insertTable = (InsertTable) statements.get(0);
        ValuesClause valuesClause = insertTable.getValuesClause();
        
        assertEquals(2, valuesClause.getRows().size());
        
        // 验证第一行包含NULL和函数
        List<String> row1 = valuesClause.getRows().get(0);
        assertEquals("'System started'", row1.get(0));
        assertEquals("'INFO'", row1.get(1));
        assertEquals("NOW()", row1.get(2));
        assertEquals("NULL", row1.get(3));
        
        String result = generator.generate(insertTable);
        // 验证MySQL函数被转换为达梦等价函数
        assertTrue(result.contains("SYSDATE") || result.contains("NOW()"));
    }

    @Test
    @DisplayName("测试无列名的INSERT语句")
    void testInsertWithoutColumnList() {
        String sql = "INSERT INTO users VALUES (1, 'John', '<EMAIL>');";
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());

        InsertTable insertTable = (InsertTable) statements.get(0);
        
        // 验证没有指定列名
        assertTrue(insertTable.getColumns() == null || insertTable.getColumns().isEmpty());
        
        // 验证VALUES被正确解析
        assertNotNull(insertTable.getValuesClause());
        ValuesClause valuesClause = insertTable.getValuesClause();
        assertEquals(1, valuesClause.getRows().size());
        
        List<String> row = valuesClause.getRows().get(0);
        assertEquals(3, row.size());
        assertEquals("1", row.get(0));
        assertEquals("'John'", row.get(1));
        assertEquals("'<EMAIL>'", row.get(2));
        
        String result = generator.generate(insertTable);
        assertTrue(result.contains("INSERT INTO") &&
                   (result.toUpperCase().contains("USERS") || result.contains("users")) &&
                   result.contains("VALUES"));
        // 不应该有列名列表 - 检查不包含列名定义的括号
        assertFalse(result.matches(".*\\([^)]*\\)\\s*VALUES.*"));
    }

    @Test
    @DisplayName("测试带反引号的INSERT语句完整场景")
    void testCompleteInsertWithBackticks() {
        String sql = "INSERT INTO `user_profiles` (`user_id`, `name`, `status`, `type`) VALUES " +
                     "(1, 'Admin User', 'active', 'admin'), " +
                     "(2, 'Regular User', 'inactive', 'user');";

        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());

        InsertTable insertTable = (InsertTable) statements.get(0);

        // 验证表名和列名（MySQL关键字）
        assertEquals("user_profiles", insertTable.getTableId().getTableName());
        assertEquals(4, insertTable.getColumns().size());
        assertEquals("user_id", insertTable.getColumns().get(0));
        assertEquals("name", insertTable.getColumns().get(1));
        assertEquals("status", insertTable.getColumns().get(2));
        assertEquals("type", insertTable.getColumns().get(3));

        // 验证VALUES
        ValuesClause valuesClause = insertTable.getValuesClause();
        assertEquals(2, valuesClause.getRows().size());

        String result = generator.generate(insertTable);

        // 验证完整的转换结果
        assertTrue(result.contains("INSERT INTO") &&
                   (result.toUpperCase().contains("USER_PROFILES") || result.contains("user_profiles")));
        assertTrue((result.toUpperCase().contains("USER_ID") || result.contains("user_id")) &&
                   (result.toUpperCase().contains("NAME") || result.contains("name")) &&
                   (result.toUpperCase().contains("STATUS") || result.contains("status")) &&
                   (result.toUpperCase().contains("TYPE") || result.contains("type")));
        assertTrue(result.contains("(1, 'Admin User', 'active', 'admin')"));
        assertTrue(result.contains("(2, 'Regular User', 'inactive', 'user')"));
    }

    @Test
    @DisplayName("测试真实场景：sdk_mode_content_layout表的多VALUES INSERT")
    void testRealWorldSdkModeContentLayoutMultiValues() {
        // 模拟真实SQL文件中的多VALUES INSERT语句
        String sql = "INSERT INTO sdk_mode_content_layout VALUES " +
                     "('1310', 0, '13x1', '0.25, 0, 0.5, 0.5'), " +
                     "('1311', 1, '13x1', '0, 0.75, 0.25, 0.25'), " +
                     "('1312', 2, '13x1', '0.25, 0.75, 0.25, 0.25'), " +
                     "('1313', 3, '13x1', '0.5, 0.75, 0.25, 0.25'), " +
                     "('1314', 4, '13x1', '0.75, 0.75, 0.25, 0.25');";

        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(1, statements.size());

        InsertTable insertTable = (InsertTable) statements.get(0);

        // 验证表名
        assertEquals("sdk_mode_content_layout", insertTable.getTableId().getTableName());

        // 验证VALUES - 应该有5行数据
        ValuesClause valuesClause = insertTable.getValuesClause();
        assertEquals(5, valuesClause.getRows().size());

        String result = generator.generate(insertTable);

        // 验证生成的SQL保持多VALUES格式，而不是拆分成多条INSERT
        assertTrue(result.contains("VALUES"));
        assertTrue(result.contains("('1310', 0, '13x1', '0.25, 0, 0.5, 0.5')"));
        assertTrue(result.contains("('1311', 1, '13x1', '0, 0.75, 0.25, 0.25')"));
        assertTrue(result.contains("('1314', 4, '13x1', '0.75, 0.75, 0.25, 0.25')"));

        // 确保不是多条INSERT语句
        assertEquals(1, countOccurrences(result, "INSERT INTO"));
    }

    @Test
    @DisplayName("测试多条单独INSERT语句的处理")
    void testMultipleSeparateInsertStatements() {
        // 模拟原始SQL文件中已经分开的多条INSERT语句
        String sql = "INSERT INTO sdk_mode_content_layout VALUES('1310', 0, '13x1', '0.25, 0, 0.5, 0.5');\n" +
                     "INSERT INTO sdk_mode_content_layout VALUES('1311', 1, '13x1', '0, 0.75, 0.25, 0.25');\n" +
                     "INSERT INTO sdk_mode_content_layout VALUES('1312', 2, '13x1', '0.25, 0.75, 0.25, 0.25');";

        List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
        assertEquals(3, statements.size()); // 应该解析为3条独立的INSERT语句

        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            result.append(generator.generate(statement)).append("\n");
        }

        // 验证确实生成了3条独立的INSERT语句
        assertEquals(3, countOccurrences(result.toString(), "INSERT INTO"));
    }

    @Test
    @DisplayName("测试巨大的单行INSERT语句格式保持")
    void testLargeInlineInsertFormatPreservation() {
        // 模拟原始SQL文件中的巨大单行INSERT语句（类似libra_allowed_capability_of_device表）
        StringBuilder sql = new StringBuilder("INSERT INTO large_table VALUES ");
        for (int i = 1; i <= 100; i++) {
            if (i > 1) sql.append(",");
            sql.append("(").append(i).append(",'value").append(i).append("','data").append(i).append("')");
        }
        sql.append(";");

        String originalSql = sql.toString();
        System.out.println("Original SQL length: " + originalSql.length() + " characters");

        List<Statement> statements = MySqlHelper.parseMultiStatement(originalSql);
        assertEquals(1, statements.size());

        InsertTable insertTable = (InsertTable) statements.get(0);

        // 验证解析正确
        assertEquals("large_table", insertTable.getTableId().getTableName());
        ValuesClause valuesClause = insertTable.getValuesClause();
        assertEquals(100, valuesClause.getRows().size());

        // 验证是否保存了原始格式
        System.out.println("Has original format: " + valuesClause.hasOriginalFormat());
        if (valuesClause.hasOriginalFormat()) {
            System.out.println("Original VALUES text length: " + valuesClause.getOriginalValuesText().length());
        }

        String result = generator.generate(insertTable);
        System.out.println("Generated SQL length: " + result.length() + " characters");
        System.out.println("Size increase ratio: " + (double)result.length() / originalSql.length());

        // 调试输出（可选）
        // System.out.println("Generated SQL first 200 chars: " + result.substring(0, Math.min(200, result.length())));
        // System.out.println("Generated SQL last 200 chars: " + result.substring(Math.max(0, result.length() - 200)));

        // 验证转换后的SQL仍然正确
        assertTrue(result.contains("INSERT INTO") &&
                   (result.toUpperCase().contains("LARGE_TABLE") || result.contains("large_table")) &&
                   result.contains("VALUES"));
        assertTrue(result.contains("(1,'value1','data1')") || result.contains("(1, 'value1', 'data1')"));
        assertTrue(result.contains("(100,'value100','data100')") || result.contains("(100, 'value100', 'data100')"));
        assertTrue(result.endsWith(";"), "SQL should end with semicolon");

        // 检查格式化：如果保持原始格式，行数应该相近
        int originalLines = originalSql.split("\n").length;
        int resultLines = result.split("\n").length;

        System.out.println("Original lines: " + originalLines);
        System.out.println("Result lines: " + resultLines);

        // 如果成功保持原始格式，行数差异应该很小
        if (valuesClause.hasOriginalFormat()) {
            assertTrue(Math.abs(resultLines - originalLines) <= 2,
                      "保持原始格式时，行数差异应该很小。原始: " + originalLines + ", 结果: " + resultLines);
        }
    }

    /**
     * 计算字符串中子串出现次数
     */
    private int countOccurrences(String text, String substring) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(substring, index)) != -1) {
            count++;
            index += substring.length();
        }
        return count;
    }
}
