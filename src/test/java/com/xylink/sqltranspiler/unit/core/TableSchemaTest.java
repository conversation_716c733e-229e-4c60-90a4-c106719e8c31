package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.validation.TableSchema;
import com.xylink.sqltranspiler.core.validation.TableSchemaBuilder;

/**
 * 表结构测试
 */
class TableSchemaTest {
    
    @Test
    @DisplayName("创建空的表结构")
    void testEmptyTableSchema() {
        TableSchemaBuilder builder = new TableSchemaBuilder("test_table");
        TableSchema schema = builder.build();
        
        assertEquals("test_table", schema.getTableName());
        assertEquals(0, schema.getVarcharColumnCount());
        assertFalse(schema.hasVarcharColumns());
        assertFalse(schema.hasVarcharColumn("any_column"));
    }
    
    @Test
    @DisplayName("创建包含VARCHAR列的表结构")
    void testTableSchemaWithVarcharColumns() {
        TableSchemaBuilder builder = new TableSchemaBuilder("user_table");
        TableSchema schema = builder
            .addVarcharColumn("name", 50)
            .addVarcharColumn("email", 100, false)
            .addVarcharColumn("description", 500)
            .build();
        
        assertEquals("user_table", schema.getTableName());
        assertEquals(3, schema.getVarcharColumnCount());
        assertTrue(schema.hasVarcharColumns());
        
        // 测试具体列
        assertTrue(schema.hasVarcharColumn("name"));
        assertTrue(schema.hasVarcharColumn("email"));
        assertTrue(schema.hasVarcharColumn("description"));
        assertFalse(schema.hasVarcharColumn("nonexistent"));
        
        // 测试列详情
        TableSchema.VarcharColumn nameColumn = schema.getVarcharColumn("name");
        assertNotNull(nameColumn);
        assertEquals("name", nameColumn.getColumnName());
        assertEquals(50, nameColumn.getMaxLength());
        assertEquals(0, nameColumn.getPosition());
        assertTrue(nameColumn.isNullable());
        
        TableSchema.VarcharColumn emailColumn = schema.getVarcharColumn("email");
        assertNotNull(emailColumn);
        assertEquals("email", emailColumn.getColumnName());
        assertEquals(100, emailColumn.getMaxLength());
        assertEquals(1, emailColumn.getPosition());
        assertFalse(emailColumn.isNullable());
    }
    
    @Test
    @DisplayName("表结构构建器测试")
    void testTableSchemaBuilder() {
        TableSchemaBuilder builder = new TableSchemaBuilder("test_table");
        
        assertEquals("test_table", builder.getTableName());
        assertEquals(0, builder.getColumnCount());
        assertFalse(builder.hasColumn("any_column"));
        
        // 添加列
        builder.addVarcharColumn("col1", 10);
        assertEquals(1, builder.getColumnCount());
        assertTrue(builder.hasColumn("col1"));
        
        builder.addVarcharColumn("col2", 20, 5, false);
        assertEquals(2, builder.getColumnCount());
        assertTrue(builder.hasColumn("col2"));
        
        // 构建schema
        TableSchema schema = builder.build();
        assertEquals(2, schema.getVarcharColumnCount());
        
        // 测试指定位置的列
        TableSchema.VarcharColumn col2 = schema.getVarcharColumn("col2");
        assertEquals(5, col2.getPosition());
        assertFalse(col2.isNullable());
    }
    
    @Test
    @DisplayName("表结构构建器重置和复制")
    void testBuilderResetAndCopy() {
        TableSchemaBuilder builder = new TableSchemaBuilder("test_table");
        builder.addVarcharColumn("col1", 10);
        builder.addVarcharColumn("col2", 20);
        
        assertEquals(2, builder.getColumnCount());
        
        // 测试复制
        TableSchemaBuilder copy = builder.copy();
        assertEquals(2, copy.getColumnCount());
        assertTrue(copy.hasColumn("col1"));
        assertTrue(copy.hasColumn("col2"));
        
        // 测试重置
        builder.reset();
        assertEquals(0, builder.getColumnCount());
        assertFalse(builder.hasColumn("col1"));
        
        // 复制应该不受影响
        assertEquals(2, copy.getColumnCount());
        assertTrue(copy.hasColumn("col1"));
    }
    
    @Test
    @DisplayName("表结构访问时间更新")
    void testAccessTimeUpdate() throws InterruptedException {
        TableSchemaBuilder builder = new TableSchemaBuilder("test_table");
        TableSchema schema = builder.addVarcharColumn("col1", 10).build();
        
        long initialTime = schema.getLastAccessTime();
        
        // 等待一小段时间
        Thread.sleep(10);
        
        // 访问schema应该更新访问时间
        schema.getVarcharColumns();
        long afterAccessTime = schema.getLastAccessTime();
        
        assertTrue(afterAccessTime > initialTime, "访问后时间应该更新");
        
        // 再次访问
        Thread.sleep(10);
        schema.getVarcharColumn("col1");
        long finalAccessTime = schema.getLastAccessTime();
        
        assertTrue(finalAccessTime > afterAccessTime, "再次访问后时间应该再次更新");
    }
    
    @Test
    @DisplayName("表结构相等性和哈希码")
    void testEqualsAndHashCode() {
        TableSchema schema1 = new TableSchemaBuilder("table1")
            .addVarcharColumn("col1", 10)
            .build();
            
        TableSchema schema2 = new TableSchemaBuilder("table1")
            .addVarcharColumn("col2", 20)
            .build();
            
        TableSchema schema3 = new TableSchemaBuilder("table2")
            .addVarcharColumn("col1", 10)
            .build();
        
        // 相同表名的schema应该相等（不管列定义）
        assertEquals(schema1, schema2);
        assertEquals(schema1.hashCode(), schema2.hashCode());
        
        // 不同表名的schema不应该相等
        assertNotEquals(schema1, schema3);
    }
}
