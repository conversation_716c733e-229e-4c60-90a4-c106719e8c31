package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;

/**
 * REPLACE INTO语句在各数据库中的兼容性测试
 * 根据各数据库官方文档验证REPLACE INTO语句的支持情况
 */
public class ReplaceIntoStatementCompatibilityTest {

    private static final Logger log = LoggerFactory.getLogger(ReplaceIntoStatementCompatibilityTest.class);
    private Transpiler transpiler;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }

    /**
     * 测试MySQL对REPLACE INTO语句的支持
     * 根据MySQL官方文档，MySQL原生支持REPLACE INTO语法
     */
    @Test
    void testMySqlReplaceIntoSupport() {
        // 注意：转换器不支持MySQL作为目标数据库，只支持从MySQL转换到其他数据库
        // 这个测试应该测试MySQL语法的解析能力，而不是MySQL到MySQL的转换
        String sql = "REPLACE INTO employees (id, name, salary) VALUES (1, 'John', 60000)";

        // 测试MySQL到金仓的转换（金仓支持REPLACE INTO）
        String result = transpiler.transpile(sql, "mysql", "kingbase").translatedSql();

        log.info("MySQL到金仓REPLACE INTO转换结果: {}", result);

        // 金仓数据库应该支持REPLACE INTO语句
        assertNotNull(result);
        assertTrue(result.contains("REPLACE INTO"));
        assertTrue(result.contains("employees"));
        assertTrue(result.contains("VALUES"));
        assertTrue(result.trim().endsWith(";"));
        assertFalse(result.contains("-- "), "KingbaseES should support REPLACE INTO without comments");
    }

    /**
     * 测试达梦数据库对REPLACE INTO语句的处理
     * 根据达梦数据库官方文档，不支持REPLACE INTO，建议使用MERGE INTO
     */
    @Test
    void testDamengReplaceIntoConversion() {
        String sql = "REPLACE INTO employees (id, name, salary) VALUES (1, 'John', 60000)";
        
        String result = transpiler.transpile(sql, "mysql", "dameng").translatedSql();

        log.info("达梦数据库REPLACE INTO转换结果: {}", result);

        // 达梦数据库应该将REPLACE INTO转换为MERGE INTO
        assertNotNull(result, "转换结果不应为null");
        assertFalse(result.trim().isEmpty(), "转换结果不应为空");

        assertTrue(result.contains("MERGE INTO"),
                  "Dameng should convert REPLACE INTO to MERGE INTO");
        assertTrue(result.contains("USING"), "应包含USING子句");
        assertTrue(result.contains("WHEN MATCHED"), "应包含WHEN MATCHED子句");
        assertTrue(result.contains("WHEN NOT MATCHED"), "应包含WHEN NOT MATCHED子句");
        assertTrue(result.trim().endsWith(";"), "应以分号结尾");
    }

    /**
     * 测试金仓数据库对REPLACE INTO语句的支持
     * 根据金仓数据库官方文档，支持REPLACE INTO语法
     */
    @Test
    void testKingbaseReplaceIntoSupport() {
        String sql = "REPLACE INTO employees (id, name, salary) VALUES (1, 'John', 60000)";

        String result = transpiler.transpile(sql, "mysql", "kingbase").translatedSql();

        log.info("金仓数据库REPLACE INTO转换结果: {}", result);

        // 金仓数据库应该支持REPLACE INTO语句
        assertNotNull(result);
        assertTrue(result.contains("REPLACE INTO"));
        assertTrue(result.contains("employees"));
        assertTrue(result.contains("VALUES"));
        assertTrue(result.trim().endsWith(";"), "应以分号结尾");
        assertFalse(result.contains("-- "), "KingbaseES should support REPLACE INTO without comments");
    }

    /**
     * 测试神通数据库对REPLACE INTO语句的处理
     * 根据神通数据库官方文档，不支持REPLACE INTO语法
     */
    @Test
    void testShentongReplaceIntoNotSupported() {
        String sql = "REPLACE INTO employees (id, name, salary) VALUES (1, 'John', 60000)";

        String result = transpiler.transpile(sql, "mysql", "shentong").translatedSql();
        
        log.info("神通数据库REPLACE INTO转换结果: {}", result);
        
        // 神通数据库应该拒绝REPLACE INTO语句
        assertNotNull(result);
        assertTrue(result.contains("-- REPLACE INTO is not supported in Shentong database"));
        assertTrue(result.contains("-- Please use standard INSERT INTO ... VALUES syntax"));
        assertTrue(result.contains("-- Original SQL: " + sql));
    }

    /**
     * 测试带反引号的REPLACE INTO语句转换
     * 验证各数据库对反引号的处理
     */
    @Test
    void testReplaceIntoWithBackticks() {
        String sql = "REPLACE INTO `employees` (`id`, `name`, `salary`) VALUES (1, 'John', 60000)";
        
        // 注意：转换器不支持MySQL作为目标数据库，只支持从MySQL转换到其他数据库

        // 测试金仓数据库（应该将反引号转换为双引号）
        String kingbaseResult = transpiler.transpile(sql, "mysql", "kingbase").translatedSql();
        log.info("金仓数据库带反引号REPLACE INTO转换结果: {}", kingbaseResult);
        assertNotNull(kingbaseResult);
        assertTrue(kingbaseResult.contains("REPLACE INTO"));
        assertTrue(kingbaseResult.contains("\"employees\"") || kingbaseResult.contains("`employees`"));

        // 测试神通数据库（应该拒绝）
        String shentongResult = transpiler.transpile(sql, "mysql", "shentong").translatedSql();
        log.info("神通数据库带反引号REPLACE INTO转换结果: {}", shentongResult);
        assertNotNull(shentongResult);
        assertTrue(shentongResult.contains("-- REPLACE INTO is not supported") ||
                  shentongResult.contains("-- [CONVERSION ERROR]"),
                  "神通数据库应该拒绝REPLACE INTO语句");
    }

    /**
     * 测试复杂的REPLACE INTO语句
     * 验证各数据库对复杂REPLACE INTO语句的处理
     */
    @Test
    void testComplexReplaceIntoStatement() {
        String sql = "REPLACE INTO employees (id, name, salary, department) VALUES (1, 'John', 60000, 'IT'), (2, 'Jane', 65000, 'HR')";
        
        // 注意：转换器不支持MySQL作为目标数据库，只支持从MySQL转换到其他数据库

        // 测试金仓数据库
        String kingbaseResult = transpiler.transpile(sql, "mysql", "kingbase").translatedSql();
        log.info("金仓数据库复杂REPLACE INTO转换结果: {}", kingbaseResult);
        assertNotNull(kingbaseResult);
        assertTrue(kingbaseResult.contains("REPLACE INTO"));

        // 测试神通数据库（应该拒绝）
        String shentongResult = transpiler.transpile(sql, "mysql", "shentong").translatedSql();
        log.info("神通数据库复杂REPLACE INTO转换结果: {}", shentongResult);
        assertNotNull(shentongResult);
        assertTrue(shentongResult.contains("-- REPLACE INTO is not supported") ||
                  shentongResult.contains("-- [CONVERSION ERROR]"),
                  "神通数据库应该拒绝REPLACE INTO语句");
    }

    /**
     * 测试各数据库对REPLACE INTO语句的官方文档符合性
     * 验证实现是否严格遵循各数据库的官方文档
     */
    @Test
    void testOfficialDocumentationCompliance() {
        String sql = "REPLACE INTO test_table (id, value) VALUES (1, 'test')";
        
        // 注意：转换器不支持MySQL作为目标数据库，只支持从MySQL转换到其他数据库

        // 达梦: 官方文档不支持REPLACE INTO，建议使用MERGE INTO
        String damengResult = transpiler.transpile(sql, "mysql", "dameng").translatedSql();
        assertTrue(damengResult.contains("MERGE INTO"),
                  "Dameng should convert REPLACE INTO to MERGE INTO according to official documentation");

        // 金仓: 官方文档支持REPLACE INTO
        String kingbaseResult = transpiler.transpile(sql, "mysql", "kingbase").translatedSql();
        assertTrue(kingbaseResult.contains("REPLACE INTO"),
                  "KingbaseES should support REPLACE INTO according to official documentation");

        // 神通: 官方文档不支持REPLACE INTO
        String shentongResult = transpiler.transpile(sql, "mysql", "shentong").translatedSql();
        assertTrue(shentongResult.contains("-- REPLACE INTO is not supported") ||
                  shentongResult.contains("-- [CONVERSION ERROR]"),
                  "Shentong should not support REPLACE INTO according to official documentation");
    }
}
