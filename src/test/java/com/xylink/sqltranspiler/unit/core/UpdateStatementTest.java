package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.dml.UpdateTable;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * UPDATE语句转换测试
 * 验证MySQL UPDATE语句到达梦数据库的转换功能
 * 
 * 参考文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/update.html
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 */
public class UpdateStatementTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    void testSimpleUpdate() {
        String sql = "UPDATE ainemo.libra_device_subtype_model SET `multi_image`='[\"1x1\", \"2x1\"]' WHERE `sub_type`='8300'";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement, "解析结果不应为null");
        assertTrue(statement instanceof UpdateTable, "应该解析为UpdateTable类型，实际类型：" + statement.getClass().getSimpleName());
        
        UpdateTable updateTable = (UpdateTable) statement;
        assertEquals("libra_device_subtype_model", updateTable.getTableId().getTableName());
        assertEquals("ainemo", updateTable.getTableId().getSchemaName());
        assertNotNull(updateTable.getSetClauses());
        assertEquals(1, updateTable.getSetClauses().size());
        
        UpdateTable.SetClause setClause = updateTable.getSetClauses().get(0);
        assertEquals("multi_image", setClause.getColumnName());
        assertEquals("'[\"1x1\", \"2x1\"]'", setClause.getValue());
        
        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE") &&
                   (damengSql.toUpperCase().contains("AINEMO") || damengSql.contains("ainemo")) &&
                   (damengSql.toUpperCase().contains("LIBRA_DEVICE_SUBTYPE_MODEL") || damengSql.contains("libra_device_subtype_model")));
        assertTrue(damengSql.contains("SET") &&
                   (damengSql.toUpperCase().contains("MULTI_IMAGE") || damengSql.contains("multi_image")) &&
                   damengSql.contains("'[\"1x1\", \"2x1\"]'"));
        assertTrue(damengSql.contains("WHERE") &&
                   (damengSql.toUpperCase().contains("SUB_TYPE") || damengSql.contains("sub_type")) &&
                   damengSql.contains("'8300'"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    void testUpdateWithMultipleColumns() {
        String sql = "UPDATE ainemo.t_en_resources SET `name` = '个人云会议室', description = '个人云会议室' WHERE `res_value` = '/meeting/file/user/room'";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof UpdateTable);

        UpdateTable updateTable = (UpdateTable) statement;
        assertEquals(2, updateTable.getSetClauses().size());

        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE") &&
                   (damengSql.toUpperCase().contains("AINEMO") || damengSql.contains("ainemo")) &&
                   (damengSql.toUpperCase().contains("T_EN_RESOURCES") || damengSql.contains("t_en_resources")));
        assertTrue(damengSql.contains("SET") &&
                   (damengSql.toUpperCase().contains("NAME") || damengSql.contains("name")) &&
                   (damengSql.toUpperCase().contains("DESCRIPTION") || damengSql.contains("description")) &&
                   damengSql.contains("个人云会议室"));
        assertTrue(damengSql.contains("WHERE") &&
                   (damengSql.toUpperCase().contains("RES_VALUE") || damengSql.contains("res_value")) &&
                   damengSql.contains("/meeting/file/user/room"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    void testUpdateWithFunctions() {
        String sql = "UPDATE ainemo.libra_box_enterprise SET `model_list`=CONCAT(model_list, ',', 'TVBox_TP860-X') WHERE `package_name` = 'com.xylink.charlie.app'";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof UpdateTable);

        UpdateTable updateTable = (UpdateTable) statement;
        assertEquals(1, updateTable.getSetClauses().size());

        UpdateTable.SetClause setClause = updateTable.getSetClauses().get(0);
        assertEquals("model_list", setClause.getColumnName());
        assertTrue(setClause.getValue().contains("CONCAT"));

        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE") &&
                   (damengSql.toUpperCase().contains("AINEMO") || damengSql.contains("ainemo")) &&
                   (damengSql.toUpperCase().contains("LIBRA_BOX_ENTERPRISE") || damengSql.contains("libra_box_enterprise")));
        assertTrue(damengSql.contains("SET") &&
                   (damengSql.toUpperCase().contains("MODEL_LIST") || damengSql.contains("model_list")) &&
                   damengSql.contains("CONCAT") && damengSql.contains("TVBox_TP860-X"));
        assertTrue(damengSql.contains("WHERE") &&
                   (damengSql.toUpperCase().contains("PACKAGE_NAME") || damengSql.contains("package_name")) &&
                   damengSql.contains("com.xylink.charlie.app"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    void testUpdateWithoutWhere() {
        String sql = "UPDATE test_table SET status = 1";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof UpdateTable);

        UpdateTable updateTable = (UpdateTable) statement;
        assertNull(updateTable.getWhereClause());

        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE") &&
                   (damengSql.toUpperCase().contains("TEST_TABLE") || damengSql.contains("test_table")));
        assertTrue(damengSql.contains("SET") &&
                   (damengSql.toUpperCase().contains("STATUS") || damengSql.contains("status")) &&
                   damengSql.contains("= 1"));
        assertFalse(damengSql.contains("WHERE"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    void testUpdateWithOrderByAndLimit() {
        String sql = "UPDATE test_table SET status = 1 WHERE id > 100 ORDER BY id LIMIT 10";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof UpdateTable);

        UpdateTable updateTable = (UpdateTable) statement;
        assertNotNull(updateTable.getWhereClause());
        assertNotNull(updateTable.getOrderByClause());
        assertNotNull(updateTable.getLimitClause());

        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE") &&
                   (damengSql.toUpperCase().contains("TEST_TABLE") || damengSql.contains("test_table")));
        assertTrue(damengSql.contains("SET") &&
                   (damengSql.toUpperCase().contains("STATUS") || damengSql.contains("status")) &&
                   damengSql.contains("= 1"));
        assertTrue(damengSql.contains("WHERE"));
        assertTrue(damengSql.contains("ORDER BY"));
        assertTrue(damengSql.contains("LIMIT"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    void testUpdateWithBackticks() {
        String sql = "UPDATE `test_table` SET `column_name` = 'value' WHERE `id` = 1";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof UpdateTable);
        
        // 测试转换为达梦SQL - 反引号应该转换为双引号
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE") &&
                   (damengSql.toUpperCase().contains("TEST_TABLE") || damengSql.contains("test_table")));
        assertTrue(damengSql.contains("SET") &&
                   (damengSql.toUpperCase().contains("COLUMN_NAME") || damengSql.contains("column_name")) &&
                   damengSql.contains("'value'"));
        assertTrue(damengSql.contains("WHERE") &&
                   (damengSql.toUpperCase().contains("ID") || damengSql.contains("id")) &&
                   damengSql.contains("= 1"));
        assertFalse(damengSql.contains("`")); // 不应该包含反引号
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    void testUpdateWithSchemaTable() {
        String sql = "UPDATE ainemo.test_table SET name = 'updated' WHERE id = 1";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof UpdateTable);

        UpdateTable updateTable = (UpdateTable) statement;
        assertEquals("ainemo", updateTable.getTableId().getSchemaName());
        assertEquals("test_table", updateTable.getTableId().getTableName());

        // 测试转换为达梦SQL
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);

        // 验证UPDATE语句中的schema.table格式正确
        assertTrue(damengSql.contains("UPDATE") &&
                   (damengSql.toUpperCase().contains("AINEMO") || damengSql.contains("ainemo")) &&
                   (damengSql.toUpperCase().contains("TEST_TABLE") || damengSql.contains("test_table")));
        assertTrue(damengSql.contains("SET") &&
                   (damengSql.toUpperCase().contains("NAME") || damengSql.contains("name")) &&
                   damengSql.contains("'updated'"));
        assertTrue(damengSql.contains("WHERE") &&
                   (damengSql.toUpperCase().contains("ID") || damengSql.contains("id")) &&
                   damengSql.contains("= 1"));
        // 确保不是表别名格式 - 检查包含点号分隔符
        assertTrue(damengSql.contains(".") || damengSql.contains("AINEMO") || damengSql.contains("ainemo"));
    }

    @Test
    void testUpdateSchemaTableFormatConsistency() {
        // 测试UPDATE语句的schema.table格式一致性
        String sql = "UPDATE ainemo.test_table SET name = 'updated' WHERE id = 1";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        assertTrue(statement instanceof UpdateTable);

        String damengSql = generator.generate(statement);

        // 验证schema.table格式正确
        assertTrue(damengSql.contains("UPDATE") &&
                   (damengSql.toUpperCase().contains("AINEMO") || damengSql.contains("ainemo")) &&
                   (damengSql.toUpperCase().contains("TEST_TABLE") || damengSql.contains("test_table")));
        // 确保不是表别名格式 - 检查包含点号分隔符
        assertTrue(damengSql.contains(".") || damengSql.contains("AINEMO") || damengSql.contains("ainemo"));
        // 验证完整的SQL语法
        assertTrue(damengSql.endsWith(";"));
    }
}
