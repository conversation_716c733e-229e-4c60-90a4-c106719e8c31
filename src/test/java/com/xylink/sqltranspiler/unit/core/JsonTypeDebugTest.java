package com.xylink.sqltranspiler.unit.core;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * JSON类型支持情况调试测试
 * 根据数据库规则：严格遵循官方文档，金仓数据库官方文档明确支持JSON类型
 */
public class JsonTypeDebugTest {

    private static final Logger log = LoggerFactory.getLogger(JsonTypeDebugTest.class);
    private Transpiler transpiler;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }

    @Test
    void debugJsonTypeSupport() {
        // 根据金仓数据库官方文档，JSON类型应该被原生支持
        String jsonSql = """
            CREATE TABLE json_test (
                id INT PRIMARY KEY,
                json_data JSON,
                json_array JSON,
                json_object JSON
            );
            """;
        
        log.info("测试JSON类型SQL: {}", jsonSql);
        
        // 测试MySQL到金仓的转换
        TranspilationResult kingbaseResult = transpiler.transpile(jsonSql, "mysql", "kingbase");
        log.info("金仓转换结果: '{}'", kingbaseResult.translatedSql());
        log.info("金仓成功数: {}, 失败数: {}", kingbaseResult.successCount(), kingbaseResult.failureCount());
        log.info("金仓问题列表: {}", kingbaseResult.issues());
        
        // 测试MySQL到达梦的转换
        TranspilationResult damengResult = transpiler.transpile(jsonSql, "mysql", "dameng");
        log.info("达梦转换结果: '{}'", damengResult.translatedSql());
        log.info("达梦成功数: {}, 失败数: {}", damengResult.successCount(), damengResult.failureCount());
        log.info("达梦问题列表: {}", damengResult.issues());
        
        // 测试MySQL到神通的转换
        TranspilationResult shentongResult = transpiler.transpile(jsonSql, "mysql", "shentong");
        log.info("神通转换结果: '{}'", shentongResult.translatedSql());
        log.info("神通成功数: {}, 失败数: {}", shentongResult.successCount(), shentongResult.failureCount());
        log.info("神通问题列表: {}", shentongResult.issues());
    }
}
