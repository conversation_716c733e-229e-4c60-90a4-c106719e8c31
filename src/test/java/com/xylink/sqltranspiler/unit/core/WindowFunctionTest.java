package com.xylink.sqltranspiler.unit.core;

import java.util.List;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

public class WindowFunctionTest {
    
    public static void main(String[] args) {
        System.out.println("=== 测试修复后的窗口函数解析 ===");
        
        // 使用非保留关键字作为别名
        String sql = "SELECT id, ROW_NUMBER() OVER (PARTITION BY category ORDER BY amount DESC) as row_rank FROM products;";
        System.out.println("SQL: " + sql);

        try {
            List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
            System.out.println("解析结果: 成功，语句数量 = " + statements.size());
            
            if (statements.size() > 0) {
                Statement stmt = statements.get(0);
                System.out.println("语句类型: " + stmt.getStatementType());
                System.out.println("包含窗口函数: " + (stmt.getSql().contains("ROW_NUMBER") ? "是" : "否"));
                System.out.println("SQL内容: " + stmt.getSql());
            }
        } catch (Exception e) {
            System.out.println("解析失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        // 测试保留关键字问题
        System.out.println("\n=== 测试保留关键字问题 ===");
        String sqlWithReservedKeyword = "SELECT id, ROW_NUMBER() OVER (PARTITION BY category ORDER BY amount DESC) as rank FROM products;";
        System.out.println("SQL: " + sqlWithReservedKeyword);

        try {
            List<Statement> statements = MySqlHelper.parseMultiStatement(sqlWithReservedKeyword);
            System.out.println("解析结果: 成功，语句数量 = " + statements.size());

            if (statements.size() > 0) {
                Statement stmt = statements.get(0);
                System.out.println("语句类型: " + stmt.getStatementType());
                System.out.println("包含窗口函数: " + (stmt.getSql().contains("ROW_NUMBER") ? "是" : "否"));
                System.out.println("SQL内容: " + stmt.getSql());
                System.out.println("保留字处理: " + (stmt.getSql().contains("`rank`") ? "已自动添加反引号" : "未检测到保留字问题"));
            }
        } catch (Exception e) {
            System.out.println("解析失败: " + e.getMessage());
            System.out.println("这可能是由于保留关键字'rank'导致的解析失败");
        }

        // 测试其他保留字
        System.out.println("\n=== 测试其他保留字 ===");
        String[] testSqls = {
            "SELECT order FROM test_table;",
            "SELECT group FROM test_table;",
            "SELECT table FROM test_table;",
            "SELECT `rank` FROM test_table;"  // 已正确引用的情况
        };

        for (String testSql : testSqls) {
            System.out.println("\n测试SQL: " + testSql);
            try {
                List<Statement> statements = MySqlHelper.parseMultiStatement(testSql);
                System.out.println("解析结果: 成功，语句数量 = " + statements.size());
                if (statements.size() > 0) {
                    System.out.println("修复后SQL: " + statements.get(0).getSql());
                }
            } catch (Exception e) {
                System.out.println("解析失败: " + e.getMessage());
            }
        }

        // 测试新的保留字验证系统
        System.out.println("\n=== 测试新的保留字验证系统 ===");
        testNewReservedWordValidation();
    }

    private static void testNewReservedWordValidation() {
        String[] testSqls = {
            "SELECT id, name FROM products;",  // 正常SQL
            "SELECT order FROM test_table;",   // 保留字作为列名
            "SELECT id, ROW_NUMBER() OVER (PARTITION BY category ORDER BY amount DESC) as rank FROM products;",  // 保留字作为别名
            "CREATE TABLE `order` (id INT, `rank` VARCHAR(50));",  // 已正确引用的保留字
            "CREATE TABLE test (id INT, `group` VARCHAR(50), table_name VARCHAR(100));"  // 混合情况
        };

        for (String sql : testSqls) {
            System.out.println("\n测试SQL: " + sql);
            try {
                List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
                System.out.println("解析结果: 成功，语句数量 = " + statements.size());
                if (statements.size() > 0) {
                    System.out.println("语句类型: " + statements.get(0).getStatementType());
                }
            } catch (Exception e) {
                System.out.println("解析失败: " + e.getMessage());
            }
        }
    }
}
