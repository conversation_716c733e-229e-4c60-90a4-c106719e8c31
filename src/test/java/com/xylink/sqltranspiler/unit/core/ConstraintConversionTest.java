package com.xylink.sqltranspiler.unit.core;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseConversionTest;

@DisplayName("约束转换测试")
public class ConstraintConversionTest extends BaseConversionTest {

    @Test
    @DisplayName("外键约束分离测试")
    void testForeignKeyConstraintSeparation() throws Exception {
        // 使用简化的外键测试SQL，避免引用不存在的表
        String mysqlSql = """
            CREATE TABLE parent_table (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                name varchar(100) NOT NULL,
                PRIMARY KEY (id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

            CREATE TABLE child_table (
                id bigint(20) NOT NULL AUTO_INCREMENT,
                parent_id bigint(20) NOT NULL,
                description varchar(255),
                PRIMARY KEY (id),
                CONSTRAINT fk_parent FOREIGN KEY (parent_id) REFERENCES parent_table (id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """;

        String damengSql = convertMySqlToDameng(mysqlSql);

        assertBasicConversionRequirements(damengSql);
        assertConstraintSeparation(damengSql);
        assertTrue(damengSql.contains("ALTER TABLE \"child_table\" ADD CONSTRAINT \"fk_parent\" FOREIGN KEY (\"parent_id\") REFERENCES \"parent_table\" (\"id\")"),
                   "应包含标准的外键约束语句");
    }

    @Test
    @DisplayName("唯一索引约束分离测试")
    void testUniqueIndexConstraintSeparation() throws Exception {
        String mysqlSql = "CREATE TABLE `idx_test` (`id` int, `email` varchar(255), UNIQUE KEY `uk_email` (`email`));";
        String damengSql = convertMySqlToDameng(mysqlSql);

        assertBasicConversionRequirements(damengSql);
        assertConstraintSeparation(damengSql);
        assertTrue(damengSql.contains("CREATE UNIQUE INDEX \"udx_idx_test_uk_email\" ON \"idx_test\" (\"email\")"),
                   "应包含标准的唯一索引创建语句");
    }
}
