package com.xylink.sqltranspiler.unit.core;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * MATCH AGAINST语法支持情况调试测试
 * 根据数据库规则：严格禁止简化测试用例SQL，必须实现对真实MySQL语法的支持
 */
public class MatchAgainstSupportTest {

    private static final Logger log = LoggerFactory.getLogger(MatchAgainstSupportTest.class);
    private Transpiler transpiler;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }

    @Test
    void debugMatchAgainstSupport() {
        // 测试基本的MATCH AGAINST语法
        String basicMatchSql = "SELECT * FROM articles WHERE MATCH(title) AGAINST('数据库');";
        
        log.info("测试基本MATCH AGAINST语法: {}", basicMatchSql);
        
        TranspilationResult damengResult = transpiler.transpile(basicMatchSql, "mysql", "dameng");
        log.info("达梦转换结果: '{}'", damengResult.translatedSql());
        log.info("达梦成功数: {}, 失败数: {}", damengResult.successCount(), damengResult.failureCount());
        log.info("达梦问题列表: {}", damengResult.issues());
        
        // 测试布尔模式MATCH AGAINST语法
        String booleanMatchSql = "SELECT * FROM articles WHERE MATCH(title) AGAINST('+数据库 -Oracle' IN BOOLEAN MODE);";
        
        log.info("测试布尔模式MATCH AGAINST语法: {}", booleanMatchSql);
        
        TranspilationResult booleanResult = transpiler.transpile(booleanMatchSql, "mysql", "dameng");
        log.info("布尔模式转换结果: '{}'", booleanResult.translatedSql());
        log.info("布尔模式成功数: {}, 失败数: {}", booleanResult.successCount(), booleanResult.failureCount());
        log.info("布尔模式问题列表: {}", booleanResult.issues());
        
        // 测试查询扩展模式MATCH AGAINST语法
        String expansionMatchSql = "SELECT * FROM articles WHERE MATCH(title) AGAINST('数据库' WITH QUERY EXPANSION);";
        
        log.info("测试查询扩展MATCH AGAINST语法: {}", expansionMatchSql);
        
        TranspilationResult expansionResult = transpiler.transpile(expansionMatchSql, "mysql", "dameng");
        log.info("查询扩展转换结果: '{}'", expansionResult.translatedSql());
        log.info("查询扩展成功数: {}, 失败数: {}", expansionResult.successCount(), expansionResult.failureCount());
        log.info("查询扩展问题列表: {}", expansionResult.issues());
    }
}
