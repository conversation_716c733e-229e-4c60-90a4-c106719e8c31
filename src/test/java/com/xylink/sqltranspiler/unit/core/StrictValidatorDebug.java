package com.xylink.sqltranspiler.unit.core;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 严格SQL验证器调试程序
 * 
 * 用于调试和分析SQL解析过程
 */
public class StrictValidatorDebug {
    
    public static void main(String[] args) {
        System.out.println("=".repeat(80));
        System.out.println("严格SQL验证器调试程序");
        System.out.println("=".repeat(80));
        System.out.println();
        
        // 测试CREATE TABLE解析
        testCreateTableParsing();
        
        // 测试列定义解析
        testColumnDefinitionParsing();
        
        // 测试默认值解析
        testDefaultValueParsing();
        
        // 测试ON UPDATE解析
        testOnUpdateParsing();
        
        System.out.println("=".repeat(80));
        System.out.println("调试完成");
        System.out.println("=".repeat(80));
    }
    
    private static void testCreateTableParsing() {
        System.out.println("1. CREATE TABLE解析测试");
        System.out.println("-".repeat(50));
        
        String sql = """
            CREATE TABLE contact1.t_en_department_extend (
                id varchar(64) not null,
                dep_id bigint(20) not null,
                field_code varchar(64) not null,
                field_value varchar(256) not null,
                create_time bigint(20) default '0',
                update_time bigint(20) default '0',
                primary key (id)
            );
            """;
        
        Pattern createTablePattern = Pattern.compile(
            "CREATE\\s+TABLE\\s+(?:IF\\s+NOT\\s+EXISTS\\s+)?([^\\s(]+)\\s*\\((.*)\\)\\s*(?:;|$)",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        
        Matcher tableMatcher = createTablePattern.matcher(sql);
        
        if (tableMatcher.find()) {
            String tableName = tableMatcher.group(1);
            String columnDefinitions = tableMatcher.group(2);
            
            System.out.println("表名: " + tableName);
            System.out.println("列定义: " + columnDefinitions);
            System.out.println();
            
            // 分析列定义 - 使用智能分割
            String[] columns = splitColumnDefinitions(columnDefinitions);
            System.out.println("智能分割后的列定义:");
            for (int i = 0; i < columns.length; i++) {
                System.out.println("  列" + (i+1) + ": [" + columns[i].trim() + "]");
            }
        } else {
            System.out.println("无法匹配CREATE TABLE语句");
        }
        
        System.out.println();
    }
    
    private static void testColumnDefinitionParsing() {
        System.out.println("2. 列定义解析测试");
        System.out.println("-".repeat(50));
        
        String[] testColumns = {
            "id varchar(64) not null",
            "create_time bigint(20) default '0'",
            "update_time bigint(20) default '0'",
            "primary key (id)"
        };
        
        Pattern improvedColumnPattern = Pattern.compile(
            "^([`\"']?\\w+[`\"']?)\\s+([\\w()\\s,]+?)(?:\\s+(.*?))?$",
            Pattern.CASE_INSENSITIVE | Pattern.DOTALL
        );
        
        for (String column : testColumns) {
            System.out.println("测试列: [" + column + "]");
            
            // 跳过约束定义
            if (column.toUpperCase().matches("^(PRIMARY\\s+KEY|KEY|INDEX|UNIQUE|CONSTRAINT|FOREIGN\\s+KEY).*")) {
                System.out.println("  -> 跳过约束定义");
                System.out.println();
                continue;
            }
            
            Matcher columnMatcher = improvedColumnPattern.matcher(column);
            
            if (columnMatcher.find()) {
                String columnName = columnMatcher.group(1);
                String dataTypeAndModifiers = columnMatcher.group(2);
                String attributes = columnMatcher.group(3);
                
                System.out.println("  列名: [" + columnName + "]");
                System.out.println("  数据类型: [" + dataTypeAndModifiers + "]");
                System.out.println("  属性: [" + attributes + "]");
                
                // 提取基本数据类型
                String dataType = extractDataTypeFromDefinition(dataTypeAndModifiers);
                System.out.println("  基本类型: [" + dataType + "]");
            } else {
                System.out.println("  -> 无法解析");
            }
            
            System.out.println();
        }
    }
    
    private static void testDefaultValueParsing() {
        System.out.println("3. 默认值解析测试");
        System.out.println("-".repeat(50));
        
        String[] testAttributes = {
            "not null",
            "default '0'",
            "default '0' not null",
            "default 100",
            "default current_timestamp",
            "default now() on update now()"
        };
        
        Pattern defaultPattern = Pattern.compile(
            "DEFAULT\\s+([^\\s]+(?:\\([^)]*\\))?|'[^']*')",
            Pattern.CASE_INSENSITIVE
        );
        
        for (String attributes : testAttributes) {
            System.out.println("测试属性: [" + attributes + "]");
            
            Matcher defaultMatcher = defaultPattern.matcher(attributes);
            
            if (defaultMatcher.find()) {
                String defaultValue = defaultMatcher.group(1).trim();
                System.out.println("  默认值: [" + defaultValue + "]");
                
                // 检查是否为数值类型使用引号
                if (defaultValue.startsWith("'") && defaultValue.endsWith("'")) {
                    String innerValue = defaultValue.substring(1, defaultValue.length() - 1);
                    System.out.println("  引号内容: [" + innerValue + "]");
                    System.out.println("  是否为数值: " + isValidNumeric(innerValue));
                }
            } else {
                System.out.println("  -> 无默认值");
            }
            
            System.out.println();
        }
    }
    
    private static void testOnUpdateParsing() {
        System.out.println("4. ON UPDATE解析测试");
        System.out.println("-".repeat(50));
        
        String[] testAttributes = {
            "default now() on update now()",
            "on update current_timestamp",
            "default current_timestamp on update current_timestamp"
        };
        
        Pattern improvedOnUpdatePattern = Pattern.compile(
            "ON\\s+UPDATE\\s+([^\\s]+(?:\\([^)]*\\))?)",
            Pattern.CASE_INSENSITIVE
        );
        
        for (String attributes : testAttributes) {
            System.out.println("测试属性: [" + attributes + "]");
            
            Matcher onUpdateMatcher = improvedOnUpdatePattern.matcher(attributes);
            
            if (onUpdateMatcher.find()) {
                String onUpdateValue = onUpdateMatcher.group(1).trim();
                System.out.println("  ON UPDATE值: [" + onUpdateValue + "]");
                
                // 检查是否为NOW()
                if (onUpdateValue.toUpperCase().contains("NOW()")) {
                    System.out.println("  -> 检测到NOW()，这是错误的语法");
                }
            } else {
                System.out.println("  -> 无ON UPDATE子句");
            }
            
            System.out.println();
        }
    }
    
    private static String extractDataTypeFromDefinition(String dataTypeAndModifiers) {
        // 移除修饰符，提取基本类型
        String cleaned = dataTypeAndModifiers.trim().toUpperCase();
        
        // 移除常见修饰符
        cleaned = cleaned.replaceAll("\\s+(UNSIGNED|ZEROFILL|NOT\\s+NULL|NULL|AUTO_INCREMENT).*", "");
        
        // 提取类型名（可能包含长度）
        Pattern typePattern = Pattern.compile("^(\\w+)(?:\\([^)]*\\))?");
        Matcher typeMatcher = typePattern.matcher(cleaned);
        
        if (typeMatcher.find()) {
            return typeMatcher.group(1);
        }
        
        return cleaned.split("\\s+")[0];
    }
    
    private static boolean isValidNumeric(String value) {
        try {
            Double.parseDouble(value);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 智能分割列定义 - 考虑括号内的逗号
     */
    private static String[] splitColumnDefinitions(String columnDefs) {
        java.util.List<String> columns = new java.util.ArrayList<>();
        StringBuilder currentColumn = new StringBuilder();
        int parenthesesLevel = 0;
        boolean inQuotes = false;
        char quoteChar = 0;

        for (int i = 0; i < columnDefs.length(); i++) {
            char c = columnDefs.charAt(i);

            // 处理引号
            if ((c == '\'' || c == '"' || c == '`') && !inQuotes) {
                inQuotes = true;
                quoteChar = c;
                currentColumn.append(c);
            } else if (c == quoteChar && inQuotes) {
                inQuotes = false;
                quoteChar = 0;
                currentColumn.append(c);
            } else if (inQuotes) {
                currentColumn.append(c);
            } else if (c == '(') {
                parenthesesLevel++;
                currentColumn.append(c);
            } else if (c == ')') {
                parenthesesLevel--;
                currentColumn.append(c);
            } else if (c == ',' && parenthesesLevel == 0) {
                // 只有在括号外的逗号才分割
                columns.add(currentColumn.toString().trim());
                currentColumn = new StringBuilder();
            } else {
                currentColumn.append(c);
            }
        }

        // 添加最后一列
        if (currentColumn.length() > 0) {
            columns.add(currentColumn.toString().trim());
        }

        return columns.toArray(new String[0]);
    }
}
