package com.xylink.sqltranspiler.unit.core.parser;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * MySQL解析器健壮性测试
 * 
 * 测试解析器在各种边界情况和错误情况下的健壮性
 * 基于MySQL 8.4官方文档语法规范进行测试
 * 官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 */
@DisplayName("MySQL解析器健壮性测试")
public class MySqlParserRobustnessTest {
    
    private static final Logger logger = LoggerFactory.getLogger(MySqlParserRobustnessTest.class);
    
    @BeforeEach
    void setUp() {
        logger.info("开始执行MySQL解析器健壮性测试");
    }
    
    /**
     * 测试解析器对空输入的处理
     * 基于MySQL官方文档，空输入应该被正确处理
     */
    @Test
    @DisplayName("空输入处理测试")
    void testEmptyInputHandling() {
        logger.info("开始执行空输入处理测试");
        
        String[] emptyInputs = {
            "",           // 完全空字符串
            "   ",        // 只有空格
            "\n\n\n",     // 只有换行符
            "\t\t\t",     // 只有制表符
            "  \n  \t  ", // 混合空白字符
            null          // null输入
        };
        
        for (String input : emptyInputs) {
            logger.debug("测试空输入: '{}'", input);

            try {
                List<Statement> statements = MySqlHelper.parseMultiStatement(input);

                if (statements != null) {
                    logger.debug("空输入解析结果: {} 个语句", statements.size());
                } else {
                    logger.debug("空输入解析结果: null");
                }
            } catch (Exception e) {
                // 空输入可能导致解析异常，这是可以接受的行为
                logger.debug("空输入解析异常（可接受的行为）: {}", e.getClass().getSimpleName());
                assertNotNull(e.getMessage(), "异常消息不应为null");
            }
        }
        
        logger.info("空输入处理测试通过");
    }
    
    /**
     * 测试解析器对注释的处理
     * 基于MySQL官方文档注释语法规范
     */
    @Test
    @DisplayName("注释处理测试")
    void testCommentHandling() {
        logger.info("开始执行注释处理测试");
        
        String[] commentInputs = {
            "-- 这是单行注释",
            "/* 这是多行注释 */",
            "# 这是MySQL风格的注释",
            """
            -- 第一行注释
            /* 多行注释
               第二行 */
            # MySQL注释
            """,
            """
            /* 注释中包含SQL关键字 SELECT FROM WHERE */
            -- CREATE TABLE test (id INT);
            """,
            "SELECT 1; -- 行尾注释",
            "/* 注释 */ SELECT 1; /* 另一个注释 */"
        };
        
        for (String input : commentInputs) {
            logger.debug("测试注释输入: {}", input.replace("\n", "\\n"));

            try {
                List<Statement> statements = MySqlHelper.parseMultiStatement(input);

                if (statements != null) {
                    logger.debug("注释解析结果: {} 个语句", statements.size());

                    // 纯注释应该不产生语句，或者产生的语句应该是有效的
                    for (Statement stmt : statements) {
                        assertNotNull(stmt, "解析出的语句不应为null");
                        assertNotNull(stmt.getStatementType(), "语句类型不应为null");
                    }
                } else {
                    logger.debug("注释解析结果: null");
                }
            } catch (Exception e) {
                // 纯注释可能导致解析异常，这是可以接受的行为
                logger.debug("注释解析异常（可接受的行为）: {}", e.getClass().getSimpleName());
                assertNotNull(e.getMessage(), "异常消息不应为null");
            }
        }
        
        logger.info("注释处理测试通过");
    }
    
    /**
     * 测试解析器对语法错误的处理
     * 验证解析器的错误恢复能力
     */
    @Test
    @DisplayName("语法错误处理测试")
    void testSyntaxErrorHandling() {
        logger.info("开始执行语法错误处理测试");
        
        String[] syntaxErrors = {
            "SELECT * FROM;",                    // 缺少表名
            "CREATE TABLE (id INT);",            // 缺少表名
            "INSERT INTO VALUES (1);",           // 缺少表名
            "UPDATE SET id = 1;",                // 缺少表名
            "DELETE FROM WHERE id = 1;",         // 缺少表名
            "SELECT * FROM users WHERE;",        // WHERE子句不完整
            "CREATE TABLE users (id);",          // 列定义不完整
            "INSERT INTO users (id) VALUES;",    // VALUES子句不完整
            "SELECT * FROM users ORDER BY;",     // ORDER BY子句不完整
            "CREATE TABLE users (id INT,);",     // 多余的逗号
            "SELECT ,id FROM users;",            // 多余的逗号
            "SELCT * FROM users;",               // 拼写错误
            "CREATE TABEL users (id INT);",      // 拼写错误
            "SELECT * FROM users WHRE id = 1;",  // 拼写错误
        };
        
        int handledErrors = 0;
        
        for (String sql : syntaxErrors) {
            logger.debug("测试语法错误: {}", sql);
            
            try {
                List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
                
                if (statements != null) {
                    logger.debug("语法错误解析结果: {} 个语句", statements.size());
                    
                    // 如果解析成功，验证是否为fallback语句
                    for (Statement stmt : statements) {
                        if (stmt != null) {
                            logger.debug("Fallback语句类型: {}", stmt.getClass().getSimpleName());
                            assertNotNull(stmt.getStatementType(), "Fallback语句类型不应为null");
                        }
                    }
                    handledErrors++;
                } else {
                    logger.debug("语法错误解析结果: null");
                    handledErrors++;
                }
                
            } catch (Exception e) {
                // 预期的异常处理
                logger.debug("语法错误异常: {}", e.getClass().getSimpleName());
                assertNotNull(e.getMessage(), "异常消息不应为null");
                handledErrors++;
            }
        }
        
        // 验证解析器能够处理大部分语法错误
        assertTrue(handledErrors >= syntaxErrors.length * 0.8, 
            "解析器应该能够处理至少80%的语法错误");
        
        logger.info("语法错误处理测试通过，处理了 {}/{} 个错误", handledErrors, syntaxErrors.length);
    }
    
    /**
     * 测试解析器对大型SQL的处理
     * 验证解析器的性能和内存使用
     */
    @Test
    @DisplayName("大型SQL处理测试")
    void testLargeSqlHandling() {
        logger.info("开始执行大型SQL处理测试");
        
        // 生成大型CREATE TABLE语句
        StringBuilder largeSqlBuilder = new StringBuilder();
        largeSqlBuilder.append("CREATE TABLE large_table (\n");
        
        for (int i = 1; i <= 1000; i++) {
            largeSqlBuilder.append(String.format("  col_%d VARCHAR(255)", i));
            if (i < 1000) {
                largeSqlBuilder.append(",");
            }
            largeSqlBuilder.append("\n");
        }
        largeSqlBuilder.append(");");
        
        String largeSql = largeSqlBuilder.toString();
        
        logger.debug("生成大型SQL，长度: {} 字符", largeSql.length());
        
        // 测试解析性能
        long startTime = System.currentTimeMillis();
        
        assertDoesNotThrow(() -> {
            List<Statement> statements = MySqlHelper.parseMultiStatement(largeSql);
            
            assertNotNull(statements, "大型SQL解析结果不应为null");
            assertFalse(statements.isEmpty(), "大型SQL应该解析出语句");
            assertEquals(1, statements.size(), "应该解析出1个CREATE TABLE语句");
            
        }, "解析器应该能够处理大型SQL");
        
        long endTime = System.currentTimeMillis();
        long parseTime = endTime - startTime;
        
        logger.info("大型SQL解析耗时: {} ms", parseTime);
        assertTrue(parseTime < 5000, "大型SQL解析应该在5秒内完成");
        
        logger.info("大型SQL处理测试通过");
    }
    
    /**
     * 测试解析器对特殊字符的处理
     * 基于MySQL官方文档字符集和编码规范
     */
    @Test
    @DisplayName("特殊字符处理测试")
    void testSpecialCharacterHandling() {
        logger.info("开始执行特殊字符处理测试");
        
        String[] specialCharSqls = {
            "CREATE TABLE `用户表` (id INT, `姓名` VARCHAR(100));",  // 中文字符
            "INSERT INTO users (name) VALUES ('O\\'Brien');",        // 转义单引号
            "INSERT INTO users (name) VALUES (\"John \\\"Doe\\\"\");", // 转义双引号
            "SELECT * FROM users WHERE name LIKE '%\\%%';",          // 转义百分号
            "CREATE TABLE test (data VARCHAR(100) DEFAULT 'line1\\nline2');", // 换行符
            "INSERT INTO users (emoji) VALUES ('😀😃😄');",           // Emoji字符
            "CREATE TABLE `table-with-dashes` (id INT);",            // 带连字符的标识符
            "SELECT * FROM users WHERE name = 'Müller';",            // 特殊拉丁字符
        };
        
        for (String sql : specialCharSqls) {
            logger.debug("测试特殊字符SQL: {}", sql);
            
            assertDoesNotThrow(() -> {
                List<Statement> statements = MySqlHelper.parseMultiStatement(sql);
                
                if (statements != null && !statements.isEmpty()) {
                    logger.debug("特殊字符解析成功: {} 个语句", statements.size());
                    
                    for (Statement stmt : statements) {
                        assertNotNull(stmt, "解析出的语句不应为null");
                        assertNotNull(stmt.getSql(), "原始SQL不应为null");
                    }
                } else {
                    logger.debug("特殊字符解析结果为空");
                }
                
            }, "解析器应该能够处理特殊字符");
        }
        
        logger.info("特殊字符处理测试通过");
    }
    
    /**
     * 测试解析器的并发安全性
     * 验证多线程环境下的解析器稳定性
     */
    @Test
    @DisplayName("并发安全性测试")
    void testConcurrentSafety() {
        logger.info("开始执行并发安全性测试");
        
        String testSql = "SELECT * FROM users WHERE id = 1;";
        int threadCount = 10;
        int iterationsPerThread = 100;
        
        Thread[] threads = new Thread[threadCount];
        boolean[] results = new boolean[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    for (int j = 0; j < iterationsPerThread; j++) {
                        List<Statement> statements = MySqlHelper.parseMultiStatement(testSql);
                        assertNotNull(statements, "并发解析结果不应为null");
                        assertFalse(statements.isEmpty(), "并发解析应该产生语句");
                    }
                    results[threadIndex] = true;
                } catch (Exception e) {
                    logger.error("线程 {} 解析失败: {}", threadIndex, e.getMessage());
                    results[threadIndex] = false;
                }
            });
        }
        
        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }
        
        // 等待所有线程完成
        for (Thread thread : threads) {
            assertDoesNotThrow(() -> thread.join(5000), "线程应该在5秒内完成");
        }
        
        // 验证所有线程都成功
        for (int i = 0; i < threadCount; i++) {
            assertTrue(results[i], String.format("线程 %d 应该成功完成", i));
        }
        
        logger.info("并发安全性测试通过，{} 个线程各执行 {} 次解析", threadCount, iterationsPerThread);
    }
}
