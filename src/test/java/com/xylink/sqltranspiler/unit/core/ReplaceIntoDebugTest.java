package com.xylink.sqltranspiler.unit.core;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * REPLACE INTO语句调试测试
 */
public class ReplaceIntoDebugTest {

    private static final Logger log = LoggerFactory.getLogger(ReplaceIntoDebugTest.class);
    private Transpiler transpiler;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }

    @Test
    void debugReplaceIntoConversion() {
        String sql = "REPLACE INTO employees (id, name, salary) VALUES (1, 'John', 60000)";

        log.info("原始SQL: {}", sql);

        // 测试MySQL到达梦
        TranspilationResult damengResult = transpiler.transpile(sql, "mysql", "dameng");
        log.info("MySQL到达梦转换结果: '{}'", damengResult.translatedSql());
        log.info("MySQL到达梦成功数: {}, 失败数: {}", damengResult.successCount(), damengResult.failureCount());
        log.info("MySQL到达梦问题列表: {}", damengResult.issues());

        // 测试MySQL到金仓
        TranspilationResult kingbaseResult = transpiler.transpile(sql, "mysql", "kingbase");
        log.info("MySQL到金仓转换结果: '{}'", kingbaseResult.translatedSql());
        log.info("MySQL到金仓成功数: {}, 失败数: {}", kingbaseResult.successCount(), kingbaseResult.failureCount());
        log.info("MySQL到金仓问题列表: {}", kingbaseResult.issues());

        // 测试MySQL到神通
        TranspilationResult shentongResult = transpiler.transpile(sql, "mysql", "shentong");
        log.info("MySQL到神通转换结果: '{}'", shentongResult.translatedSql());
        log.info("MySQL到神通成功数: {}, 失败数: {}", shentongResult.successCount(), shentongResult.failureCount());
        log.info("MySQL到神通问题列表: {}", shentongResult.issues());

        // 测试带反引号的SQL
        String backtickSql = "REPLACE INTO `employees` (`id`, `name`, `salary`) VALUES (1, 'John', 60000)";
        log.info("带反引号的原始SQL: {}", backtickSql);

        TranspilationResult backtickResult = transpiler.transpile(backtickSql, "mysql", "dameng");
        log.info("带反引号达梦转换结果: '{}'", backtickResult.translatedSql());
        log.info("带反引号达梦成功数: {}, 失败数: {}", backtickResult.successCount(), backtickResult.failureCount());
    }
}
