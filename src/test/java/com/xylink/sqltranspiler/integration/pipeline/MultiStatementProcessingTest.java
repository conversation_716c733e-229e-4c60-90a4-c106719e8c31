package com.xylink.sqltranspiler.integration.pipeline;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 多语句处理测试
 * 基于MySQL 8.4、达梦、金仓、神通官方文档的多语句处理功能测试
 */
@DisplayName("多语句处理测试")
public class MultiStatementProcessingTest {

    private Transpiler transpiler;

    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }

    @Test
    @DisplayName("达梦数据库多语句处理 - DDL和DML混合")
    void testDamengMixedDdlDmlStatements() {
        // 根据达梦官方文档，测试DDL和DML语句的混合处理
        // 使用MySQL语法作为输入，转换为达梦语法
        String sql = """
            CREATE TABLE dm_employees (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                department VARCHAR(50),
                salary DECIMAL(10,2),
                hire_date DATE DEFAULT CURRENT_DATE
            );

            CREATE INDEX idx_dm_employees_dept ON dm_employees(department);

            INSERT INTO dm_employees (name, department, salary) VALUES
                ('张三', '技术部', 8000.00),
                ('李四', '销售部', 6000.00),
                ('王五', '技术部', 9000.00);

            UPDATE dm_employees SET salary = salary * 1.1 WHERE department = '技术部';

            CREATE VIEW dm_high_salary_employees AS
            SELECT name, department, salary FROM dm_employees WHERE salary > 8000;

            SELECT department, COUNT(*), AVG(salary) FROM dm_employees GROUP BY department;
            """;

        TranspilationResult result = transpiler.transpile(sql, "mysql", "dameng");
        
        assertNotNull(result, "达梦转换结果不应为空");
        assertTrue(result.successCount() >= 5,
            "达梦应该成功处理至少5个语句，实际: " + result.successCount());
        
        String translatedSql = result.translatedSql();
        assertNotNull(translatedSql, "达梦转换SQL不应为空");
        
        // 验证达梦特有的语法转换
        assertTrue(translatedSql.contains("IDENTITY(1,1)"), "应该包含达梦IDENTITY语法");
        assertTrue(translatedSql.contains("SYSDATE"), "应该包含达梦SYSDATE函数");
        assertTrue(translatedSql.contains("CREATE INDEX"), "应该包含索引创建");
        assertTrue(translatedSql.contains("CREATE VIEW"), "应该包含视图创建");
        
        System.out.println("✅ 达梦数据库DDL/DML混合语句处理测试通过");
        System.out.println("成功处理: " + result.successCount() + " 个语句");
    }

    @Test
    @DisplayName("金仓数据库多语句处理 - 批处理和事务")
    void testKingbaseBatchAndTransactionStatements() {
        // 根据金仓官方文档，测试批处理和事务处理
        String sql = """
            CREATE TABLE kb_orders (
                id INT AUTO_INCREMENT PRIMARY KEY,
                customer_id INT NOT NULL,
                order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                total_amount DECIMAL(12,2),
                status VARCHAR(20) DEFAULT 'PENDING'
            );
            
            CREATE TABLE kb_order_items (
                id INT AUTO_INCREMENT PRIMARY KEY,
                order_id INT,
                product_name VARCHAR(100),
                quantity INT,
                unit_price DECIMAL(10,2),
                FOREIGN KEY (order_id) REFERENCES kb_orders(id)
            );
            
            CREATE INDEX idx_kb_orders_customer ON kb_orders(customer_id);
            CREATE INDEX idx_kb_order_items_order ON kb_order_items(order_id);
            
            INSERT INTO kb_orders (customer_id, total_amount, status) VALUES 
                (1001, 299.99, 'COMPLETED'),
                (1002, 149.50, 'PENDING'),
                (1003, 599.00, 'COMPLETED');
            
            INSERT INTO kb_order_items (order_id, product_name, quantity, unit_price) VALUES 
                (1, 'Laptop', 1, 299.99),
                (2, 'Mouse', 2, 74.75),
                (3, 'Monitor', 1, 599.00);
            
            UPDATE kb_orders SET status = 'SHIPPED' WHERE status = 'COMPLETED';
            
            SELECT o.id, o.customer_id, o.total_amount, COUNT(oi.id) as item_count
            FROM kb_orders o
            LEFT JOIN kb_order_items oi ON o.id = oi.order_id
            GROUP BY o.id, o.customer_id, o.total_amount;
            """;

        TranspilationResult result = transpiler.transpile(sql, "mysql", "kingbase");
        
        assertNotNull(result, "金仓转换结果不应为空");
        assertTrue(result.successCount() >= 8, 
            "金仓应该成功处理至少8个语句，实际: " + result.successCount());
        
        String translatedSql = result.translatedSql();
        assertNotNull(translatedSql, "金仓转换SQL不应为空");
        
        // 验证金仓特有的语法转换
        assertTrue(translatedSql.contains("AUTO_INCREMENT") || translatedSql.contains("SERIAL"), "应该包含自增语法");
        assertTrue(translatedSql.contains("CREATE INDEX"), "应该包含索引创建");
        assertTrue(translatedSql.contains("LEFT JOIN"), "应该包含连接查询");
        // 注意：外键约束可能在转换过程中被处理为独立的ALTER TABLE语句
        
        System.out.println("✅ 金仓数据库批处理和事务语句处理测试通过");
        System.out.println("成功处理: " + result.successCount() + " 个语句");
    }

    @Test
    @DisplayName("神通数据库多语句处理 - 复杂查询和存储过程")
    void testShentongComplexQueriesAndProcedures() {
        // 根据神通官方文档，测试复杂查询和存储过程的多语句处理
        String sql = """
            CREATE TABLE st_products (
                id INT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                category VARCHAR(50),
                price DECIMAL(10,2),
                stock_quantity INT DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE TABLE st_sales (
                id INT PRIMARY KEY,
                product_id INT,
                sale_date DATE,
                quantity INT,
                sale_price DECIMAL(10,2)
            );
            
            CREATE INDEX idx_st_products_category ON st_products(category);
            CREATE INDEX idx_st_sales_product ON st_sales(product_id);
            CREATE INDEX idx_st_sales_date ON st_sales(sale_date);
            
            INSERT INTO st_products (id, name, category, price, stock_quantity) VALUES 
                (1, '笔记本电脑', '电子产品', 5999.00, 50),
                (2, '无线鼠标', '电子产品', 199.00, 100),
                (3, '编程书籍', '图书', 89.00, 200);
            
            INSERT INTO st_sales (id, product_id, sale_date, quantity, sale_price) VALUES 
                (1, 1, '2024-01-15', 2, 5999.00),
                (2, 2, '2024-01-16', 5, 199.00),
                (3, 3, '2024-01-17', 10, 89.00);
            
            UPDATE st_products SET stock_quantity = stock_quantity - 2 WHERE id = 1;
            UPDATE st_products SET stock_quantity = stock_quantity - 5 WHERE id = 2;
            UPDATE st_products SET stock_quantity = stock_quantity - 10 WHERE id = 3;
            
            SELECT 
                p.category,
                COUNT(DISTINCT p.id) as product_count,
                SUM(s.quantity * s.sale_price) as total_revenue,
                AVG(s.sale_price) as avg_price
            FROM st_products p
            LEFT JOIN st_sales s ON p.id = s.product_id
            WHERE s.sale_date >= '2024-01-01'
            GROUP BY p.category
            ORDER BY total_revenue DESC;
            """;

        TranspilationResult result = transpiler.transpile(sql, "mysql", "shentong");
        
        assertNotNull(result, "神通转换结果不应为空");
        assertTrue(result.successCount() >= 10, 
            "神通应该成功处理至少10个语句，实际: " + result.successCount());
        
        String translatedSql = result.translatedSql();
        assertNotNull(translatedSql, "神通转换SQL不应为空");
        
        // 验证神通数据库的语法转换
        assertTrue(translatedSql.contains("CREATE TABLE"), "应该包含表创建");
        assertTrue(translatedSql.contains("CREATE INDEX"), "应该包含索引创建");
        assertTrue(translatedSql.contains("INSERT INTO"), "应该包含数据插入");
        assertTrue(translatedSql.contains("UPDATE"), "应该包含数据更新");
        assertTrue(translatedSql.contains("LEFT JOIN"), "应该包含连接查询");
        assertTrue(translatedSql.contains("GROUP BY"), "应该包含分组查询");
        
        System.out.println("✅ 神通数据库复杂查询和存储过程语句处理测试通过");
        System.out.println("成功处理: " + result.successCount() + " 个语句");
    }

    @Test
    @DisplayName("跨数据库多语句处理一致性测试")
    void testCrossDatabaseMultiStatementConsistency() {
        // 测试相同的多语句SQL在不同数据库上的处理一致性
        String commonSql = """
            CREATE TABLE test_consistency (
                id INT PRIMARY KEY,
                name VARCHAR(100),
                value DECIMAL(10,2),
                created_at TIMESTAMP
            );
            
            INSERT INTO test_consistency (id, name, value) VALUES (1, 'Test', 100.00);
            
            UPDATE test_consistency SET value = 200.00 WHERE id = 1;
            
            SELECT * FROM test_consistency WHERE value > 150.00;
            """;

        String[] databases = {"dameng", "kingbase", "shentong"};
        
        for (String db : databases) {
            System.out.println("=== 测试 " + db + " 数据库 ===");
            
            TranspilationResult result = transpiler.transpile(commonSql, "mysql", db);
            assertNotNull(result, db + " 转换结果不应为空");
            
            // 所有数据库都应该能处理这些基本语句
            assertTrue(result.successCount() >= 4, 
                db + " 应该成功处理至少4个语句，实际: " + result.successCount());
            
            String translatedSql = result.translatedSql();
            assertNotNull(translatedSql, db + " 转换SQL不应为空");
            
            // 验证基本语句都被正确转换
            assertTrue(translatedSql.contains("CREATE TABLE"), db + " 应该包含CREATE TABLE");
            assertTrue(translatedSql.contains("INSERT INTO"), db + " 应该包含INSERT INTO");
            assertTrue(translatedSql.contains("UPDATE"), db + " 应该包含UPDATE");
            assertTrue(translatedSql.contains("SELECT"), db + " 应该包含SELECT");
            
            System.out.println(db + " 成功处理: " + result.successCount() + " 个语句");
        }
        
        System.out.println("✅ 跨数据库多语句处理一致性测试通过");
    }
}
