package com.xylink.sqltranspiler.integration.realworld;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * 全面的SQL转换器测试框架
 * 基于官方文档标准，确保所有SQL转换功能的正确性和完整性
 * 
 * 遵循官方文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: 基于提供的官方文档
 */
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@DisplayName("全面SQL转换器测试")
public class ComprehensiveSqlTranspilerTest {

    private static Transpiler transpiler;
    
    // 支持的数据库类型
    private static final List<String> SUPPORTED_DATABASES = Arrays.asList("mysql", "dameng", "kingbase", "shentong");
    
    @BeforeAll
    static void setUp() {
        transpiler = new Transpiler();
        System.out.println("=== 全面SQL转换器测试开始 ===");
        System.out.println("支持的数据库: " + SUPPORTED_DATABASES);
    }
    
    @AfterAll
    static void tearDown() {
        System.out.println("=== 全面SQL转换器测试结束 ===");
    }
    
    @BeforeEach
    void beforeEach(TestInfo testInfo) {
        System.out.println("\n--- 开始测试: " + testInfo.getDisplayName() + " ---");
    }
    
    @AfterEach
    void afterEach(TestInfo testInfo) {
        System.out.println("--- 结束测试: " + testInfo.getDisplayName() + " ---");
    }
    
    /**
     * 测试基本的数据库连通性和转换器初始化
     */
    @Test
    @Order(1)
    @DisplayName("转换器基本功能测试")
    void testTranspilerBasicFunctionality() {
        assertNotNull(transpiler, "转换器应该正确初始化");
        
        // 测试简单的SELECT语句转换
        String simpleSelect = "SELECT 1;";
        
        for (String sourceDb : SUPPORTED_DATABASES) {
            for (String targetDb : SUPPORTED_DATABASES) {
                if (!sourceDb.equals(targetDb)) {
                    TranspilationResult result = transpiler.transpile(simpleSelect, sourceDb, targetDb);
                    assertNotNull(result, String.format("从%s到%s的转换结果不应为空", sourceDb, targetDb));
                    assertTrue(result.successCount() >= 0, "成功计数应该非负");
                    assertTrue(result.failureCount() >= 0, "失败计数应该非负");
                }
            }
        }
    }
    
    /**
     * 测试数据库类型验证
     */
    @ParameterizedTest
    @ValueSource(strings = {"mysql", "dameng", "kingbase", "shentong"})
    @Order(2)
    @DisplayName("数据库类型验证测试")
    void testDatabaseTypeValidation(String dbType) {
        assertTrue(SUPPORTED_DATABASES.contains(dbType), 
                   "数据库类型 " + dbType + " 应该被支持");
        
        // 测试从该数据库类型转换到其他类型
        String testSql = "SELECT CURRENT_TIMESTAMP;";
        
        for (String targetDb : SUPPORTED_DATABASES) {
            if (!dbType.equals(targetDb)) {
                assertDoesNotThrow(() -> {
                    TranspilationResult result = transpiler.transpile(testSql, dbType, targetDb);
                    assertNotNull(result, String.format("从%s到%s的转换应该成功", dbType, targetDb));
                }, String.format("从%s到%s的转换不应抛出异常", dbType, targetDb));
            }
        }
    }
    
    /**
     * 测试空值和边界条件
     */
    @Test
    @Order(3)
    @DisplayName("空值和边界条件测试")
    void testNullAndBoundaryConditions() {
        // 测试空SQL
        assertDoesNotThrow(() -> {
            TranspilationResult result = transpiler.transpile("", "mysql", "dameng");
            assertNotNull(result, "空SQL转换结果不应为空");
        });
        
        // 测试只有空格的SQL
        assertDoesNotThrow(() -> {
            TranspilationResult result = transpiler.transpile("   ", "mysql", "dameng");
            assertNotNull(result, "空格SQL转换结果不应为空");
        });
        
        // 测试只有注释的SQL
        assertDoesNotThrow(() -> {
            TranspilationResult result = transpiler.transpile("-- This is a comment", "mysql", "dameng");
            assertNotNull(result, "注释SQL转换结果不应为空");
        });
        
        // 测试无效的数据库类型
        assertDoesNotThrow(() -> {
            TranspilationResult result = transpiler.transpile("SELECT 1;", "invalid", "mysql");
            assertNotNull(result, "无效数据库类型转换结果不应为空");
        });
    }
    
    /**
     * 测试基本SQL语句类型的转换
     */
    @ParameterizedTest
    @CsvSource({
        "SELECT * FROM users;, SELECT",
        "INSERT INTO users (name) VALUES ('test');, INSERT", 
        "UPDATE users SET name = 'updated';, UPDATE",
        "DELETE FROM users WHERE id = 1;, DELETE",
        "CREATE TABLE test (id INT);, CREATE",
        "DROP TABLE test;, DROP"
    })
    @Order(4)
    @DisplayName("基本SQL语句类型转换测试")
    void testBasicSqlStatementTypes(String sql, String statementType) {
        System.out.println("测试SQL类型: " + statementType + " - " + sql);
        
        // 测试从MySQL到其他数据库的转换
        for (String targetDb : Arrays.asList("dameng", "kingbase", "shentong")) {
            TranspilationResult result = transpiler.transpile(sql, "mysql", targetDb);
            assertNotNull(result, String.format("%s语句从MySQL到%s的转换结果不应为空", statementType, targetDb));
            
            // 记录转换结果
            System.out.println(String.format("  MySQL -> %s: 成功=%d, 失败=%d", 
                               targetDb, result.successCount(), result.failureCount()));
            
            if (result.translatedSql() != null && !result.translatedSql().trim().isEmpty()) {
                System.out.println("  转换结果: " + result.translatedSql().trim());
            }
            
            if (result.issues() != null && !result.issues().isEmpty()) {
                result.issues().forEach(issue -> 
                    System.out.println("  问题: " + issue.message()));
            }
        }
    }
    
    /**
     * 验证转换结果的基本属性
     */
    private void validateTranspilationResult(TranspilationResult result, String context) {
        assertNotNull(result, context + ": 转换结果不应为空");
        assertTrue(result.successCount() >= 0, context + ": 成功计数应该非负");
        assertTrue(result.failureCount() >= 0, context + ": 失败计数应该非负");
        
        // 如果有转换结果，应该不为空
        if (result.successCount() > 0) {
            assertNotNull(result.translatedSql(), context + ": 成功转换时应该有SQL结果");
            assertFalse(result.translatedSql().trim().isEmpty(), context + ": 转换结果不应为空字符串");
        }
        
        // 如果有失败，应该有问题描述
        if (result.failureCount() > 0) {
            assertNotNull(result.issues(), context + ": 失败时应该有问题描述");
            assertFalse(result.issues().isEmpty(), context + ": 问题列表不应为空");
        }
    }
    
    /**
     * 测试转换结果的一致性
     */
    @Test
    @Order(5)
    @DisplayName("转换结果一致性测试")
    void testTranspilationResultConsistency() {
        String testSql = "SELECT COUNT(*) FROM users WHERE created_at > '2023-01-01';";
        
        for (String sourceDb : SUPPORTED_DATABASES) {
            for (String targetDb : SUPPORTED_DATABASES) {
                if (!sourceDb.equals(targetDb)) {
                    TranspilationResult result = transpiler.transpile(testSql, sourceDb, targetDb);
                    String context = String.format("从%s到%s", sourceDb, targetDb);
                    validateTranspilationResult(result, context);
                    
                    // 多次转换应该得到相同结果
                    TranspilationResult result2 = transpiler.transpile(testSql, sourceDb, targetDb);
                    assertEquals(result.successCount(), result2.successCount(), 
                                context + ": 多次转换的成功计数应该一致");
                    assertEquals(result.failureCount(), result2.failureCount(), 
                                context + ": 多次转换的失败计数应该一致");
                }
            }
        }
    }
}
