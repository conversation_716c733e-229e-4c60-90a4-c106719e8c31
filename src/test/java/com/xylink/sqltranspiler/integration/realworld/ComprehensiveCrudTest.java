package com.xylink.sqltranspiler.integration.realworld;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;

/**
 * 全面的CRUD操作测试套件 - 补充缺失的常见语法和边界情况
 * 
 * 参考文档：
 * - MySQL: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 */
@DisplayName("全面CRUD操作测试")
public class ComprehensiveCrudTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    // ================================ CREATE TABLE 补充测试 ================================

    @Test
    @DisplayName("CREATE TABLE - 临时表")
    void testCreateTemporaryTable() {
        String sql = "CREATE TEMPORARY TABLE temp_users (id INT, name VARCHAR(50));";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE"));
        assertTrue(damengSql.contains("temp_users"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("CREATE TABLE - IF NOT EXISTS")
    void testCreateTableIfNotExists() {
        String sql = "CREATE TABLE IF NOT EXISTS users (id INT PRIMARY KEY, name VARCHAR(100));";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE TABLE"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("CREATE TABLE - 带索引和约束")
    void testCreateTableWithIndexesAndConstraints() {
        String sql = """
            CREATE TABLE products (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                category_id INT,
                price DECIMAL(10,2) DEFAULT 0.00,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_category (category_id),
                INDEX idx_name_price (name, price),
                UNIQUE KEY uk_name (name),
                FOREIGN KEY (category_id) REFERENCES categories(id)
            );
            """;
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("CREATE TABLE"));
        assertTrue(damengSql.contains("products"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ INSERT 补充测试 ================================

    @Test
    @DisplayName("INSERT - INSERT IGNORE")
    void testInsertIgnore() {
        String sql = "INSERT IGNORE INTO users (name, email) VALUES ('John', '<EMAIL>');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("INSERT - ON DUPLICATE KEY UPDATE")
    void testInsertOnDuplicateKeyUpdate() {
        String sql = "INSERT INTO users (id, name, email) VALUES (1, 'John', '<EMAIL>') ON DUPLICATE KEY UPDATE name = VALUES(name), email = VALUES(email);";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("INSERT - INSERT SELECT")
    void testInsertSelect() {
        String sql = "INSERT INTO user_backup (id, name, email) SELECT id, name, email FROM users WHERE created_at < '2023-01-01';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT"));
        assertTrue(damengSql.contains("user_backup"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("INSERT - 带函数和表达式")
    void testInsertWithFunctionsAndExpressions() {
        String sql = "INSERT INTO logs (user_id, action, created_at, data) VALUES (1, 'login', NOW(), CONCAT('User: ', 'John'));";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("INSERT"));
        assertTrue(damengSql.contains("logs"));
        assertTrue(damengSql.contains("SYSDATE")); // NOW() 应该转换为 SYSDATE
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ UPDATE 补充测试 ================================

    @Test
    @DisplayName("UPDATE - 带MySQL函数")
    void testUpdateWithMySqlFunctions() {
        String sql = "UPDATE users SET last_login = NOW(), updated_at = CURRENT_TIMESTAMP WHERE id = 1;";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);

        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.contains("SYSDATE")); // NOW() 应该转换为 SYSDATE
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("UPDATE - 带数值计算")
    void testUpdateWithCalculations() {
        String sql = "UPDATE products SET price = price * 1.1, discount = discount + 5 WHERE category = 'electronics';";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);

        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE"));
        assertTrue(damengSql.contains("products"));
        assertTrue(damengSql.contains("price"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("UPDATE - 带字符串函数")
    void testUpdateWithStringFunctions() {
        String sql = "UPDATE users SET email = LOWER(email), name = UPPER(name) WHERE id = 1;";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);

        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.contains("LOWER"));
        assertTrue(damengSql.contains("UPPER"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("UPDATE - 带ORDER BY和LIMIT")
    void testUpdateWithOrderByLimit() {
        String sql = "UPDATE users SET last_login = NOW() WHERE status = 'active' ORDER BY created_at DESC LIMIT 100;";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("UPDATE"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.contains("ORDER BY"));
        assertTrue(damengSql.contains("LIMIT"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ DELETE 补充测试 ================================

    @Test
    @DisplayName("DELETE - 多表DELETE")
    void testDeleteMultiTable() {
        String sql = "DELETE u, p FROM users u JOIN profiles p ON u.id = p.user_id WHERE u.status = 'deleted';";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("DELETE"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("DELETE - 带子查询")
    void testDeleteWithSubquery() {
        String sql = "DELETE FROM users WHERE id NOT IN (SELECT DISTINCT user_id FROM orders WHERE created_at > '2024-01-01');";
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("DELETE"));
        assertTrue(damengSql.contains("users"));
        assertTrue(damengSql.endsWith(";"));
    }

    // ================================ SELECT 补充测试 ================================

    @Test
    @DisplayName("SELECT - 聚合函数和别名")
    void testSelectWithAggregatesAndAliases() {
        String sql = "SELECT COUNT(*) as total_count, MAX(salary) as max_salary, MIN(salary) as min_salary, AVG(salary) as avg_salary FROM employees;";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);

        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("COUNT"));
        assertTrue(damengSql.contains("MAX"));
        assertTrue(damengSql.contains("MIN"));
        assertTrue(damengSql.contains("AVG"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - DISTINCT和ORDER BY")
    void testSelectDistinctWithOrderBy() {
        String sql = "SELECT DISTINCT category, status FROM products ORDER BY category ASC, status DESC;";

        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);

        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("DISTINCT"));
        assertTrue(damengSql.contains("ORDER BY"));
        assertTrue(damengSql.endsWith(";"));
    }

    @Test
    @DisplayName("SELECT - 复杂JOIN")
    void testSelectComplexJoins() {
        String sql = """
            SELECT u.name, p.title, c.name as category, COUNT(o.id) as order_count
            FROM users u
            LEFT JOIN profiles p ON u.id = p.user_id
            INNER JOIN orders o ON u.id = o.user_id
            RIGHT JOIN categories c ON o.category_id = c.id
            WHERE u.status = 'active'
            GROUP BY u.id, p.id, c.id
            HAVING COUNT(o.id) > 5
            ORDER BY order_count DESC
            LIMIT 20;
            """;
        
        Statement statement = MySqlHelper.parseStatement(sql);
        assertNotNull(statement);
        
        String damengSql = generator.generate(statement);
        System.out.println("Generated SQL: " + damengSql);
        assertNotNull(damengSql);
        assertTrue(damengSql.contains("SELECT"));
        assertTrue(damengSql.contains("LEFT JOIN"));
        assertTrue(damengSql.contains("INNER JOIN"));
        assertTrue(damengSql.contains("RIGHT JOIN"));
        assertTrue(damengSql.endsWith(";"));
    }
}
