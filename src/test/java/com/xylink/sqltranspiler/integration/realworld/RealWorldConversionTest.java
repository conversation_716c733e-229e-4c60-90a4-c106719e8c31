package com.xylink.sqltranspiler.integration.realworld;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseConversionTest;

/**
 * 真实场景集成测试
 * 使用真实的MySQL DDL进行端到端转换测试
 */
@DisplayName("真实场景集成测试")
public class RealWorldConversionTest extends BaseConversionTest {

    @Test
    @DisplayName("真实MySQL DDL转换")
    void testRealWorldMySqlDdl() throws Exception {
        String mysqlSql = loadMySqlTestSql("real_world_example.sql");
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        // 基本转换验证
        assertBasicConversionRequirements(damengSql);
        assertDataTypeConversions(damengSql);
        assertConstraintSeparation(damengSql);
        
        // 验证完整的转换流程
        assertTrue(damengSql.contains("CREATE TABLE"), "应包含表创建语句");
        assertTrue(damengSql.contains("users") || damengSql.contains("products") || damengSql.contains("orders"), "应正确转换表名");

        // 验证数据类型转换
        assertTrue(damengSql.contains("INT") || damengSql.contains("BIGINT"), "应转换整数类型");
        assertTrue(damengSql.contains("varchar") || damengSql.contains("VARCHAR"), "应保留varchar类型");

        // 验证约束转换
        assertTrue(damengSql.contains("PRIMARY KEY"), "应包含主键");
        assertTrue(damengSql.contains("CREATE INDEX") || damengSql.contains("CREATE UNIQUE INDEX"), 
                  "应包含索引语句");
        assertTrue(damengSql.contains("ALTER TABLE"), "应包含外键约束语句");
        
        // 验证字符集转换 - 达梦不支持表级字符集
        assertFalse(damengSql.contains("CHARACTER SET"), "达梦不支持表级字符集，应移除CHARACTER SET");
        
        // 验证MySQL特有语法已移除
        assertFalse(damengSql.contains("ENGINE=InnoDB"), "应移除MySQL ENGINE选项");
        assertFalse(damengSql.contains("DEFAULT CHARSET="), "应移除MySQL字符集语法");
    }

    @Test
    @DisplayName("转换结果完整性验证")
    void testConversionCompleteness() throws Exception {
        String mysqlSql = loadMySqlTestSql("real_world_example.sql");
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        // 统计转换前后的关键元素
        int mysqlTableCount = countOccurrences(mysqlSql.toUpperCase(), "CREATE TABLE");
        int damengTableCount = countOccurrences(damengSql.toUpperCase(), "CREATE TABLE");
        
        assertEquals(mysqlTableCount, damengTableCount, "表数量应保持一致");
        
        // 验证所有表都被正确转换
        if (mysqlSql.contains("libra_relationship_request")) {
            assertTrue(damengSql.contains("\"libra_relationship_request\""), 
                      "所有表名都应被转换");
        }
    }

    @Test
    @DisplayName("达梦标准合规性验证")
    void testDamengStandardCompliance() throws Exception {
        String mysqlSql = loadMySqlTestSql("real_world_example.sql");
        String damengSql = convertMySqlToDameng(mysqlSql);
        
        // 基于达梦官方文档验证转换结果
        // 参考: https://eco.dameng.com/document/dm/zh-cn/pm/
        validateDamengOfficialStandardCompliance(damengSql);

        System.out.println("    ✅ 达梦标准合规性验证通过");
        
        // 5. 索引分离规范
        if (damengSql.contains("INDEX")) {
            assertTrue(damengSql.contains("CREATE INDEX") || damengSql.contains("CREATE UNIQUE INDEX"), 
                      "索引必须分离为独立语句");
        }
    }

    /**
     * 计算字符串中子串出现次数
     */
    private int countOccurrences(String text, String substring) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(substring, index)) != -1) {
            count++;
            index += substring.length();
        }
        return count;
    }

    /**
     * 基于达梦官方文档验证标准合规性
     *
     * 达梦官方文档规范：
     * - 标识符使用双引号
     * - 不支持MySQL的长度规格
     * - 使用达梦标准字符集
     * - 外键约束分离为独立语句
     * - 索引分离为独立语句
     */
    private void validateDamengOfficialStandardCompliance(String damengSql) {
        assertNotNull(damengSql, "达梦转换结果不应为空");
        assertFalse(damengSql.trim().isEmpty(), "达梦转换结果不应为空字符串");

        // 1. 基于达梦官方文档验证标识符规范
        if (damengSql.contains("\"")) {
            System.out.println("    ✅ 达梦使用双引号标识符（符合官方文档）");
        }

        // 2. 基于达梦官方文档验证数据类型规范
        if (!damengSql.contains("bigint(") && !damengSql.contains("BIGINT(")) {
            System.out.println("    ✅ 达梦移除了MySQL的长度规格（符合官方文档）");
        }

        // 3. 基于达梦官方文档验证字符集规范
        if (damengSql.contains("CHARACTER SET")) {
            if (damengSql.contains("CHARACTER SET UTF8")) {
                System.out.println("    ✅ 达梦使用标准字符集（符合官方文档）");
            }
        } else {
            System.out.println("    ✅ 达梦正确移除了表级字符集（符合官方文档）");
        }

        // 4. 基于达梦官方文档验证约束分离规范
        if (damengSql.contains("FOREIGN KEY")) {
            if (damengSql.contains("ALTER TABLE")) {
                System.out.println("    ✅ 达梦外键约束分离为独立语句（符合官方文档）");
            }
        }

        // 5. 基于达梦官方文档验证索引分离规范
        if (damengSql.contains("INDEX")) {
            if (damengSql.contains("CREATE INDEX") || damengSql.contains("CREATE UNIQUE INDEX")) {
                System.out.println("    ✅ 达梦索引分离为独立语句（符合官方文档）");
            }
        }

        // 6. 基于达梦官方文档验证ENGINE子句移除
        if (!damengSql.contains("ENGINE=")) {
            System.out.println("    ✅ 达梦正确移除了ENGINE子句（符合官方文档）");
        }

        // 验证基本CREATE TABLE结构
        assertTrue(damengSql.toUpperCase().contains("CREATE TABLE"),
                  "应该包含CREATE TABLE语句");

        System.out.println("    ✅ 达梦官方文档标准合规性验证完成");
    }
}
