package com.xylink.sqltranspiler.integration.realworld;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xylink.sqltranspiler.Transpiler;
import com.xylink.sqltranspiler.core.context.TranspilationResult;

/**
 * SQL转换功能全面审查测试
 * 
 * 基于四个数据库官方文档进行全面验证：
 * - MySQL 8.4: https://dev.mysql.com/doc/refman/8.4/en/
 * - 达梦: https://eco.dameng.com/document/dm/zh-cn/pm/
 * - 金仓: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
 * - 神通: 基于神通数据库官方SQL参考手册
 */
@DisplayName("SQL转换功能全面审查测试")
public class SqlTranspilerComprehensiveTest {
    
    private static final Logger log = LoggerFactory.getLogger(SqlTranspilerComprehensiveTest.class);
    
    private Transpiler transpiler;
    
    @BeforeEach
    void setUp() {
        transpiler = new Transpiler();
    }
    
    @Test
    @DisplayName("数据类型转换全面测试")
    void testDataTypeConversionComprehensive() {
        // 基于四个数据库官方文档的数据类型映射测试
        String[] testSqls = {
            // 数值类型测试
            "CREATE TABLE test_numeric (id TINYINT(1), age SMALLINT, count MEDIUMINT, total BIGINT);",
            "CREATE TABLE test_decimal (price DECIMAL(10,2), rate NUMERIC(8,4), amount FLOAT, ratio DOUBLE);",
            
            // 字符串类型测试
            "CREATE TABLE test_string (name VARCHAR(255), description TEXT, code CHAR(10), content LONGTEXT);",
            
            // 日期时间类型测试
            "CREATE TABLE test_datetime (created_date DATE, created_time TIME, updated_at TIMESTAMP, birth_year YEAR);",
            
            // 二进制类型测试
            "CREATE TABLE test_binary (data BINARY(16), content VARBINARY(255), file_data BLOB);",
            
            // 自增字段测试
            "CREATE TABLE test_auto_increment (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(100));",
            
            // 神通数据库特有类型测试
            "CREATE TABLE test_shentong_specific (id SERIAL, name NAME, flag BIT, xml_data XMLTYPE);"
        };
        
        for (String sql : testSqls) {
            log.info("Testing SQL: {}", sql);

            // 测试转换到达梦数据库 - 基于达梦官方文档验证
            TranspilationResult damengResult = transpiler.transpile(sql, "mysql", "dameng");
            assertNotNull(damengResult.translatedSql(), "达梦转换结果不应为空");

            // 基于达梦官方文档验证转换结果
            validateDamengComplexDdlConversion(sql, damengResult.translatedSql());

            // 测试转换到金仓数据库 - 基于金仓官方文档验证
            TranspilationResult kingbaseResult = transpiler.transpile(sql, "mysql", "kingbase");
            assertNotNull(kingbaseResult.translatedSql(), "金仓转换结果不应为空");

            // 基于金仓官方文档验证转换结果
            validateKingbaseComplexDdlConversion(sql, kingbaseResult.translatedSql());

            // 测试转换到神通数据库
            TranspilationResult shentongResult = transpiler.transpile(sql, "mysql", "shentong");
            assertNotNull(shentongResult.translatedSql(), "神通转换结果不应为空");
            assertFalse(shentongResult.translatedSql().contains("ERROR"), "神通转换不应包含错误: " + shentongResult.translatedSql());

            log.info("达梦结果: {}", damengResult.translatedSql());
            log.info("金仓结果: {}", kingbaseResult.translatedSql());
            log.info("神通结果: {}", shentongResult.translatedSql());
        }
    }
    
    @Test
    @DisplayName("复杂查询转换测试")
    void testComplexQueryConversion() {
        String[] complexQueries = {
            // JOIN查询测试
            "SELECT u.name, p.email FROM users u INNER JOIN profiles p ON u.id = p.user_id WHERE u.status = 'active';",
            
            // 子查询测试
            "SELECT * FROM orders WHERE customer_id IN (SELECT id FROM customers WHERE region = 'Asia');",
            
            // 聚合函数测试
            "SELECT COUNT(*), AVG(price), MAX(created_at) FROM products GROUP BY category HAVING COUNT(*) > 10;",
            
            // LIMIT分页测试
            "SELECT * FROM users ORDER BY created_at DESC LIMIT 10 OFFSET 20;",
            
            // CASE WHEN测试
            "SELECT name, CASE WHEN age < 18 THEN 'minor' WHEN age >= 65 THEN 'senior' ELSE 'adult' END as category FROM users;",
            
            // 函数转换测试
            "SELECT NOW(), CURDATE(), IFNULL(email, 'no-email'), CONCAT(first_name, ' ', last_name) FROM users;"
        };
        
        for (String sql : complexQueries) {
            log.info("Testing complex query: {}", sql);
            
            // 测试所有目标数据库
            String[] targets = {"dameng", "kingbase", "shentong"};

            for (String target : targets) {
                TranspilationResult result = transpiler.transpile(sql, "mysql", target);
                assertNotNull(result.translatedSql(), target + "转换结果不应为空");
                assertFalse(result.translatedSql().contains("ERROR"), target + "转换不应包含错误: " + result.translatedSql());

                log.info("{} 结果: {}", target, result.translatedSql());
            }
        }
    }
    
    @Test
    @DisplayName("DDL语句转换测试")
    void testDdlStatementConversion() {
        String[] ddlStatements = {
            // CREATE TABLE测试
            "CREATE TABLE IF NOT EXISTS users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(255) NOT NULL, email VARCHAR(255) UNIQUE, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;",
            
            // ALTER TABLE测试
            "ALTER TABLE users ADD COLUMN phone VARCHAR(20), DROP COLUMN old_field, MODIFY COLUMN name VARCHAR(300);",
            
            // CREATE INDEX测试
            "CREATE INDEX idx_user_email ON users(email);",
            "CREATE UNIQUE INDEX udx_user_phone ON users(phone);",
            
            // DROP语句测试
            "DROP TABLE IF EXISTS temp_table;",
            "DROP INDEX idx_user_email ON users;",
            
            // TRUNCATE测试
            "TRUNCATE TABLE logs;"
        };
        
        for (String sql : ddlStatements) {
            log.info("Testing DDL: {}", sql);
            
            String[] targets = {"dameng", "kingbase", "shentong"};

            for (String target : targets) {
                TranspilationResult result = transpiler.transpile(sql, "mysql", target);
                assertNotNull(result.translatedSql(), target + "转换结果不应为空");

                log.info("{} 结果: {}", target, result.translatedSql());
            }
        }
    }
    
    @Test
    @DisplayName("DML语句转换测试")
    void testDmlStatementConversion() {
        String[] dmlStatements = {
            // INSERT测试
            "INSERT INTO users (name, email) VALUES ('张三', '<EMAIL>'), ('李四', '<EMAIL>');",
            
            // INSERT INTO...SELECT测试
            "INSERT INTO user_backup SELECT * FROM users WHERE created_at < '2023-01-01';",
            
            // UPDATE测试
            "UPDATE users SET email = '<EMAIL>', updated_at = NOW() WHERE id = 1;",
            
            // UPDATE JOIN测试
            "UPDATE users u INNER JOIN profiles p ON u.id = p.user_id SET u.status = 'verified' WHERE p.verified = 1;",
            
            // DELETE测试
            "DELETE FROM users WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 YEAR);",
            
            // DELETE JOIN测试
            "DELETE u FROM users u INNER JOIN blacklist b ON u.email = b.email;"
        };
        
        for (String sql : dmlStatements) {
            log.info("Testing DML: {}", sql);
            
            String[] targets = {"dameng", "kingbase", "shentong"};

            for (String target : targets) {
                TranspilationResult result = transpiler.transpile(sql, "mysql", target);
                assertNotNull(result.translatedSql(), target + "转换结果不应为空");

                log.info("{} 结果: {}", target, result.translatedSql());
            }
        }
    }
    
    @Test
    @DisplayName("字符集和排序规则转换测试")
    void testCharsetAndCollationConversion() {
        String[] charsetSqls = {
            "CREATE TABLE test_charset (name VARCHAR(255)) DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;",
            "CREATE TABLE test_charset2 (content TEXT CHARACTER SET utf8 COLLATE utf8_general_ci);",
            "ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
        };
        
        for (String sql : charsetSqls) {
            log.info("Testing charset SQL: {}", sql);
            
            // 达梦数据库：utf8mb4 -> UTF8
            TranspilationResult damengResult = transpiler.transpile(sql, "mysql", "dameng");
            assertTrue(damengResult.translatedSql().contains("UTF8") || !damengResult.translatedSql().contains("utf8mb4"),
                "达梦应该转换字符集: " + damengResult.translatedSql());

            // 神通数据库：utf8mb4 -> UTF8（注意神通使用UTF8不是UTF-8）
            TranspilationResult shentongResult = transpiler.transpile(sql, "mysql", "shentong");
            assertNotNull(shentongResult.translatedSql(), "神通转换结果不应为空");

            log.info("达梦字符集结果: {}", damengResult.translatedSql());
            log.info("神通字符集结果: {}", shentongResult.translatedSql());
        }
    }

    /**
     * 基于达梦官方文档验证复杂DDL转换
     *
     * 达梦官方文档规范：
     * - AUTO_INCREMENT → IDENTITY(1,1)
     * - ENGINE子句应该被移除
     * - DEFAULT CHARSET应该被转换为CHARACTER SET
     * - 复杂数据类型应该正确映射
     */
    private void validateDamengComplexDdlConversion(String originalSql, String translatedSql) {
        assertNotNull(translatedSql, "达梦转换结果不应为空");
        assertFalse(translatedSql.trim().isEmpty(), "达梦转换结果不应为空字符串");

        String upperOriginal = originalSql.toUpperCase();
        String upperTranslated = translatedSql.toUpperCase();

        // 基于达梦官方文档验证AUTO_INCREMENT转换
        if (upperOriginal.contains("AUTO_INCREMENT")) {
            if (upperTranslated.contains("IDENTITY")) {
                System.out.println("    ✅ 达梦正确将AUTO_INCREMENT转换为IDENTITY");
            } else if (upperTranslated.contains("AUTO_INCREMENT")) {
                System.out.println("    ⚠️ 达梦保持了AUTO_INCREMENT语法，需要验证兼容性");
            }
        }

        // 验证ENGINE子句处理
        if (upperOriginal.contains("ENGINE=") && !upperTranslated.contains("ENGINE=")) {
            System.out.println("    ✅ 达梦正确移除了ENGINE子句");
        }

        // 验证字符集处理
        if (upperOriginal.contains("DEFAULT CHARSET")) {
            if (upperTranslated.contains("CHARACTER SET")) {
                System.out.println("    ✅ 达梦正确转换了字符集设置");
            }
        }

        // 验证复杂数据类型转换
        if (upperOriginal.contains("TEXT") && upperTranslated.contains("CLOB")) {
            System.out.println("    ✅ 达梦正确将TEXT转换为CLOB");
        }

        if (upperOriginal.contains("JSON") && (upperTranslated.contains("CLOB") || upperTranslated.contains("JSON"))) {
            System.out.println("    ✅ 达梦正确处理了JSON类型");
        }

        System.out.println("    ✅ 达梦复杂DDL转换验证通过");
    }

    /**
     * 基于金仓官方文档验证复杂DDL转换
     *
     * 金仓官方文档规范：
     * - 良好的MySQL兼容性
     * - 支持大部分MySQL语法
     * - AUTO_INCREMENT保持或转换为SERIAL
     */
    private void validateKingbaseComplexDdlConversion(String originalSql, String translatedSql) {
        assertNotNull(translatedSql, "金仓转换结果不应为空");
        assertFalse(translatedSql.trim().isEmpty(), "金仓转换结果不应为空字符串");

        String upperOriginal = originalSql.toUpperCase();
        String upperTranslated = translatedSql.toUpperCase();

        // 基于金仓官方文档验证MySQL兼容性
        if (upperOriginal.contains("AUTO_INCREMENT")) {
            if (upperTranslated.contains("AUTO_INCREMENT")) {
                System.out.println("    ✅ 金仓保持了AUTO_INCREMENT语法（良好兼容性）");
            } else if (upperTranslated.contains("SERIAL")) {
                System.out.println("    ✅ 金仓将AUTO_INCREMENT转换为SERIAL");
            }
        }

        // 验证ENGINE子句处理 - 金仓可能保持或移除
        if (upperOriginal.contains("ENGINE=")) {
            if (upperTranslated.contains("ENGINE=")) {
                System.out.println("    ✅ 金仓保持了ENGINE子句（MySQL兼容性）");
            } else {
                System.out.println("    ✅ 金仓移除了ENGINE子句");
            }
        }

        // 验证字符集处理 - 金仓应该有良好的字符集支持
        if (upperOriginal.contains("CHARSET") || upperOriginal.contains("CHARACTER SET")) {
            System.out.println("    ✅ 金仓正确处理了字符集设置");
        }

        // 验证复杂数据类型 - 金仓应该支持大部分MySQL数据类型
        if (upperOriginal.contains("JSON")) {
            if (upperTranslated.contains("JSON") || upperTranslated.contains("JSONB") || upperTranslated.contains("TEXT")) {
                System.out.println("    ✅ 金仓正确处理了JSON类型");
            }
        }

        System.out.println("    ✅ 金仓复杂DDL转换验证通过");
    }

}
