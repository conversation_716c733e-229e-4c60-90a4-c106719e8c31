package com.xylink.sqltranspiler.integration.conversion.dameng;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.dialects.dameng.DamengGenerator;
import com.xylink.sqltranspiler.infrastructure.parser.MySqlHelper;
import com.xylink.sqltranspiler.infrastructure.parser.antlr.ParseException;

/**
 * 达梦数据库转换集成测试
 * 
 * 这个测试类验证了完整的MySQL到达梦数据库转换流程，
 * 包括我们修复的所有问题的综合场景
 */
@DisplayName("达梦数据库转换集成测试")
public class DamengConversionIntegrationTest {

    private DamengGenerator generator;

    @BeforeEach
    void setUp() {
        generator = new DamengGenerator();
    }

    @Test
    @DisplayName("测试完整的用户表转换场景")
    void testCompleteUserTableConversion() {
        String mysqlSql = """
            -- Test MySQL to Dameng conversion
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(100) NOT NULL,
                email VARCHAR(255) UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            
            INSERT INTO users (username, email) VALUES
            ('John Doe', '<EMAIL>'),
            ('Jane Smith', '<EMAIL>');
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(mysqlSql);
        assertEquals(2, statements.size());
        
        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            result.append(generator.generate(statement)).append("\n");
        }
        
        String damengSql = result.toString();
        
        // 根据达梦官方文档修正后，验证CREATE TABLE转换
        assertTrue(damengSql.contains("CREATE TABLE users"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("id INT IDENTITY(1,1)"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("username VARCHAR(100) NOT NULL"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("email VARCHAR(255)"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("created_at TIMESTAMP DEFAULT SYSDATE"));  // 普通标识符不需要双引号
        assertFalse(damengSql.contains("CHARACTER SET")); // 达梦不支持表级字符集
        assertFalse(damengSql.contains("ENGINE=InnoDB"));
        assertFalse(damengSql.contains("AUTO_INCREMENT"));
        assertFalse(damengSql.contains("CURRENT_TIMESTAMP"));
        
        // 根据达梦官方文档修正后，验证INSERT转换
        assertTrue(damengSql.contains("INSERT INTO users"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("(username, email)"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("('John Doe', '<EMAIL>')"));
        assertTrue(damengSql.contains("('Jane Smith', '<EMAIL>')"));
    }

    @Test
    @DisplayName("测试带反引号关键字的复杂表转换")
    void testComplexTableWithBacktickKeywords() {
        String mysqlSql = """
            CREATE TABLE `order_details` (
                `order_id` INT AUTO_INCREMENT PRIMARY KEY,
                `user` VARCHAR(100) NOT NULL,
                `name` VARCHAR(255),
                `status` ENUM('pending', 'completed', 'cancelled') DEFAULT 'pending',
                `type` VARCHAR(50),
                `class` VARCHAR(50),
                `group` VARCHAR(50),
                `index` INT,
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY `idx_order_user` (`order_id`, `user`),
                INDEX `idx_status` (`status`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='Order details table';
            
            INSERT INTO `order_details` (`user`, `name`, `status`, `type`) VALUES
            ('admin', 'Test Order', 'pending', 'premium'),
            ('user1', 'Another Order', 'completed', 'standard');
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(mysqlSql);
        assertEquals(2, statements.size());
        
        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            result.append(generator.generate(statement)).append("\n");
        }
        
        String damengSql = result.toString();
        
        // 根据达梦官方文档修正后，验证表名和所有MySQL关键字字段名的转换
        assertTrue(damengSql.contains("CREATE TABLE order_details"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("order_id INT IDENTITY(1,1)"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("\"user\""));    // user是达梦保留关键字，需要双引号
        assertTrue(damengSql.contains("name"));        // name不是达梦保留关键字，不需要双引号
        assertTrue(damengSql.contains("status"));      // status不是达梦保留关键字，不需要双引号
        assertTrue(damengSql.contains("\"type\""));    // type是达梦保留关键字，需要双引号
        assertTrue(damengSql.contains("\"class\""));   // class是达梦保留关键字，需要双引号
        assertTrue(damengSql.contains("\"group\""));   // group是达梦保留关键字，需要双引号
        assertTrue(damengSql.contains("\"index\""));   // index是达梦保留关键字，需要双引号
        
        // 根据达梦官方文档修正后，验证INSERT语句中的反引号转换
        assertTrue(damengSql.contains("INSERT INTO order_details"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("(\"user\", name, status, \"type\")"));  // 只有保留关键字需要双引号
        assertTrue(damengSql.contains("('admin', 'Test Order', 'pending', 'premium')"));
        assertTrue(damengSql.contains("('user1', 'Another Order', 'completed', 'standard')"));
    }

    @Test
    @DisplayName("测试电商订单系统转换场景")
    void testEcommerceOrderSystemConversion() {
        String mysqlSql = """
            CREATE TABLE `products` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `name` VARCHAR(255) NOT NULL,
                `price` DECIMAL(10,2) NOT NULL,
                `stock` INT DEFAULT 0,
                `category_id` INT,
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

            CREATE TABLE `orders` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `user_id` INT NOT NULL,
                `total_amount` DECIMAL(10,2) NOT NULL,
                `status` ENUM('pending', 'paid', 'shipped', 'delivered') DEFAULT 'pending',
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

            INSERT INTO `products` (`name`, `price`, `stock`, `category_id`) VALUES
            ('iPhone 15', 999.99, 50, 1),
            ('MacBook Pro', 1999.99, 20, 2),
            ('AirPods', 199.99, 100, 3);

            INSERT INTO `orders` (`user_id`, `total_amount`, `status`) VALUES
            (1, 999.99, 'pending'),
            (2, 1999.99, 'paid');
            """;

        List<Statement> statements = MySqlHelper.parseMultiStatement(mysqlSql);
        assertEquals(4, statements.size());

        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            String converted = generator.generate(statement);
            result.append(converted).append("\n");
        }

        String damengSql = result.toString();

        // 验证表结构转换（根据达梦官方文档）
        assertTrue(damengSql.contains("CREATE TABLE products"), "应包含products表");
        assertTrue(damengSql.contains("CREATE TABLE orders"), "应包含orders表");
        assertTrue(damengSql.contains("IDENTITY(1,1)"), "应转换AUTO_INCREMENT为IDENTITY");
        assertTrue(damengSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");

        // 验证ENUM转换（根据达梦官方文档，转换为VARCHAR + CHECK约束）
        assertTrue(damengSql.contains("VARCHAR") && (damengSql.contains("CHECK") || damengSql.contains("status")),
                  "应转换ENUM类型为VARCHAR");

        // 验证INSERT语句
        assertTrue(damengSql.contains("INSERT INTO products"), "应包含products INSERT语句");
        assertTrue(damengSql.contains("INSERT INTO orders"), "应包含orders INSERT语句");
        assertTrue(damengSql.contains("'iPhone 15'"), "应包含产品数据");
        assertTrue(damengSql.contains("999.99"), "应包含价格数据");
    }

    @Test
    @DisplayName("测试数据库和索引管理转换")
    void testDatabaseAndIndexManagementConversion() {
        String mysqlSql = """
            CREATE DATABASE `test_db` DEFAULT CHARSET=utf8mb4;

            USE `test_db`;

            CREATE INDEX `idx_user_email` ON `users` (`email`);
            CREATE UNIQUE INDEX `idx_user_username` ON `users` (`username`);

            DROP INDEX `idx_old_index` ON `users`;
            DROP TABLE IF EXISTS `temp_table`;
            """;

        List<Statement> statements = MySqlHelper.parseMultiStatement(mysqlSql);
        assertTrue(statements.size() >= 5, "应解析出至少5个语句");

        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            String converted = generator.generate(statement);
            result.append(converted).append("\n");
        }

        String damengSql = result.toString();

        // 验证数据库操作（根据达梦官方文档）
        assertTrue(damengSql.contains("CREATE DATABASE") || damengSql.contains("CREATE SCHEMA"),
                  "应支持CREATE DATABASE或CREATE SCHEMA");

        // 验证索引操作
        assertTrue(damengSql.contains("CREATE INDEX"), "应支持CREATE INDEX");
        assertTrue(damengSql.contains("CREATE UNIQUE INDEX"), "应支持CREATE UNIQUE INDEX");

        // 验证DROP操作
        assertTrue(damengSql.contains("DROP TABLE"), "应支持DROP TABLE");

        // 验证标识符转换（根据达梦官方文档，普通标识符不需要双引号）
        assertFalse(damengSql.contains("`"), "不应包含反引号");
        assertTrue(damengSql.contains("test_db") || damengSql.contains("\"test_db\""),
                  "数据库名应正确转换");
        assertTrue(damengSql.contains("users") || damengSql.contains("\"users\""),
                  "表名应正确转换");
    }

    @Test
    @DisplayName("测试事务和存储过程转换")
    void testTransactionAndProcedureConversion() {
        String mysqlSql = """
            START TRANSACTION;

            UPDATE `users` SET `last_login` = NOW() WHERE `id` = 1;
            INSERT INTO `user_logs` (`user_id`, `action`, `timestamp`) VALUES (1, 'login', NOW());

            COMMIT;
            """;

        List<Statement> statements = MySqlHelper.parseMultiStatement(mysqlSql);
        assertTrue(statements.size() >= 3, "应解析出至少3个语句");

        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            String converted = generator.generate(statement);
            result.append(converted).append("\n");
        }

        String damengSql = result.toString();

        // 验证事务支持（根据达梦官方文档）
        assertTrue(damengSql.contains("BEGIN") || damengSql.contains("START TRANSACTION"),
                  "应支持事务开始");
        assertTrue(damengSql.contains("COMMIT"), "应支持事务提交");

        // 验证NOW()函数转换
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP") || damengSql.contains("NOW"),
                  "NOW()应被正确转换");

        // 验证UPDATE和INSERT语句
        assertTrue(damengSql.contains("UPDATE"), "应支持UPDATE");
        assertTrue(damengSql.contains("INSERT INTO"), "应支持INSERT");

        // 验证标识符转换
        assertFalse(damengSql.contains("`"), "不应包含反引号");
    }

    @Test
    @DisplayName("测试多种数据类型和函数的转换")
    void testVariousDataTypesAndFunctions() {
        String mysqlSql = """
            CREATE TABLE `test_types` (
                `id` BIGINT AUTO_INCREMENT PRIMARY KEY,
                `name` VARCHAR(255) NOT NULL,
                `description` TEXT,
                `price` DECIMAL(10,2) DEFAULT 0.00,
                `quantity` INT DEFAULT 0,
                `is_active` BOOLEAN DEFAULT TRUE,
                `created_date` DATE DEFAULT (CURRENT_DATE),
                `created_time` DATETIME DEFAULT CURRENT_TIMESTAMP,
                `updated_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB;
            
            INSERT INTO `test_types` (`name`, `description`, `price`, `quantity`, `is_active`) VALUES
            ('Product A', 'Description for Product A', 99.99, 10, TRUE),
            ('Product B', 'Description for Product B', 149.50, 5, FALSE),
            ('Product C', NULL, 0.00, 0, TRUE);
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(mysqlSql);
        assertEquals(2, statements.size());
        
        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            result.append(generator.generate(statement)).append("\n");
        }
        
        String damengSql = result.toString();
        
        // 根据达梦官方文档修正后，验证数据类型转换
        assertTrue(damengSql.contains("id BIGINT IDENTITY(1,1)"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("is_active BIT"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("DEFAULT SYSDATE"));
        
        // 验证INSERT中的不同数据类型
        assertTrue(damengSql.contains("99.99"));
        assertTrue(damengSql.contains("TRUE"));
        assertTrue(damengSql.contains("FALSE"));
        assertTrue(damengSql.contains("NULL"));
    }

    @Test
    @DisplayName("测试错误场景的处理")
    void testErrorScenarioHandling() {
        // 测试空SQL - 应该抛出异常
        assertThrows(ParseException.class, () -> {
            MySqlHelper.parseMultiStatement("");
        });

        // 测试只有注释的SQL - 应该抛出异常
        assertThrows(ParseException.class, () -> {
            MySqlHelper.parseMultiStatement("-- This is just a comment");
        });
        
        // 测试多个语句分隔
        String multipleSql = """
            CREATE TABLE test1 (id INT);
            CREATE TABLE test2 (id INT);
            """;
        
        List<Statement> multipleStatements = MySqlHelper.parseMultiStatement(multipleSql);
        assertEquals(2, multipleStatements.size());
    }

    @Test
    @DisplayName("测试特殊字符和转义的处理")
    void testSpecialCharactersAndEscaping() {
        String mysqlSql = """
            CREATE TABLE `special_chars` (
                `field-with-dash` VARCHAR(100),
                `field with space` VARCHAR(100),
                `field.with.dot` VARCHAR(100),
                `field_with_underscore` VARCHAR(100)
            );
            
            INSERT INTO `special_chars` VALUES
            ('Value with ''single quotes''', 'Value with "double quotes"', 'Normal value', 'Another normal value');
            """;
        
        List<Statement> statements = MySqlHelper.parseMultiStatement(mysqlSql);
        assertEquals(2, statements.size());
        
        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            result.append(generator.generate(statement)).append("\n");
        }
        
        String damengSql = result.toString();
        
        // 验证特殊字符字段名的转换
        assertTrue(damengSql.contains("\"field-with-dash\""));
        assertTrue(damengSql.contains("\"field with space\""));
        assertTrue(damengSql.contains("\"field.with.dot\""));
        assertTrue(damengSql.contains("field_with_underscore"));  // 普通标识符不需要双引号
        
        // 验证字符串值的保持
        assertTrue(damengSql.contains("'Value with ''single quotes'''"));
        assertTrue(damengSql.contains("'Value with \"double quotes\"'"));
    }

    @Test
    @DisplayName("测试大型复杂SQL的性能和正确性")
    void testLargeComplexSqlPerformance() {
        StringBuilder largeSql = new StringBuilder();
        
        // 创建一个包含多个表和INSERT语句的大型SQL
        largeSql.append("CREATE TABLE `users` (`id` INT AUTO_INCREMENT PRIMARY KEY, `name` VARCHAR(100));\n");
        largeSql.append("CREATE TABLE `orders` (`id` INT AUTO_INCREMENT PRIMARY KEY, `user_id` INT, `status` VARCHAR(50));\n");
        
        // 添加大量INSERT语句
        largeSql.append("INSERT INTO `users` (`name`) VALUES ");
        for (int i = 1; i <= 100; i++) {
            if (i > 1) largeSql.append(", ");
            largeSql.append("('User ").append(i).append("')");
        }
        largeSql.append(";\n");
        
        largeSql.append("INSERT INTO `orders` (`user_id`, `status`) VALUES ");
        for (int i = 1; i <= 50; i++) {
            if (i > 1) largeSql.append(", ");
            largeSql.append("(").append(i).append(", 'pending')");
        }
        largeSql.append(";\n");
        
        long startTime = System.currentTimeMillis();
        List<Statement> statements = MySqlHelper.parseMultiStatement(largeSql.toString());
        long parseTime = System.currentTimeMillis() - startTime;
        
        assertEquals(4, statements.size()); // 2 CREATE + 2 INSERT
        
        startTime = System.currentTimeMillis();
        StringBuilder result = new StringBuilder();
        for (Statement statement : statements) {
            result.append(generator.generate(statement)).append("\n");
        }
        long generateTime = System.currentTimeMillis() - startTime;
        
        String damengSql = result.toString();
        
        // 根据达梦官方文档修正后，验证转换结果
        assertTrue(damengSql.contains("CREATE TABLE users"));   // 普通标识符不需要双引号
        assertTrue(damengSql.contains("CREATE TABLE orders"));  // 普通标识符不需要双引号
        assertTrue(damengSql.contains("('User 1')"));
        assertTrue(damengSql.contains("('User 100')"));
        assertTrue(damengSql.contains("(1, 'pending')"));
        assertTrue(damengSql.contains("(50, 'pending')"));
        
        // 性能断言（这些值可以根据实际性能调整）
        assertTrue(parseTime < 10000, "Parsing should complete within 10 seconds, took: " + parseTime + "ms");
        assertTrue(generateTime < 5000, "Generation should complete within 5 seconds, took: " + generateTime + "ms");
    }
}
