package com.xylink.sqltranspiler.integration.conversion.shentong;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import com.xylink.sqltranspiler.shared.base.BaseShentongConversionTest;

/**
 * 神通数据库转换集成测试
 * 
 * 验证完整的MySQL到神通数据库转换流程
 * 包括复杂SQL语句、批量转换、实际应用场景等
 */
@DisplayName("神通数据库转换集成测试")
public class ShentongConversionIntegrationTest extends BaseShentongConversionTest {

    @Test
    @DisplayName("完整数据库架构转换测试")
    void testCompleteSchemaConversion() throws Exception {
        String mysqlSql = """
            -- 创建数据库
            CREATE DATABASE ecommerce DEFAULT CHARSET=utf8mb4;
            USE ecommerce;
            
            -- 用户表
            CREATE TABLE users (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
                username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
                email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱',
                password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
                first_name VARCHAR(50) COMMENT '名',
                last_name VARCHAR(50) COMMENT '姓',
                phone VARCHAR(20) COMMENT '电话',
                status TINYINT(1) DEFAULT 1 COMMENT '状态：1-活跃，0-禁用',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                INDEX idx_username (username),
                INDEX idx_email (email),
                INDEX idx_status_created (status, created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
            
            -- 商品分类表
            CREATE TABLE categories (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '分类ID',
                name VARCHAR(100) NOT NULL COMMENT '分类名称',
                parent_id INT DEFAULT NULL COMMENT '父分类ID',
                description TEXT COMMENT '分类描述',
                sort_order INT DEFAULT 0 COMMENT '排序',
                is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
                INDEX idx_parent_id (parent_id),
                INDEX idx_sort_order (sort_order)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品分类表';
            
            -- 商品表
            CREATE TABLE products (
                id INT AUTO_INCREMENT PRIMARY KEY COMMENT '商品ID',
                category_id INT NOT NULL COMMENT '分类ID',
                name VARCHAR(200) NOT NULL COMMENT '商品名称',
                description TEXT COMMENT '商品描述',
                price DECIMAL(10,2) NOT NULL COMMENT '价格',
                stock_quantity INT DEFAULT 0 COMMENT '库存数量',
                sku VARCHAR(100) UNIQUE COMMENT 'SKU编码',
                weight DECIMAL(8,3) COMMENT '重量(kg)',
                dimensions VARCHAR(100) COMMENT '尺寸',
                is_active TINYINT(1) DEFAULT 1 COMMENT '是否上架',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
                INDEX idx_category_id (category_id),
                INDEX idx_sku (sku),
                INDEX idx_price (price),
                INDEX idx_stock (stock_quantity),
                INDEX idx_active_created (is_active, created_at)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        assertBasicConversionRequirements(shentongSql);
        assertShentongStandardCompliance(shentongSql);
        
        // 验证数据库创建
        assertTrue(shentongSql.contains("CREATE DATABASE"), "应包含数据库创建");
        assertTrue(shentongSql.contains("CHARACTER SET UTF8"), "应转换字符集");
        
        // 验证表结构转换
        assertTrue(shentongSql.contains("\"users\""), "用户表名应使用双引号");
        assertTrue(shentongSql.contains("\"categories\""), "分类表名应使用双引号");
        assertTrue(shentongSql.contains("\"products\""), "商品表名应使用双引号");
        
        // 验证AUTO_INCREMENT转换
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应支持AUTO_INCREMENT");
        
        // 验证约束转换
        assertTrue(shentongSql.contains("PRIMARY KEY"), "应支持主键");
        assertTrue(shentongSql.contains("FOREIGN KEY"), "应支持外键");
        assertTrue(shentongSql.contains("UNIQUE"), "应支持唯一约束");
        assertTrue(shentongSql.contains("NOT NULL"), "应支持非空约束");
        
        // 验证索引转换
        assertTrue(shentongSql.contains("INDEX"), "应支持索引");
        
        // 验证数据类型转换
        assertTrue(shentongSql.contains("BIT"), "TINYINT(1)应转换为BIT");
        assertTrue(shentongSql.contains("VARCHAR"), "应支持VARCHAR");
        assertTrue(shentongSql.contains("TEXT"), "应支持TEXT");
        assertTrue(shentongSql.contains("DECIMAL"), "应支持DECIMAL");
        assertTrue(shentongSql.contains("TIMESTAMP"), "应支持TIMESTAMP");
        
        // 验证不支持的语法被移除
        assertFalse(shentongSql.contains("ENGINE=InnoDB"), "应移除存储引擎");
        assertFalse(shentongSql.contains("utf8mb4"), "应转换字符集");
    }

    @Test
    @DisplayName("复杂查询转换测试")
    void testComplexQueryConversion() throws Exception {
        String mysqlSql = """
            -- 复杂的电商统计查询
            SELECT 
                c.name as category_name,
                COUNT(DISTINCT p.id) as product_count,
                COUNT(DISTINCT o.id) as order_count,
                SUM(oi.quantity * oi.price) as total_revenue,
                AVG(p.price) as avg_product_price,
                MAX(o.created_at) as latest_order,
                CASE 
                    WHEN SUM(oi.quantity * oi.price) > 10000 THEN 'High'
                    WHEN SUM(oi.quantity * oi.price) > 5000 THEN 'Medium'
                    ELSE 'Low'
                END as revenue_level,
                IFNULL(AVG(r.rating), 0) as avg_rating,
                CONCAT(c.name, ' (', COUNT(DISTINCT p.id), ' products)') as category_summary
            FROM categories c
            LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
            LEFT JOIN order_items oi ON p.id = oi.product_id
            LEFT JOIN orders o ON oi.order_id = o.id AND o.status = 'completed'
            LEFT JOIN reviews r ON p.id = r.product_id AND r.is_approved = 1
            WHERE c.is_active = 1
                AND c.created_at >= DATE_SUB(NOW(), INTERVAL 1 YEAR)
            GROUP BY c.id, c.name
            HAVING COUNT(DISTINCT p.id) > 0
            ORDER BY total_revenue DESC, category_name ASC
            LIMIT 20;
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        assertBasicConversionRequirements(shentongSql);
        
        // 验证复杂查询元素
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT");
        assertTrue(shentongSql.contains("COUNT(DISTINCT"), "应支持COUNT DISTINCT");
        assertTrue(shentongSql.contains("SUM"), "应支持SUM");
        assertTrue(shentongSql.contains("AVG"), "应支持AVG");
        assertTrue(shentongSql.contains("MAX"), "应支持MAX");
        assertTrue(shentongSql.contains("CASE"), "应支持CASE语句");
        assertTrue(shentongSql.contains("WHEN"), "应支持WHEN");
        assertTrue(shentongSql.contains("ELSE"), "应支持ELSE");
        assertTrue(shentongSql.contains("END"), "应支持END");
        assertTrue(shentongSql.contains("IFNULL"), "应支持IFNULL");
        assertTrue(shentongSql.contains("CONCAT"), "应支持CONCAT");
        assertTrue(shentongSql.contains("LEFT JOIN"), "应支持LEFT JOIN");
        assertTrue(shentongSql.contains("GROUP BY"), "应支持GROUP BY");
        assertTrue(shentongSql.contains("HAVING"), "应支持HAVING");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY");
        
        // 验证函数转换 - 基于神通官方文档，NOW()应被转换为SYSDATE
        assertTrue(shentongSql.contains("SYSDATE") || shentongSql.contains("CURRENT_TIMESTAMP") || shentongSql.contains("NOW"),
                   "NOW()应被正确转换为SYSDATE（神通官方文档标准）");
    }

    @Test
    @DisplayName("电商订单系统转换测试")
    void testEcommerceOrderSystemConversion() throws Exception {
        String mysqlSql = """
            CREATE TABLE `products` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `name` VARCHAR(255) NOT NULL,
                `price` DECIMAL(10,2) NOT NULL,
                `stock` INT DEFAULT 0,
                `category_id` INT,
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

            CREATE TABLE `orders` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `user_id` INT NOT NULL,
                `total_amount` DECIMAL(10,2) NOT NULL,
                `status` ENUM('pending', 'paid', 'shipped', 'delivered') DEFAULT 'pending',
                `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (`user_id`) REFERENCES `users`(`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

            INSERT INTO `products` (`name`, `price`, `stock`, `category_id`) VALUES
            ('iPhone 15', 999.99, 50, 1),
            ('MacBook Pro', 1999.99, 20, 2),
            ('AirPods', 199.99, 100, 3);

            INSERT INTO `orders` (`user_id`, `total_amount`, `status`) VALUES
            (1, 999.99, 'pending'),
            (2, 1999.99, 'paid');
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        assertBasicConversionRequirements(shentongSql);

        // 验证表结构转换（根据神通官方文档）
        assertTrue(shentongSql.contains("\"products\""), "应包含products表");
        assertTrue(shentongSql.contains("\"orders\""), "应包含orders表");
        assertTrue(shentongSql.contains("AUTO_INCREMENT"), "应支持AUTO_INCREMENT");
        assertTrue(shentongSql.contains("DECIMAL(10,2)"), "应保留DECIMAL类型");

        // 验证ENUM转换（根据神通官方文档，转换为VARCHAR + CHECK约束）
        assertTrue(shentongSql.contains("VARCHAR") && (shentongSql.contains("CHECK") || shentongSql.contains("status")),
                  "应转换ENUM类型为VARCHAR");

        // 验证INSERT语句
        assertTrue(shentongSql.contains("INSERT INTO"), "应包含INSERT语句");
        assertTrue(shentongSql.contains("'iPhone 15'"), "应包含产品数据");
        assertTrue(shentongSql.contains("999.99"), "应包含价格数据");

        // 验证字符集转换
        assertTrue(shentongSql.contains("CHARACTER SET UTF8"), "应转换字符集");
    }

    @Test
    @DisplayName("数据库和索引管理转换测试")
    void testDatabaseAndIndexManagementConversion() throws Exception {
        String mysqlSql = """
            CREATE DATABASE `test_db` DEFAULT CHARSET=utf8mb4;

            USE `test_db`;

            CREATE INDEX `idx_user_email` ON `users` (`email`);
            CREATE UNIQUE INDEX `idx_user_username` ON `users` (`username`);

            DROP INDEX `idx_old_index` ON `users`;
            DROP TABLE IF EXISTS `temp_table`;
            """;

        String shentongSql = convertMySqlToShentong(mysqlSql);

        assertBasicConversionRequirements(shentongSql);

        // 验证数据库操作（根据神通官方文档）
        assertTrue(shentongSql.contains("CREATE DATABASE") || shentongSql.contains("CREATE SCHEMA"),
                  "应支持CREATE DATABASE或CREATE SCHEMA");

        // 验证索引操作
        assertTrue(shentongSql.contains("CREATE INDEX"), "应支持CREATE INDEX");
        assertTrue(shentongSql.contains("CREATE UNIQUE INDEX"), "应支持CREATE UNIQUE INDEX");

        // 验证DROP操作
        assertTrue(shentongSql.contains("DROP TABLE"), "应支持DROP TABLE");

        // 验证标识符转换（根据神通官方文档，使用双引号）
        assertFalse(shentongSql.contains("`"), "不应包含反引号");
        assertTrue(shentongSql.contains("\"test_db\""), "数据库名应使用双引号");
        assertTrue(shentongSql.contains("\"users\""), "表名应使用双引号");
    }

    @Test
    @DisplayName("批量数据操作转换测试")
    void testBatchDataOperationConversion() throws Exception {
        String mysqlSql = """
            -- 批量插入用户数据
            INSERT INTO users (username, email, password_hash, first_name, last_name, status) VALUES
            ('john_doe', '<EMAIL>', 'hash1', 'John', 'Doe', 1),
            ('jane_smith', '<EMAIL>', 'hash2', 'Jane', 'Smith', 1),
            ('bob_wilson', '<EMAIL>', 'hash3', 'Bob', 'Wilson', 1);
            
            -- 批量更新商品价格
            UPDATE products 
            SET price = price * 1.1, 
                updated_at = CURRENT_TIMESTAMP
            WHERE category_id IN (
                SELECT id FROM categories WHERE name IN ('Electronics', 'Books', 'Clothing')
            );
            
            -- 批量删除过期订单
            DELETE FROM orders 
            WHERE status = 'pending' 
                AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
            
            -- 复杂的数据统计更新
            UPDATE categories c
            SET c.product_count = (
                SELECT COUNT(*) 
                FROM products p 
                WHERE p.category_id = c.id AND p.is_active = 1
            ),
            c.updated_at = CURRENT_TIMESTAMP
            WHERE c.is_active = 1;
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        assertBasicConversionRequirements(shentongSql);
        
        // 验证批量操作支持
        assertTrue(shentongSql.contains("INSERT INTO"), "应支持INSERT");
        assertTrue(shentongSql.contains("VALUES"), "应支持VALUES");
        assertTrue(shentongSql.contains("UPDATE"), "应支持UPDATE");
        assertTrue(shentongSql.contains("SET"), "应支持SET");
        assertTrue(shentongSql.contains("DELETE FROM"), "应支持DELETE");
        assertTrue(shentongSql.contains("WHERE"), "应支持WHERE");
        assertTrue(shentongSql.contains("IN"), "应支持IN操作符");
        assertTrue(shentongSql.contains("SELECT"), "应支持子查询");
        
        // 验证函数转换
        assertTrue(shentongSql.contains("CURRENT_TIMESTAMP"), "应支持CURRENT_TIMESTAMP");
        assertTrue(shentongSql.contains("COUNT(*)"), "应支持COUNT(*)");
    }

    @Test
    @DisplayName("事务处理转换测试")
    void testTransactionProcessingConversion() throws Exception {
        String mysqlSql = """
            -- 完整的订单处理事务
            START TRANSACTION;
            
            -- 创建订单
            INSERT INTO orders (user_id, total_amount, status, created_at) 
            VALUES (1, 299.99, 'pending', CURRENT_TIMESTAMP);
            
            SET @order_id = LAST_INSERT_ID();
            
            -- 添加订单项
            INSERT INTO order_items (order_id, product_id, quantity, price) VALUES
            (@order_id, 101, 2, 99.99),
            (@order_id, 102, 1, 99.99);
            
            -- 更新库存
            UPDATE products 
            SET stock_quantity = stock_quantity - 2,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = 101;
            
            UPDATE products 
            SET stock_quantity = stock_quantity - 1,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = 102;
            
            -- 检查库存是否足够
            SELECT COUNT(*) as insufficient_stock_count
            FROM products
            WHERE id IN (101, 102) AND stock_quantity < 0;

            -- 创建保存点
            SAVEPOINT before_update;

            -- 更新订单状态为已确认
            UPDATE orders
            SET status = 'confirmed',
                updated_at = CURRENT_TIMESTAMP
            WHERE id = @order_id;

            -- 示例：回滚到保存点（用于测试ROLLBACK支持）
            ROLLBACK TO SAVEPOINT before_update;

            -- 重新更新订单状态
            UPDATE orders
            SET status = 'confirmed',
                updated_at = CURRENT_TIMESTAMP
            WHERE id = @order_id;

            -- 提交事务
            COMMIT;
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);

        assertBasicConversionRequirements(shentongSql);

        // 验证事务支持
        assertTrue(shentongSql.contains("START TRANSACTION") || shentongSql.contains("BEGIN"), 
                   "应支持事务开始");
        assertTrue(shentongSql.contains("INSERT INTO"), "应支持INSERT");
        assertTrue(shentongSql.contains("LAST_INSERT_ID"), "应支持LAST_INSERT_ID");
        assertTrue(shentongSql.contains("UPDATE"), "应支持UPDATE");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT");
        assertTrue(shentongSql.contains("COMMIT"), "应支持COMMIT");
        assertTrue(shentongSql.contains("ROLLBACK"), "应支持ROLLBACK");
        
        // 验证变量和控制流（如果支持）
        if (shentongSql.contains("@")) {
            assertTrue(shentongSql.contains("@order_id"), "应支持变量");
        }
    }

    @Test
    @DisplayName("性能优化查询转换测试")
    void testPerformanceOptimizedQueryConversion() throws Exception {
        String mysqlSql = """
            -- MySQL性能优化查询，使用LIMIT进行分页
            SELECT
                p.id,
                p.name,
                p.price,
                c.name as category_name,
                (SELECT COUNT(*) FROM order_items oi WHERE oi.product_id = p.id) as sales_count,
                (SELECT AVG(rating) FROM reviews r WHERE r.product_id = p.id) as avg_rating
            FROM products p
            INNER JOIN categories c ON p.category_id = c.id
            WHERE p.is_active = 1
                AND p.stock_quantity > 0
                AND p.price BETWEEN 10.00 AND 1000.00
            ORDER BY p.created_at DESC, p.id DESC
            LIMIT 30 OFFSET 20;
            
            -- 使用索引提示的查询优化
            SELECT /*+ USE_INDEX(products, idx_category_price) */
                p.name,
                p.price,
                c.name as category_name
            FROM products p
            FORCE INDEX (idx_category_price)
            INNER JOIN categories c ON p.category_id = c.id
            WHERE p.category_id = 1
                AND p.price > 100
            ORDER BY p.price DESC;
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);
        
        assertBasicConversionRequirements(shentongSql);
        
        // 验证分页查询支持
        assertTrue(shentongSql.contains("ROWNUM"), "应支持ROWNUM分页");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT");
        assertTrue(shentongSql.contains("FROM"), "应支持FROM");
        assertTrue(shentongSql.contains("WHERE"), "应支持WHERE");
        assertTrue(shentongSql.contains("ORDER BY"), "应支持ORDER BY");
        assertTrue(shentongSql.contains("INNER JOIN"), "应支持INNER JOIN");
        assertTrue(shentongSql.contains("BETWEEN"), "应支持BETWEEN");
        
        // 验证子查询支持
        assertTrue(shentongSql.contains("COUNT(*)"), "应支持COUNT(*)");
        assertTrue(shentongSql.contains("AVG"), "应支持AVG");
        
        // 索引提示可能不被支持，应被移除或转换
        // 这里主要验证基本查询结构正确
    }

    @Test
    @DisplayName("数据迁移场景转换测试")
    void testDataMigrationScenarioConversion() throws Exception {
        String mysqlSql = """
            -- 数据迁移：从旧表结构迁移到新表结构
            
            -- 创建临时表
            CREATE TEMPORARY TABLE temp_user_migration AS
            SELECT 
                id,
                CONCAT(first_name, ' ', last_name) as full_name,
                email,
                CASE 
                    WHEN status = 'A' THEN 1
                    WHEN status = 'I' THEN 0
                    ELSE 1
                END as is_active,
                created_date as created_at
            FROM old_users
            WHERE created_date >= '2020-01-01';
            
            -- 插入迁移数据
            INSERT INTO users (id, username, email, first_name, last_name, status, created_at)
            SELECT 
                t.id,
                LOWER(REPLACE(t.full_name, ' ', '_')) as username,
                t.email,
                SUBSTRING_INDEX(t.full_name, ' ', 1) as first_name,
                SUBSTRING_INDEX(t.full_name, ' ', -1) as last_name,
                t.is_active,
                t.created_at
            FROM temp_user_migration t
            WHERE NOT EXISTS (
                SELECT 1 FROM users u WHERE u.email = t.email
            );
            
            -- 更新统计信息
            UPDATE categories c
            SET c.product_count = (
                SELECT COUNT(*) 
                FROM products p 
                WHERE p.category_id = c.id
            );
            
            -- 清理临时数据
            DROP TEMPORARY TABLE temp_user_migration;
            """;
        
        String shentongSql = convertMySqlToShentong(mysqlSql);

        assertBasicConversionRequirements(shentongSql);

        // 验证数据迁移操作支持
        assertTrue(shentongSql.contains("CREATE"), "应支持CREATE");
        assertTrue(shentongSql.contains("SELECT"), "应支持SELECT");
        assertTrue(shentongSql.contains("INSERT"), "应支持INSERT");
        assertTrue(shentongSql.contains("UPDATE"), "应支持UPDATE");
        assertTrue(shentongSql.contains("DROP"), "应支持DROP");
        
        // 验证字符串函数支持
        assertTrue(shentongSql.contains("CONCAT"), "应支持CONCAT");
        assertTrue(shentongSql.contains("CASE"), "应支持CASE");
        
        // 验证子查询和EXISTS支持
        assertTrue(shentongSql.contains("NOT EXISTS"), "应支持NOT EXISTS");
        assertTrue(shentongSql.contains("COUNT(*)"), "应支持COUNT(*)");
        
        // 临时表可能需要特殊处理
        if (shentongSql.contains("TEMPORARY")) {
            assertTrue(shentongSql.contains("TEMPORARY"), "如果支持应包含TEMPORARY");
        }
    }
}
