package com.xylink.sqltranspiler.integration;

import static org.junit.jupiter.api.Assertions.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;

import com.xylink.sqltranspiler.core.dialects.DamengDialect;
import com.xylink.sqltranspiler.core.dialects.KingbaseDialect;
import com.xylink.sqltranspiler.core.dialects.SqlDialect;
import com.xylink.sqltranspiler.core.functions.FunctionMapper;
import com.xylink.sqltranspiler.infrastructure.util.SqlConversionUtils;

/**
 * 增强的函数映射集成测试
 * 
 * 遵循 .augment/rules/rule-db.md：
 * - 基于官方文档的函数映射测试
 * - 验证MySQL函数到各数据库的正确转换
 * - 包含边界情况和错误处理测试
 */
public class EnhancedFunctionMappingTest {
    
    private SqlDialect damengDialect;
    private SqlDialect kingbaseDialect;
    private FunctionMapper functionMapper;
    
    @BeforeEach
    void setUp() {
        damengDialect = new DamengDialect();
        kingbaseDialect = new KingbaseDialect();
        functionMapper = new FunctionMapper();
    }
    
    @Test
    void testDateTimeFunctionMapping(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 基于MySQL 8.4官方文档的日期时间函数
        // https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
        Map<String, String> mysqlFunctions = new HashMap<>();
        mysqlFunctions.put("NOW()", "获取当前日期和时间");
        mysqlFunctions.put("CURDATE()", "获取当前日期");
        mysqlFunctions.put("CURTIME()", "获取当前时间");
        mysqlFunctions.put("SYSDATE()", "获取系统日期时间");
        mysqlFunctions.put("UTC_TIMESTAMP()", "获取UTC时间戳");
        
        // 测试达梦转换
        System.out.println("达梦数据库日期时间函数转换:");
        testFunctionConversion(mysqlFunctions, damengDialect, Map.of(
            "NOW()", "SYSDATE",
            "CURDATE()", "TRUNC(SYSDATE)",
            "CURTIME()", "TO_CHAR(SYSDATE, 'HH24:MI:SS')",
            "SYSDATE()", "SYSDATE",
            "UTC_TIMESTAMP()", "SYS_EXTRACT_UTC(SYSTIMESTAMP)"
        ));
        
        // 测试金仓转换
        System.out.println("\n金仓数据库日期时间函数转换:");
        testFunctionConversion(mysqlFunctions, kingbaseDialect, Map.of(
            "NOW()", "NOW()",
            "CURDATE()", "CURRENT_DATE()",
            "CURTIME()", "CURRENT_TIME()",
            "SYSDATE()", "NOW()",
            "UTC_TIMESTAMP()", "NOW() AT TIME ZONE 'UTC'"
        ));
    }
    
    @Test
    void testStringFunctionMapping(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 基于MySQL 8.4官方文档的字符串函数
        // https://dev.mysql.com/doc/refman/8.4/en/string-functions.html
        Map<String, String> mysqlFunctions = new HashMap<>();
        mysqlFunctions.put("CONCAT(a, b)", "字符串连接");
        mysqlFunctions.put("LENGTH(str)", "字符串长度");
        mysqlFunctions.put("SUBSTRING(str, pos)", "子字符串");
        mysqlFunctions.put("UPPER(str)", "转大写");
        mysqlFunctions.put("LOWER(str)", "转小写");
        mysqlFunctions.put("TRIM(str)", "去除空格");
        
        // 测试达梦转换
        System.out.println("达梦数据库字符串函数转换:");
        testFunctionConversion(mysqlFunctions, damengDialect, Map.of(
            "CONCAT(a, b)", "CONCAT(a, b)",
            "LENGTH(str)", "LENGTH(str)",
            "SUBSTRING(str, pos)", "SUBSTR(str, pos)",
            "UPPER(str)", "UPPER(str)",
            "LOWER(str)", "LOWER(str)",
            "TRIM(str)", "TRIM(str)"
        ));
        
        // 测试金仓转换
        System.out.println("\n金仓数据库字符串函数转换:");
        testFunctionConversion(mysqlFunctions, kingbaseDialect, Map.of(
            "CONCAT(a, b)", "CONCAT(a, b)",
            "LENGTH(str)", "LENGTH(str)",
            "SUBSTRING(str, pos)", "SUBSTRING(str, pos)",
            "UPPER(str)", "UPPER(str)",
            "LOWER(str)", "LOWER(str)",
            "TRIM(str)", "TRIM(str)"
        ));
    }
    
    @Test
    void testNumericFunctionMapping(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 基于MySQL 8.4官方文档的数值函数
        // https://dev.mysql.com/doc/refman/8.4/en/numeric-functions.html
        Map<String, String> mysqlFunctions = new HashMap<>();
        mysqlFunctions.put("ABS(n)", "绝对值");
        mysqlFunctions.put("CEIL(n)", "向上取整");
        mysqlFunctions.put("FLOOR(n)", "向下取整");
        mysqlFunctions.put("ROUND(n)", "四舍五入");
        mysqlFunctions.put("RAND()", "随机数");
        mysqlFunctions.put("MOD(n, m)", "取模");
        
        // 测试达梦转换
        System.out.println("达梦数据库数值函数转换:");
        testFunctionConversion(mysqlFunctions, damengDialect, Map.of(
            "ABS(n)", "ABS(n)",
            "CEIL(n)", "CEIL(n)",
            "FLOOR(n)", "FLOOR(n)",
            "ROUND(n)", "ROUND(n)",
            "RAND()", "RANDOM()",
            "MOD(n, m)", "MOD(n, m)"
        ));
        
        // 测试金仓转换
        System.out.println("\n金仓数据库数值函数转换:");
        testFunctionConversion(mysqlFunctions, kingbaseDialect, Map.of(
            "ABS(n)", "ABS(n)",
            "CEIL(n)", "CEIL(n)",
            "FLOOR(n)", "FLOOR(n)",
            "ROUND(n)", "ROUND(n)",
            "RAND()", "RANDOM()",
            "MOD(n, m)", "MOD(n, m)"
        ));
    }
    
    @Test
    void testConditionalFunctionMapping(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 基于MySQL 8.4官方文档的条件函数
        // https://dev.mysql.com/doc/refman/8.4/en/flow-control-functions.html
        Map<String, String> mysqlFunctions = new HashMap<>();
        mysqlFunctions.put("IF(condition, true_val, false_val)", "条件判断");
        mysqlFunctions.put("IFNULL(expr, alt)", "空值处理");
        mysqlFunctions.put("NULLIF(expr1, expr2)", "空值比较");
        mysqlFunctions.put("COALESCE(expr1, expr2)", "返回第一个非空值");
        
        // 测试FunctionMapper的特殊转换规则
        System.out.println("FunctionMapper特殊转换规则测试:");
        
        // IF函数转换为CASE WHEN
        List<String> ifArgs = Arrays.asList("status = 1", "'Active'", "'Inactive'");
        String ifResult = functionMapper.mapFunction("IF", ifArgs, damengDialect);
        String expectedIf = "CASE WHEN status = 1 THEN 'Active' ELSE 'Inactive' END";
        assertEquals(expectedIf, ifResult, "IF函数应该转换为CASE WHEN语句");
        System.out.println("✅ IF函数转换: " + ifResult);
        
        // IFNULL函数转换
        String ifnullResult = damengDialect.mapFunction("IFNULL", "name", "'Unknown'");
        assertEquals("NVL(name, 'Unknown')", ifnullResult, "IFNULL应该转换为NVL");
        System.out.println("✅ IFNULL函数转换: " + ifnullResult);
    }
    
    @Test
    void testAggregateFunctionMapping(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 基于MySQL 8.4官方文档的聚合函数
        // https://dev.mysql.com/doc/refman/8.4/en/aggregate-functions.html
        Map<String, String> mysqlFunctions = new HashMap<>();
        mysqlFunctions.put("COUNT(*)", "计数");
        mysqlFunctions.put("SUM(column)", "求和");
        mysqlFunctions.put("AVG(column)", "平均值");
        mysqlFunctions.put("MAX(column)", "最大值");
        mysqlFunctions.put("MIN(column)", "最小值");
        mysqlFunctions.put("GROUP_CONCAT(column)", "分组连接");
        
        // 聚合函数在大多数数据库中都是标准的
        System.out.println("聚合函数兼容性测试:");
        for (String function : mysqlFunctions.keySet()) {
            String functionName = function.split("\\(")[0];
            
            // 测试函数支持
            boolean damengSupports = damengDialect.supportsFunction(functionName);
            boolean kingbaseSupports = kingbaseDialect.supportsFunction(functionName);
            
            System.out.printf("%-20s 达梦: %s, 金仓: %s%n", 
                function, damengSupports ? "✅" : "❌", kingbaseSupports ? "✅" : "❌");
        }
    }
    
    @Test
    void testSqlConversionUtilsIntegration(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 测试SqlConversionUtils与方言的集成
        String testSql = "SELECT NOW(), CURDATE(), CONCAT(first_name, last_name) FROM users WHERE LENGTH(email) > 0";
        
        System.out.println("原始SQL: " + testSql);
        
        // 测试达梦转换
        String damengSql = SqlConversionUtils.convertFunctionsWithMapper(testSql, damengDialect);
        System.out.println("达梦转换: " + damengSql);
        
        // 验证函数转换
        assertTrue(damengSql.contains("SYSDATE") || damengSql.contains("CURRENT_TIMESTAMP"), 
            "NOW()应该被转换为达梦兼容函数");
        assertTrue(damengSql.contains("TRUNC") || damengSql.contains("CURRENT_DATE"), 
            "CURDATE()应该被转换为达梦兼容函数");
        
        // 测试金仓转换
        String kingbaseSql = SqlConversionUtils.convertFunctionsWithMapper(testSql, kingbaseDialect);
        System.out.println("金仓转换: " + kingbaseSql);
        
        // 验证函数转换
        assertTrue(kingbaseSql.contains("NOW()") || kingbaseSql.contains("CURRENT_TIMESTAMP"), 
            "NOW()应该被转换为金仓兼容函数");
        assertTrue(kingbaseSql.contains("CURRENT_DATE"), 
            "CURDATE()应该被转换为金仓兼容函数");
    }
    
    @Test
    void testEdgeCasesAndErrorHandling(TestInfo testInfo) {
        System.out.println("\n=== " + testInfo.getDisplayName() + " ===");
        
        // 测试边界情况
        System.out.println("边界情况测试:");
        
        // 空函数名
        String emptyResult = damengDialect.mapFunction("");
        assertEquals("", emptyResult, "空函数名应该返回空字符串");
        System.out.println("✅ 空函数名处理正确");
        
        // null函数名
        String nullResult = damengDialect.mapFunction(null);
        assertEquals("", nullResult, "null函数名应该返回空字符串");
        System.out.println("✅ null函数名处理正确");
        
        // 未知函数
        String unknownResult = damengDialect.mapFunction("UNKNOWN_FUNCTION", "arg1");
        assertEquals("UNKNOWN_FUNCTION(arg1)", unknownResult, "未知函数应该保持原样");
        System.out.println("✅ 未知函数处理正确: " + unknownResult);
        
        // 函数支持检查
        assertFalse(damengDialect.supportsFunction("UNKNOWN_FUNCTION"), 
            "未知函数应该返回不支持");
        assertTrue(damengDialect.supportsFunction("NOW"), 
            "已知函数应该返回支持");
        System.out.println("✅ 函数支持检查正确");
    }
    
    /**
     * 测试函数转换的辅助方法
     */
    private void testFunctionConversion(Map<String, String> mysqlFunctions, 
                                      SqlDialect dialect, 
                                      Map<String, String> expectedMappings) {
        for (Map.Entry<String, String> entry : expectedMappings.entrySet()) {
            String mysqlFunction = entry.getKey();
            String expectedResult = entry.getValue();
            
            // 解析函数名和参数
            String functionName = mysqlFunction.split("\\(")[0];
            String[] args = {};
            
            if (mysqlFunction.contains("(") && mysqlFunction.contains(")")) {
                String argsString = mysqlFunction.substring(
                    mysqlFunction.indexOf("(") + 1, 
                    mysqlFunction.lastIndexOf(")")
                );
                if (!argsString.trim().isEmpty()) {
                    args = argsString.split(",");
                    for (int i = 0; i < args.length; i++) {
                        args[i] = args[i].trim();
                    }
                }
            }
            
            String actualResult = dialect.mapFunction(functionName, args);
            
            System.out.printf("%-25s -> %-30s (期望: %s)%n", 
                mysqlFunction, actualResult, expectedResult);
            
            // 对于复杂的转换，只检查关键部分
            if (expectedResult.contains("CASE WHEN")) {
                assertTrue(actualResult.contains("CASE WHEN"), 
                    "复杂转换应该包含CASE WHEN");
            } else {
                assertTrue(actualResult.contains(expectedResult) || actualResult.equals(expectedResult), 
                    String.format("函数转换失败: %s -> %s (期望包含: %s)", 
                        mysqlFunction, actualResult, expectedResult));
            }
        }
    }
}
