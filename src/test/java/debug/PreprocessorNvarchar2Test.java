package debug;

import com.xylink.sqltranspiler.infrastructure.parser.Preprocessor;
import com.xylink.sqltranspiler.infrastructure.parser.PreprocessingResult;
import org.junit.jupiter.api.Test;

public class PreprocessorNvarchar2Test {
    
    @Test
    public void testNvarchar2Preprocessing() {
        String sql = "CREATE TABLE test (nvarchar_field NVARCHAR2(100));";
        
        System.out.println("Original SQL: " + sql);
        
        PreprocessingResult result = Preprocessor.preprocess(sql, true, "shentong");
        String preprocessedSQL = result.cleanedSql();
        
        System.out.println("Preprocessed SQL: " + preprocessedSQL);
        System.out.println("Contains VARCHAR(100): " + preprocessedSQL.contains("VARCHAR(100)"));
        System.out.println("Contains VARCHAR(10100): " + preprocessedSQL.contains("VARCHAR(10100)"));
        
        // 打印所有日志
        result.logs().forEach(System.out::println);
    }
}
