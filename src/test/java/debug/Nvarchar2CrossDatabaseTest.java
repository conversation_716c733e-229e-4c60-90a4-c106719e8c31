package debug;

import com.xylink.sqltranspiler.infrastructure.parser.Preprocessor;
import com.xylink.sqltranspiler.infrastructure.parser.PreprocessingResult;
import org.junit.jupiter.api.Test;

public class Nvarchar2CrossDatabaseTest {
    
    @Test
    public void testNvarchar2ProcessingAcrossDatabases() {
        String sql = "CREATE TABLE test (nvarchar_field NVARCHAR2(100));";
        
        System.out.println("Original SQL: " + sql);
        
        // 测试神通数据库
        PreprocessingResult shentongResult = Preprocessor.preprocess(sql, true, "shentong");
        System.out.println("Shentong preprocessed: " + shentongResult.cleanedSql());
        System.out.println("Shentong contains VARCHAR(100): " + shentongResult.cleanedSql().contains("VARCHAR(100)"));
        
        // 测试达梦数据库
        PreprocessingResult damengResult = Preprocessor.preprocess(sql, true, "dameng");
        System.out.println("Dameng preprocessed: " + damengResult.cleanedSql());
        System.out.println("Dameng contains NVARCHAR2(100): " + damengResult.cleanedSql().contains("NVARCHAR2(100)"));
        
        // 测试金仓数据库
        PreprocessingResult kingbaseResult = Preprocessor.preprocess(sql, true, "kingbase");
        System.out.println("Kingbase preprocessed: " + kingbaseResult.cleanedSql());
        System.out.println("Kingbase contains NVARCHAR2(100): " + kingbaseResult.cleanedSql().contains("NVARCHAR2(100)"));
    }
}
