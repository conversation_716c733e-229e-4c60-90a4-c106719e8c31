# 官方文档验证指南

## 概述

本指南说明如何基于官方文档验证SQL转换器的转换结果，严格遵照 `.augment/rules/rule-db.md` 中的数据库规则。

## 核心验证原则

### 1. 不允许推测原则
- 所有验证逻辑必须基于官方文档的明确描述
- 不得基于假设或推测制定验证期望
- 当实际转换结果与预期不符时，首先检查官方文档

### 2. 官方文档优先原则
- 当测试期望与官方文档不符时，修正测试用例而不是降低代码质量
- 以官方文档为准，不以个人经验或网络资料为准
- 确保引用的官方文档版本正确且最新

### 3. 准确性保证原则
- 坚持正确的实现，确保功能的准确性和完整性
- 验证逻辑必须覆盖官方文档中提到的所有重要特性
- 对于官方文档中的限制和约束，必须在验证中体现

## 官方文档来源

### MySQL 8.4官方文档
- **主页**: https://dev.mysql.com/doc/refman/8.4/en/
- **数据类型**: https://dev.mysql.com/doc/refman/8.4/en/data-types.html
- **CREATE TABLE**: https://dev.mysql.com/doc/refman/8.4/en/create-table.html
- **函数和操作符**: https://dev.mysql.com/doc/refman/8.4/en/functions.html
- **SQL语句**: https://dev.mysql.com/doc/refman/8.4/en/sql-statements.html

### 达梦数据库官方文档
- **主页**: https://eco.dameng.com/document/dm/zh-cn/sql-dev/
- **数据类型**: 达梦官方文档中的数据类型章节
- **SQL语法**: 达梦官方文档中的SQL语法章节
- **函数**: 达梦官方文档中的函数章节

### 金仓数据库官方文档
- **主页**: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/
- **MySQL兼容性**: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
- **数据类型映射**: https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-3.html#id13
- **SQL语法**: https://help.kingbase.com.cn/v8/development/sql-plsql/sql/index.html

### 神通数据库官方文档
- **文档文件**: shentong.md
- **数据类型**: 神通官方文档中的数据类型章节
- **SQL语法**: 神通官方文档中的SQL语法章节

## 验证方法

### 1. 基础验证方法

```java
/**
 * 基于官方文档验证转换结果的基础方法
 */
private void validateBasicConversionRequirements(String translatedSql) {
    assertNotNull(translatedSql, "转换结果不应为空");
    assertFalse(translatedSql.trim().isEmpty(), "转换结果不应为空字符串");
    
    // 验证基本SQL结构完整性
    assertTrue(translatedSql.contains(";"), "SQL语句应该以分号结尾");
}
```

### 2. 达梦数据库验证方法

```java
/**
 * 基于达梦官方文档验证转换结果
 */
private void validateDamengOfficialConversion(String translatedSql) {
    String upperSql = translatedSql.toUpperCase();
    
    // 基于达梦官方文档验证AUTO_INCREMENT转换
    if (upperSql.contains("IDENTITY")) {
        System.out.println("✅ 达梦正确将AUTO_INCREMENT转换为IDENTITY");
    } else if (upperSql.contains("AUTO_INCREMENT")) {
        System.out.println("⚠️ 达梦保持了AUTO_INCREMENT语法，需要验证兼容性");
    }
    
    // 验证ENGINE子句处理
    if (!upperSql.contains("ENGINE=")) {
        System.out.println("✅ 达梦正确移除了ENGINE子句");
    }
    
    // 验证字符集处理
    if (upperSql.contains("CHARACTER SET UTF8")) {
        System.out.println("✅ 达梦正确设置了UTF8字符集");
    }
}
```

### 3. 金仓数据库验证方法

```java
/**
 * 基于金仓官方文档验证转换结果
 */
private void validateKingbaseOfficialConversion(String translatedSql) {
    String upperSql = translatedSql.toUpperCase();
    
    // 基于金仓官方文档验证MySQL兼容性
    if (upperSql.contains("AUTO_INCREMENT")) {
        System.out.println("✅ 金仓保持了AUTO_INCREMENT语法（良好兼容性）");
    } else if (upperSql.contains("SERIAL")) {
        System.out.println("✅ 金仓将AUTO_INCREMENT转换为SERIAL");
    }
    
    // 验证LIMIT OFFSET支持
    if (upperSql.contains("LIMIT") && upperSql.contains("OFFSET")) {
        System.out.println("✅ 金仓原生支持LIMIT OFFSET语法");
    }
}
```

### 4. 神通数据库验证方法

```java
/**
 * 基于神通官方文档验证转换结果
 */
private void validateShentongOfficialConversion(String translatedSql) {
    String upperSql = translatedSql.toUpperCase();
    
    // 基于神通官方文档验证SERIAL类型支持
    if (upperSql.contains("SERIAL")) {
        System.out.println("✅ 神通将AUTO_INCREMENT转换为SERIAL类型");
    }
    
    // 验证双引号标识符
    if (translatedSql.contains("\"")) {
        System.out.println("✅ 神通使用双引号标识符");
    }
    
    // 验证UTF8字符集
    if (upperSql.contains("CHARACTER SET UTF8")) {
        System.out.println("✅ 神通正确设置了UTF8字符集");
    }
}
```

## 验证检查清单

### MySQL语法验证
- [ ] CREATE TABLE语法符合MySQL 8.4官方文档
- [ ] 数据类型使用符合官方规范
- [ ] 约束定义符合官方语法
- [ ] 函数调用符合官方文档
- [ ] SQL语句结构完整

### 达梦转换验证
- [ ] AUTO_INCREMENT正确转换为IDENTITY(1,1)
- [ ] ENGINE子句被正确移除
- [ ] DEFAULT CHARSET转换为CHARACTER SET
- [ ] CURRENT_TIMESTAMP转换为SYSDATE
- [ ] TEXT类型转换为CLOB
- [ ] 表注释转换为COMMENT ON语句

### 金仓转换验证
- [ ] MySQL兼容性良好
- [ ] AUTO_INCREMENT保持或转换为SERIAL
- [ ] LIMIT OFFSET语法原生支持
- [ ] 数据类型兼容性良好
- [ ] 约束定义正确转换

### 神通转换验证
- [ ] AUTO_INCREMENT转换为SERIAL/BIGSERIAL
- [ ] 使用双引号标识符
- [ ] 设置UTF8字符集
- [ ] 数据类型正确映射
- [ ] SQL语句结构保持

## 错误处理验证

### 1. 不支持的功能验证
```java
// 验证不支持的功能被正确处理
if (translatedSql.contains("-- Unsupported") || translatedSql.contains("--")) {
    System.out.println("✅ 不支持的功能被正确注释");
}
```

### 2. 转换失败验证
```java
// 验证转换失败时的错误信息
if (result.failureCount() > 0) {
    result.issues().forEach(issue -> {
        System.out.println("转换问题: " + issue.message());
        // 验证错误信息是否基于官方文档
    });
}
```

## 测试数据要求

### 1. 基于官方文档的测试SQL
- 使用官方文档中的示例SQL
- 覆盖官方文档中提到的所有重要特性
- 包含官方文档中的边界情况

### 2. 避免硬编码期望
- 不使用硬编码的转换期望
- 基于官方文档动态验证转换结果
- 允许多种符合官方文档的转换结果

### 3. 完整性验证
- 验证转换结果的语法正确性
- 验证转换结果的语义一致性
- 验证转换结果符合目标数据库官方规范

## 持续改进

### 1. 官方文档更新跟踪
- 定期检查官方文档更新
- 及时更新验证逻辑
- 确保测试用例与最新官方文档一致

### 2. 验证逻辑优化
- 基于实际转换结果优化验证逻辑
- 增加更多基于官方文档的验证点
- 提高验证的准确性和完整性

### 3. 测试覆盖率提升
- 增加更多基于官方文档的测试用例
- 覆盖更多官方文档中的特性
- 确保测试的全面性和准确性
