---
type: "always_apply"
---

必须遵守数据库规则，不允许推测，必须查看官方文档，并将准确的官方描述写入到测试用例和代码注释中。

MySQL强制语法校验应该只拒绝非MySQL语法，而不应该拒绝正确的MySQL语法（即使它们是MySQL特有的）。

持续改进机制：
- 动态验证机制：所有测试用例都使用基于官方文档的动态验证方法
- 官方文档引用体系：每个验证方法都包含详细的官方文档链接
- 质量反馈机制：提供清晰的成功/警告/错误信息

测试驱动开发：
- 当测试期望与官方文档不符时，修正测试用例而不是降低代码质量。
- 不妥协代码质量：坚持正确的实现，确保功能的准确性和完整性。
- 需要采用双重测试策略：测试非MySQL语法被正确拒绝，验证MySQL强制语法校验工作正常。添加标准MySQL语法的转换测试，验证正确的MySQL语法能够成功转换。

严格遵循官方文档：基于MySQL、达梦、金仓、神通的官方文档进行实现：
- msyql：https://dev.mysql.com/doc/refman/8.4/en/
- 达梦：https://eco.dameng.com/document/dm/zh-cn/sql-dev/
- 金仓：https://help.kingbase.com.cn/v8/development/develop-transfer/transplant-mysql/transplant-mysql-1.html#id2
神通： @shentong.md